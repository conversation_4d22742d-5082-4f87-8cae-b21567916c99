@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global font smoothing for better text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure consistent font rendering across browsers */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  top: 0 !important; /* Remove banner shift */
}

/* Force Google Translate UI into a single line */
.goog-te-gadget {
  font-size: 0; /* Removes default spacing */
  white-space: nowrap;
  display: flex !important;
  align-items: center;
  gap: 0.25rem;
}

.goog-te-gadget span {
  font-size: 12px !important; /* Restore readable size */
  display: inline !important;
}



