import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '');

  console.log('Loaded environment variables:', {
    VITE_COGNITO_DOMAIN: env.VITE_COGNITO_DOMAIN,
    VITE_COGNITO_CLIENT_ID: env.VITE_COGNITO_CLIENT_ID,
    VITE_COGNITO_REDIRECT_URI: env.VITE_COGNITO_REDIRECT_URI,
    BASE_URL: env.BASE_URL,
  });

  return {
    base: env.VITE_BASE_PATH,
    plugins: [react()],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    server: {
      proxy: {
        '/edu-fund-services': {
          target: 'https://ashburn-rzs.icamxperts.com',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    define: {
      // Make env variables available globally in the app
      'import.meta.env.VITE_COGNITO_DOMAIN': JSON.stringify(env.VITE_COGNITO_DOMAIN),
      'import.meta.env.VITE_COGNITO_CLIENT_ID': JSON.stringify(env.VITE_COGNITO_CLIENT_ID),
      'import.meta.env.VITE_COGNITO_REDIRECT_URI': JSON.stringify(env.VITE_COGNITO_REDIRECT_URI),
      'import.meta.env.VITE_COGNITO_SIGNOUT_URI': JSON.stringify(env.VITE_COGNITO_SIGNOUT_URI),
      'import.meta.env.VITE_COGNITO_RESPONSE_TYPE': JSON.stringify(env.VITE_COGNITO_RESPONSE_TYPE),
      'import.meta.env.VITE_COGNITO_SCOPE': JSON.stringify(env.VITE_COGNITO_SCOPE),
      'import.meta.env.VITE_COGNITO_AUTHORITY': JSON.stringify(env.VITE_COGNITO_AUTHORITY || 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_xr0yffogk'),
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
  };
});
