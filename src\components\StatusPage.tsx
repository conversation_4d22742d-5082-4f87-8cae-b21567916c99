import React from 'react';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowLeft,
  GraduationCap,
  ChevronLeft
} from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';

interface StatusPageProps {
  type: 'success' | 'error' | 'warning';
  title: string;
  message: string;
  actionText?: string;
  onAction?: () => void;
  secondaryActionText?: string;
  onSecondaryAction?: () => void;
  backUrl?: string;
  showHeader?: boolean;
}

const StatusPage: React.FC<StatusPageProps> = ({
  type,
  title,
  message,
  actionText = 'Go Back',
  onAction,
  secondaryActionText,
  onSecondaryAction,
  backUrl,
  showHeader = true
}) => {
  const navigate = useNavigate();

  const handleAction = () => {
    if (onAction) {
      onAction();
    } else if (backUrl) {
      navigate(backUrl);
    } else {
      navigate(-1);
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case 'error':
        return <XCircle className="h-16 w-16 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-16 w-16 text-yellow-500" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50',
          button: 'bg-green-600 hover:bg-green-700',
          text: 'text-green-800'
        };
      case 'error':
        return {
          bg: 'bg-red-50',
          button: 'bg-red-600 hover:bg-red-700',
          text: 'text-red-800'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50',
          button: 'bg-yellow-600 hover:bg-yellow-700',
          text: 'text-yellow-800'
        };
    }
  };

  const colors = getColors();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header - conditionally rendered */}
      {showHeader && (
        <header className="bg-white shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <button
                  onClick={() => navigate(-1)}
                  className="mr-4 p-2 rounded-full hover:bg-gray-100 text-gray-600"
                  aria-label="Go back"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <Link to="/" className="flex items-center">
                  <GraduationCap className="h-8 w-8 text-blue-600" />
                  <span className="ml-2 text-xl font-bold text-gray-900">
                    EDU-FUND
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Main Content */}
      <div className={`flex-grow ${colors.bg} flex items-center justify-center p-4`}>
        <div className="max-w-md w-full text-center">
          <div className="mb-6 flex justify-center">
            {getIcon()}
          </div>

          <h1 className={`text-2xl font-bold ${colors.text} mb-4`}>
            {title}
          </h1>

          <div className="text-gray-600 mb-8 leading-relaxed whitespace-pre-line">
            {message}
          </div>

          <div className="flex justify-center gap-4">
            {onSecondaryAction && secondaryActionText && (
              <button
                onClick={onSecondaryAction}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {secondaryActionText}
              </button>
            )}
            <button
              onClick={handleAction}
              className={`${colors.button} text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center transition-colors`}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span>{actionText}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusPage;
