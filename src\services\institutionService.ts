import api from './api';
import axios from "axios";
import { backendUrl } from "../config/api";



const apiClient = axios.create({
  baseURL: backendUrl,
  responseType: "json",
});

const cognitoConfig = {
  domain: 'https://us-east-1xr0yffogk.auth.us-east-1.amazoncognito.com',
  clientId: '42bvu1jm3cgnb91g659idno64v',
  redirectUri: window.location.origin + '${import.meta.env.VITE_BASE_PATH}/institution-dashboard',
  tokenEndpoint: 'https://us-east-1xr0yffogk.auth.us-east-1.amazoncognito.com/oauth2/token',
  region: 'us-east-1'
};

// Function to check if a token is expired


// Store the current tokens
interface TokenData {
  idToken: string;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

const currentTokens: TokenData | null = null;

// Add a token initialization lock
let isInitializingTokens = false;
let tokenInitializationPromise: Promise<void> | null = null;



// Get new tokens using the refresh token with retry logic
const refreshTokens = async (refreshToken: string): Promise<TokenData> => {
  try {
    console.log('Starting token refresh...');

    // Validate refresh token presence only (don't validate JWT format)
    if (!refreshToken || refreshToken.trim() === '') {
      throw new Error('Invalid refresh token - token is empty');
    }

    console.log('Refresh token present. Proceeding to refresh...');

    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('client_id', cognitoConfig.clientId);
    params.append('refresh_token', refreshToken);

    // Log request metadata (not actual token)
    console.log('Token refresh request:', {
      url: cognitoConfig.tokenEndpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      params: {
        grant_type: params.get('grant_type'),
        client_id: params.get('client_id'),
        has_refresh_token: !!params.get('refresh_token')
      }
    });

    let retryCount = 0;
    const maxRetries = 2;
    let lastError = null;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`Retry attempt ${retryCount}/${maxRetries}...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }

        const response = await axios.post(cognitoConfig.tokenEndpoint, params, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });

        if (!response.data) {
          throw new Error('Empty response from token endpoint');
        }

        console.log('Token refresh response structure:', {
          has_access_token: !!response.data.access_token,
          has_id_token: !!response.data.id_token,
          has_refresh_token: !!response.data.refresh_token,
          token_type: response.data.token_type,
          has_expires_in: !!response.data.expires_in
        });

        const newTokens: TokenData = {
          idToken: response.data.id_token || response.data.access_token,
          accessToken: response.data.access_token,
          refreshToken: response.data.refresh_token || refreshToken,
          expiresIn: response.data.expires_in || 3600,
          tokenType: response.data.token_type || 'Bearer'
        };

        if (!newTokens.accessToken) {
          throw new Error('No access token in response');
        }

        // Store new tokens
        const expiryTime = Date.now() + (newTokens.expiresIn * 1000);
        localStorage.setItem('token_expiry', expiryTime.toString());

        localStorage.removeItem('id_token');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');

        setAuthTokens(newTokens);
        console.log('Successfully refreshed and stored new tokens');
        return newTokens;

      } catch (error: any) {
        lastError = error;
        console.error(`Refresh attempt ${retryCount + 1} failed:`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });

        retryCount++;

        if (error.response?.status === 400 &&
            error.response?.data?.error === 'invalid_grant') {
          console.log('Invalid grant error - token is invalid, clearing tokens');
          clearTokens();
          break;
        }
      }
    }

    throw lastError || new Error('Failed to refresh tokens after retries');

  } catch (error: any) {
    console.error('Error refreshing tokens:', error);
    if (error.response) {
      console.error('Error details:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }
    throw error;
  }
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch (err) {
    return true;
  }
};

export const getIdToken = (auth?: any): string => {
  console.log('getIdToken called with auth:', {
    hasAuth: !!auth,
    hasUser: !!auth?.user,
    userKeys: auth?.user ? Object.keys(auth.user) : [],
    id_token: auth?.user?.id_token ? 'present' : 'missing',
    access_token: auth?.user?.access_token ? 'present' : 'missing'
  });

  // Try to get token from react-oidc-context auth object first
  let token = auth?.user?.id_token || auth?.user?.access_token;

  // Fallback to localStorage
  if (!token) {
    token = localStorage.getItem('id_token') || localStorage.getItem('access_token');
  }

  if (token) {
    if (isTokenExpired(token)) {
      console.warn('Token is expired.');
      throw new Error('Token expired');
    }
    console.log('Found valid token');
    return token;
  }
  console.warn('User is not authenticated - no token found.');
  throw new Error('User is not authenticated');
};

// Initialize tokens from storage
const initializeTokens = async () => {
  try {
    // If already initializing, wait for the existing initialization
    if (isInitializingTokens && tokenInitializationPromise) {
      console.log('Token initialization already in progress, waiting...');
      return tokenInitializationPromise;
    }

    isInitializingTokens = true;
    tokenInitializationPromise = (async () => {
      try {
        console.log('Starting token initialization...');

        // Try to get token from localStorage first without throwing errors
        const token = localStorage.getItem('id_token');
        const accessToken = localStorage.getItem('access_token');
        const refreshToken = localStorage.getItem('refresh_token');
        const tokenExpiry = localStorage.getItem('token_expiry');

        console.log('Tokens in storage:', {
          hasIdToken: !!token,
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          tokenExpiry: tokenExpiry ? new Date(parseInt(tokenExpiry)).toISOString() : null
        });

        // If we have valid id_token and it hasn't expired, set interceptor and return
        if (token && tokenExpiry && parseInt(tokenExpiry) > Date.now()) {
          apiClient.interceptors.request.use(async (config) => {
            // Use the token we already found
            if (token) {
              config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
          });

          console.log('Using existing valid tokens with id_token');
          return;
        }

        // If tokens are expired and we have refresh_token, try to refresh
        if (refreshToken) {
          try {
            console.log('Tokens expired or missing, attempting refresh...');
            const newTokens = await refreshTokens(refreshToken);
            console.log('Successfully refreshed tokens');
            return;
          } catch (refreshError) {
            console.error('Failed to refresh tokens:', refreshError);
            // Don't clear tokens here, just log the error
          }
        }

        console.log('No valid tokens found or refresh failed');
        // Don't throw an error, just log the state
      } catch (error) {
        console.error('Error in token initialization:', error);
        // Don't rethrow, just log the error
      } finally {
        isInitializingTokens = false;
        tokenInitializationPromise = null;
      }
    })();

    return tokenInitializationPromise;
  } catch (error) {
    console.error('Error in initializeTokens:', error);
    isInitializingTokens = false;
    tokenInitializationPromise = null;
    // Don't rethrow, just log the error
  }
};


// Call initialize on service load
initializeTokens();

// Set the current tokens
export const setAuthTokens = (tokens: TokenData) => {
  if (!tokens.idToken) {
    throw new Error('ID token is required');
  }
 

  

  // Also store in Cognito format for persistence
  const cognitoKey = 'CognitoIdentityServiceProvider.' + cognitoConfig.clientId;
  const lastUser = localStorage.getItem(cognitoKey + '.LastAuthUser');
  if (lastUser) {
    localStorage.setItem(cognitoKey + '.' + lastUser + '.idToken', tokens.idToken);
    localStorage.setItem(cognitoKey + '.' + lastUser + '.accessToken', tokens.accessToken);
    if (tokens.refreshToken) {
      localStorage.setItem(cognitoKey + '.' + lastUser + '.refreshToken', tokens.refreshToken);
    }
  }
  


  console.log('Tokens stored in both formats');
};

// Clear tokens helper
const clearTokens = () => {
  console.log('Clearing all tokens from storage');
  localStorage.removeItem('id_token');
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('token_expiry');
  localStorage.removeItem('token_type');
  localStorage.removeItem('expires_in');
  
  // Also clear Cognito storage
  const cognitoKey = 'CognitoIdentityServiceProvider.' + cognitoConfig.clientId;
  const lastUser = localStorage.getItem(cognitoKey + '.LastAuthUser');
  if (lastUser) {
    localStorage.removeItem(cognitoKey + '.' + lastUser + '.idToken');
    localStorage.removeItem(cognitoKey + '.' + lastUser + '.accessToken');
    localStorage.removeItem(cognitoKey + '.' + lastUser + '.refreshToken');
    localStorage.removeItem(cognitoKey + '.LastAuthUser');
  }
  
 
};






  

// Get the current token with refresh if needed
const getToken = async (auth: any): Promise<string> => {
  const token = await getIdToken(auth);
  if (!token) throw new Error('User is not authenticated or token expired.');
  return token;
};



// Add response interceptor for handling 401s with better retry logic
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry && currentTokens?.refreshToken) {
      originalRequest._retry = true;
      
      try {
        console.log('Attempting token refresh on 401');
        const newTokens = await refreshTokens(currentTokens.refreshToken);
        originalRequest.headers['Authorization'] = `Bearer ${newTokens.idToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        console.error('Error refreshing token on 401:', refreshError);
        // Only clear tokens if we can't recover
        if (!currentTokens?.idToken) {
          localStorage.removeItem('id_token');
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('token_expiry');
          localStorage.removeItem('token_type');
          localStorage.removeItem('expires_in');
         
        }
        throw error;
      }
    }
    return Promise.reject(error);
  }
);

// Store the institution ID once we retrieve it
let cachedInstitutionId: string | null = null;

// Utility function to get the institution ID
export const getStoredInstitutionId = async (): Promise<string> => {
  // If we have a cached ID, return it
  if (cachedInstitutionId) {
    return cachedInstitutionId;
  }

  // Check if we have it in localStorage as a fallback
  const storedId = localStorage.getItem('institutionId');
  if (storedId) {
    cachedInstitutionId = storedId;
    return storedId;
  }

  // If no ID is found, throw an error
  throw new Error('No institution ID found. Please sign in again.');
};

// Utility function to store the institution ID
export const storeInstitutionId = (id: string): void => {
  cachedInstitutionId = id;
  localStorage.setItem('institutionId', id);
  console.log('Stored institution ID:', id);
};

export interface Institution {
  bankBranch: string;
  ifscCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  id: string; // UUID format
  userId: string; // UUID format
  name: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  registrationNumber: string;
  websiteUrl: string;
  email: string;
  contactPhone: string;
  verified: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Campaign {
  targetAmount: number;
  id: string;
  title: string;
  description: string;
  goalAmount: number;
  raisedAmount: number;
  progressPercentage: number;
  studentRegId: string;
  studentName: string;
  studentEmail: string;
  institutionId: string;
  institutionName: string;
  status: string;
  category: string | null;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  supportersCount: number;

  // Optional or frontend-managed fields
  campaignTitle?: string;
  progress?: number;
  lastUpdated?: string;
  documents?: string[];
  milestones?: {
    title: string;
    status: "completed" | "pending" | "in-progress";
    dueDate: string;
  }[];
  studentDetails?: {
    course?: string;
    year?: string;
    phone?: string;
  };
}

export interface CampaignDetails {
  studentRegId: string;
  studentBatch: ReactNode;
  studentYear: string;
  studentDepartment: string;
  approvalDate: any;
  autoCompleteAmount: number;
  autoCloseDate: any;
  endDateReached: any;
  updatedAt: ReactNode;
  createdAt: any;
  title: string;
  id: string;
  studentName: string;
  studentId: string;
  institutionId: string;
  campaignTitle: string;
  description: string;
  targetAmount: number;
  goalAmount: number;
  raisedAmount: number;
  startDate: string;
  endDate: string;
  status: "ACTIVE" | "COMPLETED" | "PAUSED" | "PENDING" | "REJECTED";
  category: "TUITION" | "RESEARCH" | "PROJECT" | "CONFERENCE" | "OTHER";
  progress: number;
  supporters: number;
  lastUpdated: string;
  approvedBy?: string;
  approvedOn?: string;
  rejectionReason?: string;
  documents: string[];
  milestones: {
    title: string;
    status: "COMPLETED" | "PENDING" | "IN_PROGRESS";
    dueDate: string;
  }[];
  // Privacy settings
  showSupportersToOthers?: boolean;
  // Top-level student details fields for direct API access
  year?: string;
  phone?: string;
  department?: string;
  academicRecord?: string;
  // Nested student details for frontend use
  studentDetails: {
    email: string;
    course: string;
    year: string;
    phone: string;
    academicRecord: string;
    department: string;
    batch?: string;
    story?: string;
  };
}

export interface Student {
  year: any;
  id: string;
  userId: string;
  name: string;
  studentId: string;
  course: string;
  academicYear: string;
  semester?: string;
  department?: string;
  email: string;
  phone?: string;
  institutionId: string;
  status: string;
  batch?: string;
  academicRecord?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ApprovalRequest {
  id: string;
  title: string;
  description: string;
  goalAmount: number;
  studentName: string;
  studentEmail: string;
  institutionName: string;
  status: string;
  createdAt: string;
}

export interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  entityId?: string;
  entityType?: string;
  userId?: string;
  userName?: string;
  userRole?: string;
  institutionId?: string;
  createdAt?: string;
  updatedAt?: string;
  // Additional fields from detailed activity API
  activityId?: string;
  detailType?: string;
  contextData?: Record<string, any> | null;
  additionalContext?: Record<string, any> | null;
  apiEndpoints?: {
    primaryDataEndpoint?: string;
    primaryDataType?: string;
    secondaryDataEndpoint?: string;
    secondaryDataType?: string;
    activityDetailEndpoint?: string;
    relatedActivitiesEndpoint?: string;
  } | null;
  primaryTitle?: string | null;
  secondaryTitle?: string | null;
  statusBadge?: string | null;
  // For backward compatibility
  action?: string;
  user?: string;
}

export interface User {
  id: string;
  roleId?: string; // Add roleId for deletion
  name: string;
  email: string;
  role: string | string[];
  roles?: string[]; // Add roles array from API
  status: string;
  lastLogin?: string;
  department?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BankDetails {
  accountName: string;
  accountNumber: string;
  ifscCode: string;
  razorpayContactId?: string;
  razorpayFundAccountId?: string;
  verified?: boolean;
}

export interface InstitutionStats {
  fundsRaised: number;
  activeCampaigns: number;
  totalFundsRaised: number;
  totalStudents: number;
  pendingApprovals: number;
}

export interface InstitutionProfileData {
  name: string;
  email: string;
  phone: string;
  website: string;
  address: string;
  logoUrl?: string;
}

interface DashboardSummary {
  totalFundsRaised: number;
  pendingApprovals: number;
  activeCampaigns: number;
  fundsRaised: number;
  totalStudents: number;
}

const institutionService = {
  setAuthTokens,
  // Get institution by user ID
 getInstitutionByUserId: async (userId: string): Promise<Institution> => {
  try {
    // Ensure userId is treated as a string (UUID format)
    const userIdStr = String(userId);
    console.log('Fetching institution for user ID (UUID format):', userIdStr);

    // First, try to get all institutions to find the one we need
    try {
      const response = await api.get(`/institutions`);
      console.log('All institutions response:', response.data);

      if (Array.isArray(response.data) && response.data.length > 0) {
        const matchingInstitution = response.data.find((inst: any) =>
          inst.userId === userIdStr || inst.userId === userId
        );

        if (matchingInstitution) {
          console.log('Found matching institution:', matchingInstitution);
          return matchingInstitution;
        } else {
          console.log('No exact match found, using first institution:', response.data[0]);
          return response.data[0];
        }
      }
    } catch (listError) {
      console.error('Error fetching all institutions:', listError);
    }

    // Fallback: return minimal placeholder institution
    console.error('All attempts to fetch institution failed, returning placeholder');
    return {
        id: '',
      userId: userIdStr,
      name: 'Institution Not Found',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      registrationNumber: '',
      websiteUrl: '',
      email: '',
      contactPhone: '',
      verified: false,
      bankBranch: '',
      ifscCode: '',
      bankName: '',
      accountNumber: '',
      accountName: ''
    };
  } catch (error) {
    console.error('Error fetching institution by user ID:', error);

    // Fallback in case of unexpected error
    return {
        id: '',
      userId: String(userId),
      name: 'Institution Not Found',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      registrationNumber: '',
      websiteUrl: '',
      email: '',
      contactPhone: '',
      verified: false,
      bankBranch: '',
      ifscCode: '',
      bankName: '',
      accountNumber: '',
      accountName: ''
    };
  }
},

  // Get all institutions for a user
 getAllInstitutionsByUserId: async (userId: string): Promise<Institution[]> => {
  try {
    const userIdStr = String(userId);
    console.log('Fetching all institutions for user ID (UUID format):', userIdStr);

    // Try to get all institutions from the API
    try {
      const response = await api.get(`/institutions`);
      console.log('All institutions response:', response.data);

      if (Array.isArray(response.data) && response.data.length > 0) {
        const matchingInstitutions = response.data.filter((inst: any) =>
          inst.userId === userIdStr || inst.userId === userId
        );

        if (matchingInstitutions.length > 0) {
          console.log('Found matching institutions:', matchingInstitutions);
          return matchingInstitutions;
        } else {
          console.log('No exact matches found, returning all institutions:', response.data);
          return response.data;
        }
      }

      console.log('No institutions found in the response');
    } catch (listError) {
      console.error('Error fetching all institutions:', listError);
    }

    // Fallback placeholder institution
    console.warn('All attempts to fetch institutions failed, returning placeholder array');
    return [{
        id: '',
      userId: userIdStr,
      name: 'Institution Not Found',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      registrationNumber: '',
      websiteUrl: '',
      email: '',
      contactPhone: '',
      verified: false,
      bankBranch: '',
      ifscCode: '',
      bankName: '',
      accountNumber: '',
      accountName: ''
    }];
  } catch (error) {
    console.error('Error fetching institutions by user ID:', error);
    return [{
        id: '',
      userId: String(userId),
      name: 'Institution Not Found',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      registrationNumber: '',
      websiteUrl: '',
      email: '',
      contactPhone: '',
      verified: false,
      bankBranch: '',
      ifscCode: '',
      bankName: '',
      accountNumber: '',
      accountName: ''
    }];
  }
  },

  // Get institution by ID
  getInstitutionById: async (id: string): Promise<Institution> => {
    try {
      console.log('Fetching institution with ID:', id);
      const response = await api.get(`/institutions/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching institution by ID:', error);
      throw error;
    }
  },

  // Get all campaigns for an institution
getInstitutionCampaigns: async (
  page: number = 0,
  size: number = 20,
  sort: string = 'updatedAt,desc'
): Promise<{
  content: Campaign[];
  totalPages: number;
  totalElements: number;
}> => {
  try {
    console.log(`Fetching campaigns, page: ${page}, size: ${size}, sort: ${sort}`);
    const response = await api.get(`/api/institution/v1/campaigns?page=${page}&size=${size}&sort=${sort}`);

    return response.data;
  } catch (error) {
    console.error('Error fetching institution campaigns:', error);
    return {
      content: [],
      totalPages: 0,
      totalElements: 0,
    };
  }
}

,

  // Search campaigns with various filters
searchCampaigns: async (
  filters: {
    title?: string;
    status?: string;
    category?: string;
    startDate?: string;
    endDate?: string;
    minAmount?: number;
    maxAmount?: number;
  },
  page: number = 0,
  size: number = 20,
  sort: string = 'createdAt,desc' // 👈 add this
): Promise<{
  content: Campaign[];
  totalPages: number;
  totalElements: number;
}> => {
  try {
    const queryParams = new URLSearchParams();

    if (filters.title) queryParams.append('keyword', filters.title);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.category) queryParams.append('category', filters.category);
    if (filters.startDate) queryParams.append('startDate', filters.startDate);
    if (filters.endDate) queryParams.append('endDate', filters.endDate);
    if (filters.minAmount) queryParams.append('minAmount', filters.minAmount.toString());
    if (filters.maxAmount) queryParams.append('maxAmount', filters.maxAmount.toString());

    queryParams.append('page', page.toString());
    queryParams.append('size', size.toString());
    queryParams.append('sort', sort); // ✅ use the passed sort param

    const response = await api.get(`/api/institution/v1/campaigns/search?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error searching campaigns:', error);
    return {
      content: [],
      totalPages: 0,
      totalElements: 0
    };
  }
}

,

createCampaignForStudent: async (campaignData: {
  studentFirstName: string;
  studentLastName: string;
  studentName: string;
  studentPhoneNumber: string;
  studentBatch: string;
  studentCourse: string;
  studentDepartment: string;
  studentYear: string;
  studentRegId: string;
  studentEmail: string;
  title: string;
  description: string;
  goalAmount: number;
  startDate: string;
  endDate: string;
}) => {
  try {
    const response = await api.post('/api/institution/v1/campaigns', campaignData);
    return response.data;
  } catch (error) {
    console.error('Error creating campaign for student:', error);
    throw error;
  }
},

updateCampaign: async (
  campaignId: string,
  payload: {
    title: string;
    description: string;
    goalAmount: number;
    startDate: string;
    endDate: string;
  },
  auth: any
): Promise<any> => {
  try {
    console.log(`Updating campaign ${campaignId} with payload:`, payload);
    const token = await getIdToken(auth);
    const response = await api.put(
      `/api/institution/v1/campaigns/${campaignId}`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating campaign:', error);
    throw error;
  }
},




// Add this method


  
  // Get all campaigns for a student
  getStudentCampaigns: async (studentId: string, page: number = 0, size: number = 20): Promise<Campaign[]> => {
    try {
      console.log(`Fetching campaigns for student ID: ${studentId}, page: ${page}, size: ${size}`);
      const response = await api.get(`/campaigns/student/${studentId}?page=${page}&size=${size}`);
      if (Array.isArray(response.data) && response.data.length === 0) {
        console.log('No campaigns found for student');
      }
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        console.warn('No campaigns found for student (404)');
        return [];
      }
      console.error('Error fetching student campaigns:', error);
      return [];
    }
  },

  // Get pending student registrations
  getPendingStudentRegistrations: async (): Promise<Student[]> => {
    try {
      console.log('Fetching pending student registrations');
      const response = await api.get('/api/institution/students/pending');
      if (Array.isArray(response.data) && response.data.length === 0) {
        console.log('No pending registrations found');
      }
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        console.warn('No pending registrations found (404)');
        return [];
      }
      console.error('Error fetching pending student registrations:', error);
      return [];
    }
  },

  // Get approval requests for an institution
  getApprovalRequests: async (): Promise<ApprovalRequest[]> => {
    try {
      console.log('Fetching approval requests');
      const response = await api.get('/api/institution/approvals');
      if (Array.isArray(response.data) && response.data.length === 0) {
        console.log('No approval requests found');
      }
      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        console.warn('No approval requests found (404)');
        return [];
      }
      console.error('Error fetching approval requests:', error);
      return [];
    }
  },

  // Filter approval requests by various criteria
  filterApprovalRequests: async (
    filters: {
      status?: string;
      startDate?: string;
      endDate?: string;
      studentName?: string;
    }
  ): Promise<ApprovalRequest[]> => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters.status) {
        queryParams.append('status', filters.status);
      }

      if (filters.startDate) {
        queryParams.append('startDate', filters.startDate);
      }

      if (filters.endDate) {
        queryParams.append('endDate', filters.endDate);
      }

      if (filters.studentName) {
        queryParams.append('studentName', filters.studentName);
      }

      const queryString = queryParams.toString();
      const url = `/api/institution/approvals/filter${queryString ? `?${queryString}` : ''}`;

      console.log(`Filtering approval requests with URL: ${url}`);
      const response = await api.get(url);
      if (Array.isArray(response.data) && response.data.length === 0) {
        console.log('No approval requests found for filters');
      }
      return response.data;
    } catch (error) {
      if ((error as any).response && (error as any).response.status === 404) {
        console.warn('No approval requests found for filters (404)');
        return [];
      }
      console.error('Error filtering approval requests:', error);
      return [];
    }
  },

  // Get approval request details
  getApprovalRequestDetails: async (approvalId: string): Promise<ApprovalRequest> => {
    try {
      console.log(`Fetching approval request details for ID: ${approvalId}`);
      const response = await api.get(`/approvals/${approvalId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching approval request details:', error);
      throw error;
    }
  },

  // Update approval request status
  updateApprovalStatus: async (approvalId: string, status: string, reason?: string) => {
    try {
      console.log(`Updating approval request status for ID: ${approvalId} to ${status}`);
      const response = await api.patch(`/approvals/${approvalId}/status`, { status, reason });
      return response.data;
    } catch (error) {
      console.error('Error updating approval request status:', error);
      throw error;
    }
  },

  // Approve a campaign
  approveCampaign: async (campaignId: string, userId: string) => {
    try {
      console.log(`Approving campaign ${campaignId} by user ${userId}`);
      // Use the API endpoint from swagger-apis.txt
      const response = await api.post(`/campaigns/${campaignId}/approve?userId=${userId}`);
      console.log('Campaign approval response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error approving campaign:', error);
      throw error;
    }
  },

  // Reject a campaign with reason
  rejectCampaign: async (campaignId: string, userId: string, reason: string) => {
    try {
      console.log(`Rejecting campaign ${campaignId} by user ${userId} with reason: ${reason}`);
      // Use the API endpoint from swagger-apis.txt
      const response = await api.post(`/campaigns/${campaignId}/reject?userId=${userId}`, {
        reason: reason
      });
      console.log('Campaign rejection response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error rejecting campaign:', error);
      throw error;
    }
  },

  // Delete a campaign
  deleteCampaign: async (campaignId: string, userId: string) => {
    try {
      console.log(`Deleting campaign ${campaignId} by user ${userId}`);
      // Use the API endpoint from swagger-apis.txt
      const response = await api.delete(`/campaigns/${campaignId}?userId=${userId}`);
      console.log('Campaign deletion response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting campaign:', error);
      throw error;
    }
  },

  // Get recent activities


  // Get paginated recent activities for an institution
  getPaginatedActivities: async (
    page: number = 0,
    size: number = 5
  ): Promise<{activities: Activity[], totalItems: number, totalPages: number}> => {
    try {
      console.log(`Fetching paginated activities, page ${page}, size ${size}`);
      const response = await api.get(
        `/api/institution/activities?page=${page}&size=${size}`
      );

      console.log('Paginated activities response:', response.data);

      // If the backend returns paginated data in Spring format
      if (response.data.content) {
        console.log('Backend returned paginated data in Spring format');
        return {
          activities: response.data.content,
          totalItems: response.data.totalElements || 0,
          totalPages: response.data.totalPages || 0
        };
      }

      // If the backend returns an array but doesn't support pagination
      if (Array.isArray(response.data)) {
        console.log('Backend returned non-paginated array, but we should not handle pagination on client side');
        // Return the data as is, with a message to implement pagination on the backend
        console.warn('Backend should implement pagination. Returning all data without client-side pagination.');
        return {
          activities: response.data,
          totalItems: response.data.length,
          totalPages: 1
        };
      }

      // Default return if response format is unexpected
      return {
        activities: [],
        totalItems: 0,
        totalPages: 0
      };
    } catch (error) {
      console.error('Error fetching paginated activities:', error);
      return {
        activities: [],
        totalItems: 0,
        totalPages: 0
      };
    }
  },

  postInstitutionWelcome: async (auth?: any): Promise<any> => {
  try {
    console.log('Sending welcome POST request');
    const token = await getIdToken(auth);

    const response = await api.post('/api/institution/v1/welcome', {}, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });
    
    console.log('Welcome response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending welcome request:', error);
    return null;
  }
},


  getInstitutionRoles: async (auth?: any): Promise<any> => {
  try {
    console.log('Fetching institution roles');
    const token = await getIdToken(auth);

    const response = await api.get('/api/institution/v1/me/roles', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Fetched roles:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    return null;
  }
},


getAllCampaigns: async (auth?: any): Promise<any> => {
  try {
    console.log('Fetching all institution campaigns');
 const token = await getIdToken(auth); // ✅ pass auth to getIdToken

    const response = await api.get('/api/institution/v1/campaigns', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Fetched campaigns:', response.data);
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error('Error fetching all campaigns:', error);
    return [];
  }
},

searchCampaignsV1: async (
  filters: {
    keyword?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    size?: number;
    sort?: string;
  },
  auth?: any
): Promise<any> => {
  try {
 const token = await getIdToken(auth); // ✅ pass auth to getIdToken
    const params = new URLSearchParams();

    if (filters.keyword) params.append('keyword', filters.keyword);
    if (filters.status) params.append('status', filters.status);
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.page !== undefined) params.append('page', filters.page.toString());
    if (filters.size !== undefined) params.append('size', filters.size.toString());
    if (filters.sort) params.append('sort', filters.sort);

    const url = `/api/institution/v1/campaigns/search?${params.toString()}`;
    console.log('Searching campaigns with URL:', url);

    const response = await api.get(url, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Search result:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error searching campaigns:', error);
    return { content: [], totalPages: 0, totalElements: 0 };
  }
},


  // Create a new institution
  createInstitution: async (institutionData: {
    name: string;
    email: string;
  }) => {
    try {
      console.log('Creating new institution with data:', institutionData);
      const response = await api.post('/institutions', institutionData);
      return response.data;
    } catch (error) {
      console.error('Error creating institution:', error);
      throw error;
    }
  },

  // Update an institution
  updateInstitution: async (id: string, institutionData: Partial<Institution>) => {
    try {
      console.log(`Updating institution ${id} with data:`, institutionData);

      // Create a payload with only the fields expected by the API
      const payload = {
        name: institutionData.name,
        address: institutionData.address,
        email: institutionData.email,
        phone: institutionData.contactPhone,
        website: institutionData.websiteUrl
      };

      console.log('Sending institution update payload:', payload);
      // Use the PUT endpoint from swagger-apis.txt
      const response = await api.put(`/institutions/${id}`, payload);
      return response.data;
    } catch (error) {
      console.error('Error updating institution:', error);
      throw error;
    }
  },

  getInstitutionProfile: async (auth?: any): Promise<any> => {
  try {
    console.log('Fetching institution profile');
 const token = await getIdToken(auth); // ✅ pass auth to getIdToken

    const response = await api.get('/api/institution/v1/profile', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Fetched profile:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching institution profile:', error);
    return null;
  }
},


  // Update institution profile settings
updateInstitutionProfile: async (profileData: {
  name: string;
  email: string;
  phone: string;
  website: string;
  address: string;
}, auth?: any): Promise<{success: boolean, message?: string}> => {
  try {
    console.log('Updating institution profile');
    const token = await getIdToken(auth);

    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await api.put('/api/institution/v1/profile-update', profileData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Profile update response:', response.data);
    
    // If we get a 200 OK response with data, consider it successful
    // The API doesn't return a success flag, it just returns the updated profile
    if (response.status === 200 && response.data) {
      return { success: true };
    }
    
    // If there's a message property in the response, use it for error
    if (response.data && response.data.message) {
      return {
        success: false,
        message: response.data.message
      };
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error updating institution profile:', error);
    return { 
      success: false, 
      message: error.response?.data?.message || 'Error updating profile'
    };
  }
},


inviteInstitutionUser: async (
  data: {
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
  },
  auth?: any
): Promise<any> => {
  try {
    const token = await getIdToken(auth); // Get token from auth context
    const response = await api.post(
      '/api/institution/v1/users/invite',
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('User invited successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error inviting user:', error);
    throw error;
  }
},


deleteUserRoleById: async (roleId: string): Promise<void> => {
  try {
    await api.delete(`/api/institution/v1/users/roles/${roleId}`);
    console.log(`Deleted user role with ID: ${roleId}`);
  } catch (error) {
    console.error('Error deleting user role:', error);
    throw error;
  }
}
,
  // Get institution bank details
 getInstitutionBankDetails: async (auth?: any): Promise<{
  accountName: string;
  accountNumber: string;
  ifscCode: string;
} | null> => {
  try {
    console.log('Fetching institution bank details');
 const token = await getIdToken(auth); // ✅ pass auth to getIdToken

    const response = await api.get('/api/institution/v1/bank-details', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Fetched bank details:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching bank details:', error);
    return null;
  }
},


  // Update institution bank details
  updateInstitutionBankDetails: async (bankData: {
  accountName: string;
  accountNumber: string;
  ifscCode: string;
}, auth?: any): Promise<{success: boolean, message?: string}> => {
  try {
    console.log('Updating institution bank details');
    const token = await getIdToken(auth);

    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await api.put('/api/institution/v1/bank-details', bankData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Bank details update response:', response.data);
    
    // If we get a 200 OK response, consider it successful
    if (response.status === 200) {
      // If the response has a success property, use it
      if (response.data && typeof response.data.success === 'boolean') {
        if (response.data.success === false) {
          return {
            success: false,
            message: response.data.message || 'Failed to update bank details'
          };
        }
      }
      
      // Otherwise, assume success if we got a 200 OK
      return { success: true };
    }
    
    return { 
      success: false, 
      message: 'Failed to update bank details'
    };
  } catch (error) {
    console.error('Error updating bank details:', error);
    return { 
      success: false, 
      message: error.response?.data?.message || 'Error updating bank details'
    };
  }
  },

  // Log when bank details are viewed in the dashboard
  logBankDetailsView: async (auth?: any): Promise<void> => {
    try {
      console.log('Logging bank details view');
      const token = await getIdToken(auth);

      await api.post('/api/institution/v1/bank-details/log', null, {
        headers: {
          Authorization: `Bearer ${token}`,
          accept: '*/*'
        }
      });
    } catch (error) {
      console.error('Error logging bank details view:', error);
    }
  },

  getStudentCampaignStatus: async (auth?: any): Promise<any> => {
  try {
    console.log('Fetching student campaign status...');
 const token = await getIdToken(auth); // ✅ pass auth to getIdToken

    const response = await api.get('/api/student/v1/me/campaign-status', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    console.log('Student campaign status response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching student campaign status:', error);
    return null;
  }
},

  // Update notification preferences
  updateNotificationPreferences: async (id: string, preferences: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    appNotifications: boolean;
    campaignUpdates: boolean;
    paymentAlerts: boolean;
  }) => {
    try {
      console.log(`Updating notification preferences for institution ${id}:`, preferences);
      const response = await api.patch(`/institutions/${id}/notification-preferences`, preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  },

  // Update security settings
  updateSecuritySettings: async (id: string, securitySettings: {
    twoFactorEnabled: boolean;
    passwordResetRequired: boolean;
    sessionTimeout: number;
  }) => {
    try {
      console.log(`Updating security settings for institution ${id}:`, securitySettings);
      const response = await api.patch(`/institutions/${id}/security-settings`, securitySettings);
      return response.data;
    } catch (error) {
      console.error('Error updating security settings:', error);
      throw error;
    }
  },

  // Generate reports
  generateReport: async (reportType: 'campaign' | 'approvals' | 'transaction', filters?: {
    startDate?: string;
    endDate?: string;
    status?: string;
  }) => {
    try {
      const queryParams = new URLSearchParams();

      if (filters?.startDate) {
        queryParams.append('startDate', filters.startDate);
      }

      if (filters?.endDate) {
        queryParams.append('endDate', filters.endDate);
      }

      if (filters?.status) {
        queryParams.append('status', filters.status);
      }

      // Always use CSV format
      queryParams.append('format', 'csv');

      const reportTypeMapping = {
        'campaign': 'campaigns',
        'transaction': 'transactions',
        'approvals': 'approvals' // already plural
      };

      const pluralReportType = reportTypeMapping[reportType] || reportType;
      const queryString = queryParams.toString();
      const url = `/api/institution/reports/${pluralReportType}${queryString ? `?${queryString}` : ''}`;

      console.log(`Generating report with URL: ${url}`);
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  },

  // Download reports as files
  downloadReport: async (
    reportType: 'campaigns' | 'approvals' | 'transactions',
    params: {
      from: string;
      to: string;
      status?: string;
      type?: string;
      format: string;
    }
  ): Promise<Blob> => {
    try {
      const searchParams = new URLSearchParams();
      searchParams.append('from', params.from);
      searchParams.append('to', params.to);
      if (params.status) searchParams.append('status', params.status);
      if (params.type) searchParams.append('type', params.type);
      searchParams.append('format', params.format);

      const url = `/api/institution/v1/reports/${reportType}`;
      const response = await api.get(url, {
        params: searchParams,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error downloading report:', error);
      throw error;
    }
  },

  // Get dashboard summary
 async getDashboardSummary(auth: any): Promise<DashboardSummary> {
  try {
    console.log('Fetching dashboard summary...');
    const token = await getIdToken(auth); // ✅ pass auth to getIdToken
    console.log('Got token for summary API:', !!token);

    const response = await api.get('/api/institution/v1/dashboard/summary', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    console.log('Summary API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching dashboard summary:', error);
    throw error;
  }
}
,

async getInstitutionUsers(auth: any): Promise<User[]> {
  try {
    console.log('Fetching institution users...');
    const token = await getIdToken(auth); // ✅ retrieve token using auth
    console.log('Got token for institution users API:', !!token);

    const response = await api.get('/api/institution/v1/users', {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: '*/*',
        'Content-Type': 'application/json',
      },
    });

    console.log('Institution users API response:', response.data);

    // Extract users array from the nested response structure
    const rawUsers = response.data.data || response.data;
    console.log('Extracted raw users array:', rawUsers);

    // Transform API response to match frontend User interface
    const users = rawUsers.map((user: any) => ({
      id: user.userId,
      roleId: user.roleId, // Include roleId for deletion
      name: `${user.firstName} ${user.lastName}`,
      email: user.email,
      role: user.role || user.roles, // Use role or roles property
      roles: user.roles, // Include the roles array from API
      status: user.status,
      lastLogin: user.lastLogin,
      department: user.department,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }));

    console.log('Transformed users for frontend:', users);
    return users;
  } catch (error) {
    console.error('Error fetching institution users:', error);
    throw error;
  }
}

,

async getCampaignById(campaignId: string, auth: any): Promise<CampaignDetails> {
  try {
    console.log(`Fetching campaign by ID: ${campaignId}`);
    const token = await getIdToken(auth); // ✅ get ID token using auth
    console.log('Got token for campaign API:', !!token);

    const response = await api.get(`/api/institution/v1/campaigns/${campaignId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: '*/*',
        'Content-Type': 'application/json',
      },
    });

    console.log('Campaign API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching campaign by ID:', error);
    throw error;
  }
},


async getDashboardActivities(
  auth: any,
  options: { keyword?: string; page?: number; size?: number; sort?: string } = {}
): Promise<{ content: Activity[]; totalPages: number }> {
  const token = await getIdToken(auth);

  const params: Record<string, any> = {
    page: options.page ?? 0,
    size: options.size ?? 5,
    sort: options.sort ?? 'asc'
  };
  if (options.keyword) {
    params.keyword = options.keyword;
  }

  const response = await api.get('/api/institution/v1/dashboard/activities', {
    headers: {
      Authorization: `Bearer ${token}`
    },
    params
  });

  return response.data;
},

async getRecentActivities(
  auth: any,
  page = 0,
  size = 5
): Promise<{ content: Activity[]; totalPages: number }> {
  return institutionService.getDashboardActivities(auth, {
    page,
    size,
    sort: 'asc'
  });
}


,


  deleteInstitutionUser: async (userId: string, auth: any): Promise<void> => {
    try {
      console.log(`Deleting institution user with userId: ${userId}`);
      // Get the auth token
      const token = await getIdToken(auth);
      
      // The API endpoint expects a userId and requires authentication
      await api.delete(`/api/institution/v1/users/${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('User deleted successfully');
    } catch (error) {
      console.error('Error deleting institution user:', error);
      throw error;
    }
  },

  // Assign roles to an existing user
  assignUserRoles: async (userId: string, roles: string[], auth: any): Promise<any> => {
    try {
      console.log(`Assigning roles ${roles.join(', ')} to user ${userId}`);
      const token = await getIdToken(auth);
      const response = await api.post(`/api/institution/v1/users/${userId}/roles`, roles, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error assigning roles to user:', error);
      throw error;
    }
  },

  // Revoke a single role from a user
  revokeUserRole: async (userId: string, roleName: string, auth: any): Promise<any> => {
    try {
      console.log(`Revoking role ${roleName} from user ${userId}`);
      const token = await getIdToken(auth);
      const response = await api.delete(`/api/institution/v1/users/${userId}/roles/${roleName}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error revoking role from user:', error);
      throw error;
    }
  },

  // Upload institution logo
  uploadInstitutionLogo: async (formData: FormData, auth: any): Promise<{ success: boolean; message?: string; logoUrl?: string }> => {
  try {
    console.log("Uploading institution logo");
    const token = await getIdToken(auth);

    if (!token) {
      throw new Error("No authentication token available");
    }

    const response = await api.post("/api/institution/v1/logo", formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        // Let the browser set the Content-Type boundary
        "Content-Type": "multipart/form-data"
      }
    });

    console.log("Logo upload response:", response.data);

    if (response.status === 200) {
      return {
        success: true,
        logoUrl: response.data.logoUrl || response.data.url
      };
    }

    return {
      success: false,
      message: "Failed to upload logo"
    };
  } catch (error:any) {
    console.error("Error uploading logo:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Error uploading logo"
    };
  }
},

  // Fetch institution logo
  getInstitutionLogo: async (auth?: any): Promise<string | null> => {
    try {
      const token = await getIdToken(auth);
      const response = await api.get('/api/institution/v1/logo', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data.logoUrl || null;
    } catch (error) {
      console.error('Error fetching institution logo:', error);
      return null;
    }
  },

};

export default institutionService;
