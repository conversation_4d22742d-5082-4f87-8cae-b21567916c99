import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  UserPlus, 
  Bell, 
  DollarSign, 
  Calendar, 
  AlertTriangle, 
  Clock, 
  Filter, 
  RefreshCw, 
  Search,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { toast } from 'react-toastify';
import institutionService, { Activity } from '../services/institutionService';
import { fetchAuthSession } from 'aws-amplify/auth';
import StatusPage from './StatusPage';

interface CampaignActivityLogProps {
  campaignId: number;
  institutionId: number;
}

export function CampaignActivityLog({ campaignId, institutionId }: CampaignActivityLogProps) {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: '',
    end: ''
  });
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  // Activity types for filtering
  const activityTypes = [
    { value: 'all', label: 'All Activities' },
    { value: 'campaign', label: 'Campaign Updates' },
    { value: 'approval', label: 'Approvals' },
    { value: 'donation', label: 'Donations' },
    { value: 'user', label: 'User Actions' },
    { value: 'milestone', label: 'Milestones' }
  ];

  // Function to fetch activities
  const fetchActivities = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Get the ID token
      const session = await fetchAuthSession();
      const idToken = session.tokens?.idToken?.toString();
      if (!idToken) {
        throw new Error('No ID token found');
      }

      // Fetch activities using the ID token
      const allActivities = await institutionService.getRecentActivities(idToken);
      
      // Filter activities related to this campaign
      const campaignActivities = allActivities.filter(activity => 
        activity.entityId === campaignId || 
        activity.description.toLowerCase().includes(`campaign ${campaignId}`)
      );
      
      setActivities(campaignActivities);
    } catch (err) {
      console.error('Error fetching campaign activities:', err);
      const apiMessage = (err as any)?.response?.data?.message || '';
      const apiData = (err as any)?.response?.data?.data || {};
      let errorMessage = apiMessage || 'Failed to load activity log.';
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setError('Failed to load activity log. Please try again later.');
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  // Fetch activities on component mount
  useEffect(() => {
    fetchActivities();
  }, [campaignId, institutionId]);

  // Function to get icon based on activity type
  const getActivityIcon = (activity: Activity) => {
    const action = activity.action.toLowerCase();
    
    if (action.includes('create') || action.includes('campaign')) {
      return <FileText className="h-5 w-5 text-blue-500" />;
    } else if (action.includes('approve')) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (action.includes('reject') || action.includes('terminate')) {
      return <XCircle className="h-5 w-5 text-red-500" />;
    } else if (action.includes('user')) {
      return <UserPlus className="h-5 w-5 text-purple-500" />;
    } else if (action.includes('donation')) {
      return <DollarSign className="h-5 w-5 text-emerald-500" />;
    } else if (action.includes('milestone')) {
      return <Calendar className="h-5 w-5 text-amber-500" />;
    } else if (action.includes('update')) {
      return <RefreshCw className="h-5 w-5 text-indigo-500" />;
    } else {
      return <Bell className="h-5 w-5 text-gray-400" />;
    }
  };

  // Function to filter activities
  const filterActivities = () => {
    if (!activities.length) return [];
    
    let filtered = [...activities];
    
    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(activity => {
        const action = activity.action.toLowerCase();
        const entityType = activity.entityType?.toLowerCase() || '';
        
        switch (filterType) {
          case 'campaign':
            return action.includes('campaign') || entityType === 'campaign';
          case 'approval':
            return action.includes('approve') || action.includes('reject');
          case 'donation':
            return action.includes('donation') || entityType === 'donation';
          case 'user':
            return action.includes('user');
          case 'milestone':
            return action.includes('milestone') || entityType === 'milestone';
          default:
            return true;
        }
      });
    }
    
    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(activity => 
        activity.action.toLowerCase().includes(term) || 
        activity.description.toLowerCase().includes(term) ||
        (activity.user && activity.user.toLowerCase().includes(term))
      );
    }
    
    // Filter by date range
    if (dateRange.start) {
      const startDate = new Date(dateRange.start);
      filtered = filtered.filter(activity => new Date(activity.timestamp) >= startDate);
    }
    
    if (dateRange.end) {
      const endDate = new Date(dateRange.end);
      endDate.setHours(23, 59, 59); // Set to end of day
      filtered = filtered.filter(activity => new Date(activity.timestamp) <= endDate);
    }
    
    // Sort by timestamp
    filtered.sort((a, b) => {
      const dateA = new Date(a.timestamp).getTime();
      const dateB = new Date(b.timestamp).getTime();
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });
    
    return filtered;
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchActivities();
    toast.info('Refreshing activity log...');
  };

  // Toggle sort order
  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
  };

  // Reset all filters
  const resetFilters = () => {
    setFilterType('all');
    setSearchTerm('');
    setDateRange({ start: '', end: '' });
  };

  // Get filtered activities
  const filteredActivities = filterActivities();

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
          fetchActivities();
        }}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Campaign Activity Log</h2>
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            title="Toggle filters"
          >
            <Filter className="h-5 w-5" />
          </button>
          <button 
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            title="Refresh activities"
          >
            <RefreshCw className="h-5 w-5" />
          </button>
          <button 
            onClick={toggleSortOrder}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            title={sortOrder === 'desc' ? "Oldest first" : "Newest first"}
          >
            {sortOrder === 'desc' ? (
              <ChevronDown className="h-5 w-5" />
            ) : (
              <ChevronUp className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center gap-3 mb-3">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                {activityTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="flex flex-col md:flex-row md:items-center gap-3">
            <div className="flex-1 flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <label className="block text-sm text-gray-600 mb-1">From Date</label>
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange({...dateRange, start: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm text-gray-600 mb-1">To Date</label>
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange({...dateRange, end: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="self-end">
              <button
                onClick={resetFilters}
                className="px-3 py-2 text-sm text-blue-600 hover:text-blue-800"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Activity List */}
      <div className="overflow-y-auto max-h-96 border rounded-lg">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">Loading activities...</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
            <p className="text-red-500">{error}</p>
            <button
              onClick={handleRefresh}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Try Again
            </button>
          </div>
        ) : filteredActivities.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Clock className="h-8 w-8 text-gray-400 mb-2" />
            <p className="text-gray-500">No activities found</p>
            <p className="text-sm text-gray-400 mt-1">
              {searchTerm || filterType !== 'all' || dateRange.start || dateRange.end
                ? 'Try adjusting your filters'
                : 'Activities will appear here when there are updates to this campaign'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredActivities.map((activity) => (
              <div
                key={activity.id}
                className="flex items-start space-x-3 p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0 mt-1">
                  {getActivityIcon(activity)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.action}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">{activity.description}</p>
                  <div className="flex items-center mt-1 text-xs text-gray-400">
                    <span>{new Date(activity.timestamp).toLocaleString()}</span>
                    {activity.user && (
                      <>
                        <span className="mx-1">•</span>
                        <span>{activity.user}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default CampaignActivityLog;
