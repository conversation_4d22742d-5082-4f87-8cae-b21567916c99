import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Book, School, Calendar, Target, Users, Award, FileText,
  TrendingUp, Clock, Download, Share2, Bell, CheckCircle,
  Briefcase, GraduationCap, Heart, Edit2, Save, Plus, Trash2,
  BarChart3, DollarSign, UserCheck, Activity,
  BookOpen, FolderOpen, MessageSquare, ChevronRight,
  Globe, Linkedin, FileText as GithubIcon, Loader
} from 'lucide-react';
import { Pagination } from '@mui/material';
import { toast } from 'react-toastify';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import { useStudentAuth } from '../context/StudentAuthContext';
import studentService from '../services/studentService';
import campaignService from '../services/campaignService';
import { Student } from '../services/institutionService';
import { Campaign } from '../services/institutionService';
import externalStudentService from '../services/externalStudentService';
import { Info } from 'lucide-react';
import StatusPage from '../components/StatusPage';
import StudentSidebar from '../components/StudentSidebar';
import StudentHeader from '../components/StudentHeader';
import { StudentFooter } from '../components/StudentFooter';
import { getBasePathWithSlash } from '../utils/basePath';

interface Skill {
  name: string;
  level: string;
}

interface Update {
  title: string;
  content: string;
  date: string;
  type: 'achievement' | 'project' | 'academic';
}

interface Document {
  name: string;
  type: string;
  url: string;
}

interface StudentProfile {
  name: string;
  studentRegId: string;
  institution: string;
  course: string;
  email: string;
  phone: string;
  year: string;
  batch: string;
  story: string;
  updates: Update[];
  documents: Document[];
  socialLinks: {
    linkedin: string;
    github: string;
    portfolio: string;
  };
}

interface CampaignForm {
  title: string;
  description: string;
  targetAmount: string;
  duration: string;
  startDate: string;
  endDate: string;
  documents: File[];
  milestones: {
    title: string;
    dueDate: string;
  }[];
}

interface CampaignDetails {
  title: string;
  description: string;
  targetAmount: number;
  raisedAmount: number;
  status: 'active' | 'pending' | 'rejected' | 'completed' | 'terminated';
  startDate: string;
  endDate: string;
  rejectionReason?: string;
}

// Enhanced mock student profile data
const studentProfile: StudentProfile = {
  name: 'Saqib',
  studentRegId: 'ST2024001',
  institution: 'Islamaih College',
  course: 'BCA',
  email: '<EMAIL>',
  phone: '987612365',
  year: '3rd Year',
  batch: '2022-2025 BCA',
  story: `I am a passionate Computer Science student at Islamaih College, currently in my third year.
    With a strong academic record maintaining a 9.2 CGPA, I've consistently demonstrated excellence in both
    theoretical knowledge and practical applications. My journey in computer science began with winning
    several coding competitions, and I've already developed two mobile applications for local businesses.

    Coming from a humble background, I am determined to make the most of every opportunity to learn and grow.
    My goal is to become a software engineer and contribute to technological innovation that can make a
    positive impact on society.`,

  updates: [
    {
      title: 'Won National Coding Competition',
      content: 'Secured first place in the national coding championship organized by Google, competing against 500+ participants.',
      date: '2024-03-20',
      type: 'achievement'
    },
    {
      title: 'Mobile App Development Project',
      content: 'Successfully completed and deployed an e-commerce mobile app for a local business, resulting in 30% increase in their online sales.',
      date: '2024-03-15',
      type: 'project'
    },
    {
      title: 'Academic Excellence',
      content: 'Achieved highest grade in Advanced Algorithms course with special mention from the department.',
      date: '2024-03-10',
      type: 'academic'
    }
  ],
  documents: [
    { name: 'Aadhar Card', type: 'Identity', url: 'aadhar.pdf' },
    { name: 'Latest Marksheet', type: 'Academic', url: 'marksheet.pdf' },
    { name: 'Income Certificate', type: 'Financial', url: 'income_certificate.pdf' },
    { name: 'Fee Receipt', type: 'Financial', url: 'fee_receipt.pdf' },

  ],
  socialLinks: {
    linkedin: 'https://linkedin.com/in/saqib-khan',
    github: 'https://github.com/saqib-khan',
    portfolio: 'https://saqib-khan.dev'
  }
};

// Enhanced campaign data
const campaignData = {
  story: `I am a passionate Computer Science student at Delhi Public School,
  seeking support to continue my education. Coming from a humble background,
  I've maintained excellent academic performance with a 9.2 CGPA. My goal is
  to become a software engineer and contribute to technological innovation.

  Despite financial constraints, I've won several coding competitions and developed
  two mobile apps for local businesses. The funds will help cover my tuition fees,
  programming course certifications, and a laptop essential for my studies.

  Your support will help me pursue my dreams and create innovative solutions that
  can make a difference in society.`,
  targetAmount: 50000,
  raisedAmount: 35000,
  supporters: 12,
  daysLeft: 45,
  startDate: '2024-03-01',
  endDate: '2024-05-01',
  campaigns: [
    {
      id: 1,
      title: '2025-4-22-saqib-BCA-2nd Year-4th Semester',
      targetAmount: 30000,
      raisedAmount: 20000,
      startDate: '2024-03-01',
      endDate: '2024-06-30',
      status: 'active',
      description: 'Seeking support for completing my final year tuition fees.',
      rejectionReason: null
    },
    {
      id: 2,
      title: '2024-6-21-saqib-BCA-2nd Year-3th Semester',
      targetAmount: 20000,
      raisedAmount: 15000,
      startDate: '2024-04-01',
      endDate: '2024-07-31',
      status: 'active',
      description: 'Funding required for AI research project materials.',
      rejectionReason: null
    },
    {
      id: 3,
      title: '2025-4-21-saqib-BCA-1nd Year-2th Semester',
      targetAmount: 45000,
      raisedAmount: 0,
      startDate: '2024-05-01',
      endDate: '2024-08-31',
      status: 'rejected',
      description: 'Need a laptop for advanced programming courses.',
      rejectionReason: 'Insufficient documentation provided.'
    },
    {
      id: 4,
      title: 'Conference Attendance Fund',
      targetAmount: 25000,
      raisedAmount: 0,
      startDate: '2024-06-01',
      endDate: '2024-09-30',
      status: 'pending',
      description: 'Support needed to attend International Tech Conference.',
      rejectionReason: null
    }
  ],
  // Milestones section commented out
  /*
  milestones: [
    { title: 'First Semester Fees', amount: 15000, status: 'completed' },
    { title: 'Laptop Purchase', amount: 20000, status: 'in-progress' },
    { title: 'Programming Courses', amount: 15000, status: 'pending' }
  ],
  */
  documents: [
    { name: 'Fee Structure', file: 'fee_structure.pdf', type: 'Financial' },
    { name: 'Academic Records', file: 'academic_records.pdf', type: 'Academic' },
    { name: 'Income Certificate', file: 'income_certificate.pdf', type: 'Financial' },
    { name: 'Achievement Certificates', file: 'achievements.pdf', type: 'Achievement' },
    { name: 'Project Portfolio', file: 'portfolio.pdf', type: 'Project' }
  ],
  updates: [
    {
      date: '2024-03-20',
      title: 'Won National Coding Competition',
      content: 'Secured first place in national level coding competition organized by Google',
      type: 'achievement'
    },
    {
      date: '2024-03-15',
      title: 'Project Milestone Achieved',
      content: 'Completed the MVP of my final year project - AI-powered Education Platform',
      type: 'project'
    },
    {
      date: '2024-03-10',
      title: 'Research Paper Accepted',
      content: 'My research paper on ML applications in education got accepted in IEEE conference',
      type: 'academic'
    }
  ],
 
  achievements: [
    {
      title: 'National Merit Scholar 2023',
      description: 'Awarded for outstanding academic performance',
      date: '2023-12-15'
    },
    {
      title: 'Best Student Innovation Award',
      description: 'Developed AI-based attendance system',
      date: '2024-01-20'
    },
    {
      title: 'Google Developer Student Club Lead',
      description: 'Leading a community of 200+ student developers',
      date: '2023-08-01'
    }
  ],
  testimonials: [
    {
      name: 'Dr. Zainab Khan',
      role: 'HOD, Computer Science',
      content: 'Saqib is one of our brightest students with exceptional problem-solving skills.'
    },
    {
      name: 'Mr. Farhan Ahmed',
      role: 'Project Mentor',
      content: 'His dedication and innovative thinking sets him apart from others.'
    }
  ]
};

// Enhanced donation history
const donationHistory = [
  {
    id: 1,
    supporter: 'Omar Farooq',
    amount: 5000,
    date: '2024-03-15',
    message: 'Keep up the great work! Your project looks promising.',
    isAnonymous: false
  },
  {
    id: 2,
    supporter: 'Anonymous',
    amount: 3000,
    date: '2024-03-14',
    message: 'All the best for your future!',
    isAnonymous: true
  },
  {
    id: 3,
    supporter: 'Aisha Rahman',
    amount: 10000,
    date: '2024-03-12',
    message: 'Your dedication is inspiring. Wishing you success!',
    isAnonymous: false
  }
];

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};

const formatCampaignTitle = (campaign: any) => {
  const today = new Date();
  const formattedDate = today.toISOString().split('T')[0];
  return `${formattedDate}-${studentProfile.name}-${studentProfile.course}-${studentProfile.class}-${getCurrentSemester()}`;
};

const getCurrentSemester = () => {
  const yearMap = {
    '1st Year': ['1st Semester', '2nd Semester'],
    '2nd Year': ['3rd Semester', '4th Semester'],
    '3rd Year': ['5th Semester', '6th Semester'],
    '4th Year': ['7th Semester', '8th Semester']
  };

  const currentMonth = new Date().getMonth() + 1;
  const semesterIndex = currentMonth >= 7 ? 0 : 1;
  return yearMap[studentProfile.class as keyof typeof yearMap][semesterIndex];
};

// Update the campaign display in the campaigns section
const renderCampaignTitle = (campaign: any) => {
  return (
    <div className="text-sm font-medium text-gray-900">
      {formatCampaignTitle(campaign)}
    </div>
  );
};

export default function StudentPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useStudentAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isEditing, setIsEditing] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [campaignFilter, setCampaignFilter] = useState('all');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // State for API data
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [studentData, setStudentData] = useState<Student | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [campaignPage, setCampaignPage] = useState(1); // Pagination current page (1-indexed)
  const [campaignPageSize, setCampaignPageSize] = useState(10);
  const [totalCampaignPages, setTotalCampaignPages] = useState(0);
  const [totalCampaignElements, setTotalCampaignElements] = useState(0);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
  const [operationType, setOperationType] = useState<'profile' | 'update'>('profile');
  const [batchYearError, setBatchYearError] = useState<string | null>(null);
const [editedProfile, setEditedProfile] = useState<{
  year: string;
  name: string;
  studentRegId: string;
  institution: string;
  course: string;

  batch: string;
  startYear: string;
  endYear: string;
  email: string;
  phone: string;
  story: string;
}>({
  name: '',
  studentRegId: '',
  institution: '',
  course: '',
  year: '',
  batch: '',
  startYear: '',
  endYear: '',
  email: '',
  phone: '',
  story: ''
});

  const [totalRaised, setTotalRaised] = useState(0);
  const [activeCampaignsCount, setActiveCampaignsCount] = useState(0);
  const [documents, setDocuments] = useState<File[]>([]);

  // Add toggle function
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Parse URL query parameters to set the active tab
  const getInitialTab = (): string => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');

    if (tabParam === 'dashboard' || tabParam === 'profile') {
      return tabParam;
    }
    return "dashboard";
  };

useEffect(() => {
  const fetchProfile = async () => {
    try {
      const profile = await externalStudentService.getStudentProfile();

     const match = profile.batch?.match(/(\d{4})-(\d{4})/);
     setEditedProfile({
  name: profile.name || '',
  studentRegId: profile.studentRegId || '',
  institution: profile.institution || '', // ✅ fixed
  course: profile.course || '',
  year: profile.year || '', // `year` from API is equivalent to `class` in UI
  batch: profile.batch || '',
  startYear: match ? match[1] : '',
  endYear: match ? match[2] : '',
  email: profile.email || user?.email || '', // Use email from profile first
  phone: profile.phone || '',
  story: profile.academicRecord || ''
});

    } catch (error: any) {
      console.error('Error loading profile:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage || 'Failed to load profile info.';
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, err]) => `• ${field}: ${err}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    }
  };

  fetchProfile();
}, []);

  // Fetch student data and campaigns
useEffect(() => {
  const fetchStudentDashboard = async () => {
    setLoading(true);
    try {
      const dashboard = await externalStudentService.getStudentDashboard();
      setTotalRaised(dashboard.totalRaisedAmount);
      setActiveCampaignsCount(dashboard.activeCampaignCount);
    } catch (err: any) {
      console.error('Dashboard error:', err);
      const apiMessage = err.response?.data?.message || '';
      const apiData = err.response?.data?.data || {};
      let errorMessage = apiMessage || 'Failed to load dashboard';
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    }

    try {
      // Convert filter to API status parameter
      const statusParam = campaignFilter === 'all' ? undefined : campaignFilter.toUpperCase();
      const campaignsData = await externalStudentService.getStudentCampaigns(
        campaignPage - 1,
        campaignPageSize,
        statusParam,
        'desc'
      );
      setCampaigns(campaignsData.content);
      setTotalCampaignPages(campaignsData.totalPages);
      setTotalCampaignElements(campaignsData.totalElements);
    } catch (err: any) {
      console.error('Campaigns error:', err);
      const apiMessage = err.response?.data?.message || '';
      const apiData = err.response?.data?.data || {};
      let errorMessage = apiMessage || 'Failed to load campaigns';
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  fetchStudentDashboard();
}, [campaignPage, campaignPageSize, campaignFilter]);



  // Check if we're returning from the add-update page with a specific tab to show
  // or if we have a tab parameter in the URL
  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setActiveTab(location.state.activeTab);
    } else {
      setActiveTab(getInitialTab());
    }
  }, [location.state, location.search]);


  const handleSignOut = () => {
    // Use the logout function from context which handles all cleanup
    logout('/student-login');

    // Show a success message
    toast.success('Successfully signed out');

    // No need for immediate redirect as the logout function will handle it
    // The context logout function will properly handle Google sign-out
  };




  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb
        items={getBreadcrumbItems('student-dashboard')}
      />

      {renderMetrics()}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-4">
          {renderActiveCampaigns()}
          {renderAcademicProgress()}
        </div>

      </div>
    </div>
  );

  // Academic Progress section commented out
  const renderAcademicProgress = () => (
    <div className="hidden">
      {/* Academic Progress section commented out
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Academic Progress</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span>CGPA</span>
            <span className="font-semibold">9.2</span>
          </div>
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Course Completion</span>
              <span>75%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full w-3/4" />
            </div>
          </div>
        </div>
      </div>
      */}
    </div>
  );



  const renderDocuments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Documents</h2>
        <button
          onClick={() => setShowDocumentModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Upload Document
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {studentProfile.documents.map((doc, idx) => (
          <div key={idx} className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center justify-between mb-2">
              <FileText className="h-6 w-6 text-blue-500" />
              <span className="text-sm bg-blue-50 text-blue-600 px-2 py-1 rounded">
                {doc.type}
              </span>
            </div>
            <h3 className="font-medium mb-2">{doc.name}</h3>
            <div className="flex justify-end space-x-2">
              <button
                className="text-blue-600 hover:text-blue-800"
                onClick={() => {
                  // Create a mock document for download
                  const mockFileName = doc.name.replace(/\s+/g, '_').toLowerCase() + '.pdf';
                  const blob = new Blob(['Mock document content for ' + doc.name], { type: 'application/pdf' });
                  const url = URL.createObjectURL(blob);

                  // Create a temporary link and trigger download
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = mockFileName;
                  document.body.appendChild(a);
                  a.click();

                  // Clean up
                  setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }, 100);

                  toast.success(`Downloaded ${doc.name}`);
                }}
              >
                <Download className="h-4 w-4" />
              </button>
              <button className="text-red-600 hover:text-red-800">
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderProfile = () => {
    // Show loading state
    if (loading && !editedProfile) {
      return (
        
        <div className="space-y-6">
          <Breadcrumb items={getBreadcrumbItems('student-profile')} />
          <div className="bg-white rounded-lg shadow p-6 flex justify-center items-center h-64">
            <div className="flex flex-col items-center">
              <Loader className="h-8 w-8 text-blue-500 animate-spin mb-2" />
              <p className="text-gray-600">Loading profile...</p>
            </div>
          </div>
        </div>
      );
    }

    // Show error state
    if (error && !editedProfile) {
      return (
        <div className="space-y-6">
          <Breadcrumb items={getBreadcrumbItems('student-profile')} />
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center p-6">
              <p className="text-red-500 mb-2">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb
          items={getBreadcrumbItems('student-profile')}
        />

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Profile Information</h2>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="text-blue-600 hover:text-blue-800"
            >
              <Edit2 className="h-5 w-5" />
            </button>
          </div>
          {isEditing ? (
          

<form onSubmit={handleSaveProfile} className="space-y-4">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
  <label className="block text-sm font-medium mb-1">Name</label>
  <input
    type="text"
    value={editedProfile?.name || ''}
    className="w-full border rounded-lg px-3 py-2 bg-gray-100 text-gray-500 cursor-not-allowed"
    disabled
    title="This field is not editable"
  />
</div>


    <div>
      <label className="block text-sm font-medium mb-1 flex items-center gap-1">
        Student Reg ID
      
      </label>
      <input
        type="text"
        value={editedProfile?.studentRegId || ''}
        className="w-full border rounded-lg px-3 py-2 bg-gray-100 text-gray-500 cursor-not-allowed"
        disabled
         title="This field is not editable"
      />
    </div>
    <div>
      <label className="block text-sm font-medium mb-1 flex items-center gap-1">
        Institution
      
      </label>
      <input
        type="text"
        value={editedProfile?.institution || ''}
        className="w-full border rounded-lg px-3 py-2 bg-gray-100 text-gray-500 cursor-not-allowed"
        disabled
         title="This field is not editable"
      />
    </div>
    <div>
      <label className="block text-sm font-medium mb-1">Course</label>
      <input
        type="text"
        value={editedProfile?.course || ''}
        className="w-full border rounded-lg px-3 py-2"
        onChange={(e) => setEditedProfile({ ...editedProfile, course: e.target.value })}
      />
    </div>
    <div>
      <label className="block text-sm font-medium mb-1">Year</label>
      <input
        type="text"
        value={editedProfile?.year || ''}
        className="w-full border rounded-lg px-3 py-2"
        onChange={(e) => setEditedProfile({ ...editedProfile, year: e.target.value })}
      />
    </div>
  <div>
  <label className="block text-sm font-medium mb-1">Batch Year Range</label>
  <div className="flex gap-2">
    <select
      className="w-1/2 border rounded-lg px-3 py-2"
      value={editedProfile?.startYear || ''}
      onChange={(e) => {
        const startYear = e.target.value;
        const endYear = editedProfile?.endYear || '';
        let error: string | null = null;
        if (endYear && parseInt(endYear) < parseInt(startYear)) {
          error = 'End year must be greater than or equal to start year';
        }
        setBatchYearError(error);
        setEditedProfile({
          ...editedProfile,
          startYear,
          batch: endYear ? `${startYear}-${endYear}` : '',
        });
      }}
    >
      <option value="">Start Year</option>
      {Array.from({ length: 10 }, (_, i) => {
        const year = new Date().getFullYear() - 5 + i;
        return (
          <option key={year} value={year}>
            {year}
          </option>
        );
      })}
    </select>

    <select
      className="w-1/2 border rounded-lg px-3 py-2"
      value={editedProfile?.endYear || ''}
      onChange={(e) => {
        const endYear = e.target.value;
        const startYear = editedProfile?.startYear || '';
        let error: string | null = null;
        if (startYear && parseInt(endYear) < parseInt(startYear)) {
          error = 'End year must be greater than or equal to start year';
        }
        setBatchYearError(error);
        setEditedProfile({
          ...editedProfile,
          endYear,
          batch: startYear ? `${startYear}-${endYear}` : '',
        });
      }}
    >
      <option value="">End Year</option>
      {Array.from({ length: 10 }, (_, i) => {
        const year = new Date().getFullYear() - 5 + i;
        return (
          <option key={year} value={year}>
            {year}
          </option>
        );
      })}
    </select>
  </div>
  {batchYearError && (
    <p className="text-red-600 text-sm mt-1">{batchYearError}</p>
  )}
</div>

    <div>
      <label className="block text-sm font-medium mb-1 flex items-center gap-1">
        Email
      
      </label>
      <input
        type="email"
        value={editedProfile?.email || ''}
        className="w-full border rounded-lg px-3 py-2 bg-gray-100 text-gray-500 cursor-not-allowed"
        disabled
         title="This field is not editable"
      />
    </div>
    <div>
      <label className="block text-sm font-medium mb-1">Phone</label>
      <input
        type="tel"
        value={editedProfile?.phone || ''}
        className="w-full border rounded-lg px-3 py-2"
        onChange={(e) => setEditedProfile({ ...editedProfile, phone: e.target.value })}
      />
    </div>
  </div>

  <div className="flex justify-end space-x-3">
    <button
      type="button"
      onClick={() => setIsEditing(false)}
      className="px-4 py-2 border rounded-lg"
      disabled={loading}
    >
      Cancel
    </button>
    <button
      type="submit"
      className="px-4 py-2 bg-blue-600 text-white rounded-lg flex items-center"
      disabled={loading || !!batchYearError}
    >
      {loading ? (
        <>
          <Loader className="h-4 w-4 animate-spin mr-2" />
          Saving...
        </>
      ) : (
        'Save Changes'
      )}
    </button>
  </div>
</form>

          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-600">Name</label>
                  <p className="font-medium">{editedProfile?.name || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Student ID</label>
                  <p className="font-medium">{editedProfile?.studentRegId || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Institution</label>
                  <p className="font-medium">{editedProfile?.institution || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Course</label>
                  <p className="font-medium">{editedProfile?.course || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Year</label>
                  <p className="font-medium">{editedProfile?.year  || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Batch</label>
                  <p className="font-medium">{editedProfile?.batch || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Email</label>
                  <p className="font-medium">{editedProfile?.email || ''}</p>
                </div>
                <div>
                  <label className="block text-sm text-gray-600">Phone</label>
                  <p className="font-medium">{editedProfile?.phone || ''}</p>
                </div>
              </div>
              
            </div>
          )}
        </div>

      
        {/* Documents section */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Documents</h3>
          <input
            id="document-upload"
            type="file"
            multiple
            onChange={handleDocumentChange}
            className="mb-4"
          />
          {documents.length > 0 ? (
            <ul className="list-disc pl-5 space-y-1">
              {documents.map((file, idx) => (
                <li key={idx} className="text-gray-700">
                  {file.name}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-600">No documents uploaded yet.</p>
          )}
        </div>
      </div>
    );
  };

  const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setDocuments((prev) => [...prev, ...Array.from(e.target.files)]);
    }
  };

  // Handle saving profile changes
const handleSaveProfile = async (e: React.FormEvent) => {
  e.preventDefault();

  if (!user) {
    toast.error("User not authenticated");
    return;
  }

  try {
    setLoading(true);
    setOperationType('profile');

    // ✅ Send all edited profile fields
    const updateData = { ...editedProfile };

    await externalStudentService.updateStudentProfile(updateData);

    setStatus('success');
    setIsEditing(false);

    // Refresh and re-set profile data
    const updated = await externalStudentService.getStudentProfile();
    setStudentData(updated);
    const updatedMatch = updated.batch?.match(/(\d{4})-(\d{4})/);
    setEditedProfile({
      ...editedProfile,
      ...updated,
      startYear: updatedMatch ? updatedMatch[1] : '',
      endYear: updatedMatch ? updatedMatch[2] : '',
      story: updated.academicRecord || ''
    });

  } catch (err) {
    console.error('Error updating profile:', err);
    setStatus('error');
  } finally {
    setLoading(false);
  }
};




  const renderMetrics = () => {
    // Calculate days left for the closest active campaign
    const activeCampaign = campaigns.find(c =>
      c.status === 'ACTIVE' || c.status === 'active'
    );

    let daysLeft = 0;
    if (activeCampaign && activeCampaign.endDate) {
      const endDate = new Date(activeCampaign.endDate);
      const today = new Date();
      const diffTime = endDate.getTime() - today.getTime();
      daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      daysLeft = daysLeft > 0 ? daysLeft : 0;
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-6">
        {[
          {
            title: 'Total Raised',
            value: `₹${totalRaised}`,
          
            color: 'bg-green-50'
          },
          {
            title: 'Active Campaigns',
            value: activeCampaignsCount,
         
            color: 'bg-blue-50'
          }
        ].map((metric, idx) => (
          <div key={idx} className={`${metric.color} p-4 md:p-6 rounded-lg`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">{metric.title}</p>
                <p className="text-xl md:text-2xl font-bold mt-1">{metric.value}</p>
              </div>
              {metric.icon}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderActiveCampaigns = () => {
    // Show loading state
    if (loading) {
      return (
        <div className="bg-white rounded-lg shadow p-4 md:p-6 mb-6 flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <Loader className="h-8 w-8 text-blue-500 animate-spin mb-2" />
            <p className="text-gray-600">Loading campaigns...</p>
          </div>
        </div>
      );
    }

    // Show error state
    if (error) {
      return (
        <div className="bg-white rounded-lg shadow p-4 md:p-6 mb-6">
          <div className="text-center p-6">
            <p className="text-red-500 mb-2">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    // Campaigns are filtered by the API, but we keep this for consistency
    const filteredCampaigns = campaigns;

    return (
      <div className="bg-white rounded-lg shadow p-4 md:p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-4 space-y-4 md:space-y-0">
          <h2 className="text-lg font-semibold">Campaigns</h2>
          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
            <select
              value={campaignFilter}
              onChange={(e) => setCampaignFilter(e.target.value)}
              className="border rounded-lg px-3 py-2"
            >
              <option value="all">All Campaigns</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
              <option value="completed">Completed</option>
              <option value="terminated">Terminated</option>
            </select>
            <button
              onClick={() => navigate('/student/request-campaign')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span className="whitespace-nowrap">Request New Campaign</span>
            </button>
          </div>
        </div>

        {filteredCampaigns.length === 0 ? (
          <div className="text-center p-6 border rounded-lg">
            <p className="text-gray-500 mb-2">
              {campaignFilter === 'all' 
                ? 'No campaigns found' 
                : `No ${campaignFilter} campaigns found`}
            </p>
            <button
              onClick={() => navigate('/student/request-campaign')}
              className="text-blue-600 hover:text-blue-800"
            >
              {campaignFilter === 'all' || totalCampaignElements === 0
                ? 'Create New Campaign'
                : 'Create New Campaign'}
            </button>
          </div>
        ) : (
          <>
            <div className="space-y-4">
              {filteredCampaigns.map((campaign) => (
              <div
                key={campaign.id}
                className="border rounded-lg p-4 cursor-pointer hover:border-blue-500"
                onClick={() => {
                  navigate(`/student/campaign/${campaign.id}`);
                }}
              >
                <div className="flex flex-col sm:flex-row justify-between sm:items-start">
                  <div className="mb-2 sm:mb-0">
                    <h3 className="font-medium break-words">{campaign.title}</h3>
                    <p className="text-sm text-gray-600">
                      Target: ₹{campaign.goalAmount || campaign.targetAmount || 0}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs inline-block sm:ml-2 ${
                    campaign.status.toLowerCase() === 'active'
                      ? 'bg-green-100 text-green-800'
                      : campaign.status.toLowerCase() === 'pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : campaign.status.toLowerCase() === 'completed'
                      ? 'bg-blue-100 text-blue-800'
                      : campaign.status.toLowerCase() === 'terminated'
                      ? 'bg-gray-100 text-gray-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1).toLowerCase()}
                  </span>
                </div>
                {(campaign.status.toLowerCase() === 'active') && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${Math.min(
                            100,
                            Math.round(((campaign.raisedAmount || 0) / (campaign.goalAmount || campaign.targetAmount || 1)) * 100)
                          )}%`
                        }}
                      />
                    </div>
                    <div className="flex justify-between mt-1 text-sm text-gray-600">
                      <span>Raised: ₹{campaign.raisedAmount || 0}</span>
                      <span>
                        {Math.round(((campaign.raisedAmount || 0) / (campaign.goalAmount || campaign.targetAmount || 1)) * 100)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
            </div>
            <div className="flex justify-center mt-4">
              <Pagination
                count={totalCampaignPages}
                page={campaignPage}
                onChange={(_, value) => setCampaignPage(value)}
                color="primary"
              />
            </div>
          </>
        )}
      </div>
    );
  };

  // Updates section removed
  const renderUpdates = () => (
    <div className="hidden">
      {/* Updates section commented out
      <div className="bg-white rounded-lg shadow p-4 md:p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Recent Updates</h2>
          <button
            onClick={() => navigate('/student/add-update')}
            className="text-blue-600 hover:text-blue-800 text-sm px-3 py-1 border border-blue-600 rounded-lg"
          >
            Add Update
          </button>
        </div>
        <div className="space-y-4">
          {studentProfile.updates.map((update, idx) => (
            <div key={idx} className="border-l-4 border-blue-500 pl-4 py-2">
              <div className="flex flex-wrap items-center text-sm text-gray-600 mb-1">
                <div className="flex items-center mr-2 mb-1 sm:mb-0">
                  <Calendar className="h-4 w-4 mr-2" />
                  {update.date}
                </div>
                <span className="hidden sm:inline mx-2">•</span>
                <span className="capitalize">{update.type}</span>
              </div>
              <h3 className="font-medium">{update.title}</h3>
              <p className="text-gray-600 text-sm mt-1">{update.content}</p>
            </div>
          ))}
        </div>
      </div>
      */}
    </div>
  );





  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title={operationType === 'profile' ? "Profile Updated Successfully" : "Update Added Successfully"}
        message={operationType === 'profile' ? "Your profile information has been saved and updated successfully." : "Your update has been added to your campaign successfully."}
        actionText="Continue"
        onAction={() => setStatus('idle')}
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={
          errorDetails?.message ||
          (operationType === 'profile'
            ? "We couldn't save your profile changes. Please try again."
            : "We couldn't add your update. Please try again.")
        }
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StudentHeader />
      <div className="flex ">
        <StudentSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab={activeTab as 'dashboard' | 'profile'}
          onSignOut={handleSignOut}
          name={editedProfile?.name}
          studentRegId={editedProfile?.studentRegId}
          onSelectTab={setActiveTab}
        />
        <div className="flex-1 p-4 md:p-8 md:ml-64">
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'profile' && renderProfile()}
        </div>
      </div>
      <StudentFooter />
    </div>
  );
}















