/**
 * Get the base path from environment variable
 * Falls back to '${import.meta.env.VITE_BASE_PATH}' if not set
 */
export const getBasePath = (): string => {
  const baseUrl = import.meta.env.VITE_BASE_URL;
  if (baseUrl) {
    try {
      const url = new URL(baseUrl);
      return url.pathname.endsWith('/') ? url.pathname.slice(0, -1) : url.pathname;
    } catch {
      return '${import.meta.env.VITE_BASE_PATH}';
    }
  }
  return '${import.meta.env.VITE_BASE_PATH}';
};

/**
 * Get the base path with trailing slash
 */
export const getBasePathWithSlash = (): string => {
  const basePath = getBasePath();
  return basePath.endsWith('/') ? basePath : `${basePath}/`;
};