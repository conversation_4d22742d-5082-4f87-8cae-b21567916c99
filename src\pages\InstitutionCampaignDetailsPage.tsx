import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  ChevronLeft,
  Calendar,
  Edit2,
  GraduationCap,
  LogOut,
  Save,
  X,
  Check,
  AlertTriangle,
  XCircle,
  Clock,
  Eye,
  BarC<PERSON>3,
  Trash2,
} from "lucide-react";
import { toast } from "react-toastify";
import { InstitutionFooter } from "../components/InstitutionFooter";
import { useAuth as useOidcAuth } from "react-oidc-context";
import { useAuth } from "../context/AuthContext";
import institutionService, {
  CampaignDetails,
} from "../services/institutionService";
import CampaignClosureInfo from "../components/CampaignClosureInfo";
import TerminateCampaignModal from "../components/TerminateCampaignModal";
import ViewCounter from "../components/ViewCounter";
import CampaignViewTracker from "../components/CampaignViewTracker";
import Breadcrumb from "../components/Breadcrumb";
import { getBreadcrumbItems } from "../utils/breadcrumbUtils";
import approvalService from "../services/approvalService";
import StatusPage from "../components/StatusPage";
import InstitutionSidebar from "../components/InstitutionSidebar";
import InstitutionHeader from "../components/InstitutionHeader";
import feedbackService from "../services/feedbackService";
import { formatDateTime } from "../utils/dateUtils";
import cognitoService from "../services/cognitoService";

interface RolePermissions {
  manageCampaigns: boolean;
  manageApprovals: boolean;
  manageInstitutionProfile: boolean;
  manageBankDetails: boolean;
  manageUsers: boolean;
  downloadReports: boolean;
}

export function InstitutionCampaignDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();

  const safeNavigate = (path: string | number) => {
    try {
      if (typeof path === "number") {
        navigate(path);
      } else {
        navigate(path);
      }
    } catch (error) {
      console.error("Navigation error:", error);
      navigate("/institution-dashboard");
    }
  };
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const auth = useOidcAuth(); // Get auth from react-oidc-context
  const { isAuthenticated, user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editedCampaign, setEditedCampaign] = useState<CampaignDetails | null>(
    null
  );
  const [showTerminateModal, setShowTerminateModal] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [canTerminate, setCanTerminate] = useState(false);
  const [canEditCampaign, setCanEditCampaign] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [status, setStatus] = useState<"idle" | "success" | "error">("idle");
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(
    null
  );
  const [wordsOfSupport, setWordsOfSupport] = useState("");
  const [replyText, setReplyText] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [supportMessages, setSupportMessages] = useState<
    Array<{
      id: string;
      name: string;
      email: string;
      message: string;
      date: string;
      replies?: Array<{
        id: string;
        name: string;
        email: string;
        message: string;
        date: string;
      }>;
    }>
  >([]);

  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  // Authentication check
  useEffect(() => {
    // Skip authentication check in development mode when using debug route
    if (import.meta.env.DEV && window.location.pathname.includes("/debug/")) {
      console.log("Debug mode: Bypassing authentication check");
      return;
    }

    // Regular authentication check for production
    const token = localStorage.getItem("id_token");
    const isLoggedIn = auth.isAuthenticated || isAuthenticated || !!token;

    console.log("Authentication check:", {
      "auth.isAuthenticated": auth.isAuthenticated,
      isAuthenticated: isAuthenticated,
      "token exists": !!token,
      isLoggedIn: isLoggedIn,
    });

    if (!isLoggedIn) {
      console.log("No authentication detected, redirecting to login");
      toast.error("Please log in to view campaign details");
      navigate("/institution-login");
    }
  }, [isAuthenticated, auth.isAuthenticated, navigate]);

  useEffect(() => {
    if (!id) return;

    const fetchFeedback = async () => {
      try {
        const feedbackList = await feedbackService.getFeedback(id, auth);

        // Build a map of messages by ID to create a nested reply structure
        const byId: Record<string, any> = {};
        const topLevel: any[] = [];

        feedbackList.forEach((fb: any) => {
          byId[fb.id] = {
            id: String(fb.id),
            name: fb.authorName ?? fb.name ?? "Institution",
            email: fb.authorEmail ?? fb.email ?? "",
            message: fb.content ?? fb.message ?? "",
            date: formatDateTime(fb.createdAt),
            replies: [] as any[],
          };
        });

        feedbackList.forEach((fb: any) => {
          if (fb.parentId && byId[fb.parentId]) {
            byId[fb.parentId].replies.push(byId[fb.id]);
          } else if (!fb.parentId) {
            topLevel.push(byId[fb.id]);
          }
        });

        setSupportMessages(topLevel);
      } catch (err) {
        console.error("Error fetching feedback:", err);
      }
    };

    fetchFeedback();
  }, [id]);

  // Set permissions based on user role
  useEffect(() => {
    const setUserPermissions = () => {
      try {
        // Get role from localStorage
        const storedRole = localStorage.getItem("institution_role");
        const storedRoles = localStorage.getItem("institution_roles");

        console.log("Role check:", {
          storedRole,
          storedRoles,
          "localStorage keys": Object.keys(localStorage),
        });

        let userRole = "";

        // First try to get the specific institution_role
        if (storedRole) {
          userRole = storedRole;
        }
        // If not available, try to parse the roles array
        else if (storedRoles) {
          try {
            const userRolesList = JSON.parse(storedRoles);
            if (Array.isArray(userRolesList) && userRolesList.length > 0) {
              userRole = userRolesList[0];
            }
          } catch (e) {
            console.error("Error parsing stored roles:", e);
          }
        }

        console.log("Determined user role:", userRole);

        // Check if the role is admin (case insensitive)
        const isAdmin =
          userRole &&
          (userRole.toUpperCase() === "INSTITUTION_ADMIN" ||
            userRole.toUpperCase() === "ADMIN" ||
            userRole.includes("ADMIN"));

        console.log("Is admin check:", isAdmin);

        // Set permissions based on role
        setCanEdit(isAdmin);
        setCanTerminate(isAdmin);

        console.log("Permissions set:", {
          canEdit: isAdmin,
          canTerminate: isAdmin,
        });
      } catch (error) {
        console.error("Error setting user permissions:", error);
        // Default to no permissions on error
        setCanEdit(false);
        setCanTerminate(false);
      }
    };

    setUserPermissions();
  }, []);

  // Fetch campaign details
  useEffect(() => {
    const fetchCampaignDetails = async () => {
      if (!id) {
        toast.error("Campaign ID is missing");
        navigate("/institution-dashboard");
        return;
      }

      setIsLoading(true);
      setApiError(null);

      try {
        // 🔍 Debug: Log authentication state before API call
        console.log("📌 Authentication state before API call:", {
          "auth.isAuthenticated": auth.isAuthenticated,
          isAuthenticated: isAuthenticated,
          "token exists": !!localStorage.getItem("id_token"),
          "user exists": !!user,
        });

        // 🔍 Debug: Log ID before calling API
        console.log("📌 Fetching campaign by ID:", id);

        // Try to get campaign details using the auth object
        const raw = await institutionService.getCampaignById(id, auth);

        console.log("📌 Campaign data received:", raw);

        const campaignDetails: CampaignDetails = {
          ...raw,
          campaignTitle: raw.title,
          targetAmount: raw.goalAmount,
          studentDetails: {
            email: raw.studentEmail || "",
            course: raw.studentCourse || "",
            year: raw.studentYear || "",
            phone: raw.studentPhone || "",
            department: raw.studentDepartment || "",
            batch: raw.studentBatch || "",
          },
        };

        setCampaign(campaignDetails);
        setEditedCampaign(campaignDetails);

        // Only allow editing for pending campaigns
        setCanEditCampaign(campaignDetails.status?.toUpperCase() === "PENDING");
      } catch (error: any) {
        console.error("❌ Error fetching campaign details:");

        if (error.response) {
          console.error("📦 Response data:", error.response.data);
          console.error("📡 Status:", error.response.status);
          console.error("🧾 Headers:", error.response.headers);
        } else if (error.request) {
          console.error("📭 No response received:", error.request);
        } else {
          console.error("🛠️ General error:", error.message);
        }

        setApiError("Failed to load campaign details");
        toast.error("Failed to load campaign details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCampaignDetails();
  }, [id, auth, isAuthenticated, user, navigate]);

  const handleSignOut = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("id_token");
    sessionStorage.removeItem("token");
    sessionStorage.removeItem("user");
    sessionStorage.removeItem("id_token");

    toast.success("Successfully signed out");

    cognitoService.redirectToLogout();
  };

  const handleTerminateCampaign = async (reason: string) => {
    if (!id || !campaign) return;

    try {
      setIsLoading(true);

      // Call the API to terminate the campaign using approvalService with react-oidc-context auth
      await approvalService.updateApprovalRequest(
        id,
        {
          id: id,
          status: "TERMINATED",
          reason: reason,
        },
        auth
      ); // Pass react-oidc-context auth object

      // Close the modal and show success status immediately after successful API call
      setShowTerminateModal(false);
      setStatus("success");
      setErrorDetails({ message: "Campaign terminated successfully" });

      // Try to refresh campaign details, but don't fail if this doesn't work
      try {
        const updatedCampaign = await institutionService.getCampaignDetails(
          id,
          true
        );
        setCampaign(updatedCampaign);
        setEditedCampaign(updatedCampaign);
      } catch (refreshError) {
        console.error(
          "Error refreshing campaign data after termination:",
          refreshError
        );
        // Don't change the success status even if refresh fails
      }
    } catch (error) {
      console.error("Error terminating campaign:", error);

      // Show error status page
      setStatus("error");
      setErrorDetails({
        message: "Failed to terminate campaign. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteFeedback = async (feedbackId: string) => {
    if (!id) return;
    try {
      await feedbackService.deleteFeedback(feedbackId, auth);
      setSupportMessages((prev) => prev.filter((msg) => msg.id !== feedbackId));
      toast.success("Message deleted");
    } catch (err) {
      console.error("Error deleting feedback:", err);
      toast.error("Failed to delete message");
    }
  };

  const handleDeleteReply = async (parentId: string, replyId: string) => {
    if (!id) return;
    try {
      await feedbackService.deleteFeedback(replyId, auth);
      setSupportMessages((prev) =>
        prev.map((msg) =>
          msg.id === parentId
            ? { ...msg, replies: msg.replies?.filter((r) => r.id !== replyId) }
            : msg
        )
      );
      toast.success("Message deleted");
    } catch (err) {
      console.error("Error deleting reply:", err);
      toast.error("Failed to delete message");
    }
  };

  const handlePostReply = async (messageId: string) => {
    if (!id || !replyText.trim()) return;
    try {
      const apiReply = await feedbackService.postFeedback(
        id,
        { content: replyText.trim(), parentId: messageId },
        auth
      );
      const mapped = {
        id: apiReply.id,
        name: apiReply.authorName ?? apiReply.name ?? "Institution Admin",
        email: apiReply.authorEmail ?? apiReply.email ?? "",
        message: apiReply.content ?? apiReply.message ?? "",
        date: formatDateTime(apiReply.createdAt),
      };
      setSupportMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? { ...msg, replies: [...(msg.replies || []), mapped] }
            : msg
        )
      );
      setReplyText("");
      setReplyingTo(null);
      toast.success("Reply posted!");
    } catch (err) {
      console.error("Error posting reply:", err);
      toast.error("Failed to post reply");
    }
  };

  const handleSave = async () => {
    if (!editedCampaign || !id) return;

    try {
      setIsLoading(true);

      // Convert campaign details to the format expected by the API
      // Ensure dates are properly formatted as ISO strings (YYYY-MM-DD)
      const startDate = new Date(editedCampaign.startDate);
      const endDate = new Date(editedCampaign.endDate);

      // Make sure end date is in the future
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

      // If end date is not in the future, set it to tomorrow
      if (endDate <= today) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        console.log(
          "End date adjusted to be in the future:",
          tomorrow.toISOString().split("T")[0]
        );
      }

      // Convert id from string to number, or use as is if conversion fails
      let campaignId: any;
      if (isNaN(campaignId)) {
        // If parsing fails, use the original string
        campaignId = id;
      }

      // Log the current state for debugging
      console.log("Saving campaign with data:", editedCampaign);

      // Step 1: Update campaign details only (no student details)
      console.log("Step 1: Updating campaign details only");

      const campaignData = {
        id: editedCampaign.id,
        title: editedCampaign.campaignTitle,
        description: editedCampaign.description,
        targetAmount: editedCampaign.targetAmount,
        startDate: startDate.toISOString().split("T")[0],
        endDate:
          endDate > today
            ? endDate.toISOString().split("T")[0]
            : new Date(today.getTime() + 86400000).toISOString().split("T")[0],
        status: editedCampaign.status,

        // Include relationship IDs
        institutionId: Number(campaign?.institutionId) || 1,
        studentId: Number(campaign?.studentId) || 1,
        // Explicitly NOT including any student details fields
      };

      // Update campaign first
      try {
        console.log("Sending campaign update data:", campaignData);
        await institutionService.updateCampaign(campaignId, campaignData, auth);

        console.log("Campaign details updated successfully");
        toast.success("Campaign details updated successfully");
      } catch (campaignError: any) {
        console.error("Error updating campaign details:", campaignError);
        const apiMessage = campaignError.response?.data?.message || "";
        const apiData = campaignError.response?.data?.data || {};
        let errorMessage = apiMessage;
        if (Object.keys(apiData).length > 0) {
          const validationErrors = Object.entries(apiData)
            .map(([field, error]) => `• ${field}: ${error}`)
            .join("\n");
          errorMessage = apiMessage + "\n" + validationErrors;
        }
        setErrorDetails({ message: errorMessage });
        setStatus("error");
        throw campaignError; // Re-throw to prevent student update if campaign update fails
      }

      // Step 2: Update student details separately
      if (editedCampaign.studentId) {
        console.log("Step 2: Updating student details separately");

        // Prepare student data with all fields
        const studentData = {
          email: editedCampaign.studentDetails.email || "",
          course: editedCampaign.studentDetails.course || "General Studies",
          batch: editedCampaign.studentDetails.batch || "",
          year: editedCampaign.studentDetails.year || "Current",
          phone: editedCampaign.studentDetails.phone || "",
          department: editedCampaign.studentDetails.department || "",
        };

        // Generate SQL for student update (as a fallback)
        const studentId = editedCampaign.studentId;
        const studentSql = `
-- Update the student details
UPDATE public.students
SET
    academic_year = '${
      editedCampaign.studentDetails.year?.replace(/'/g, "''") || "1"
    }',
    year = '${editedCampaign.studentDetails.year?.replace(/'/g, "''") || "1"}',
    phone = '${editedCampaign.studentDetails.phone?.replace(/'/g, "''") || ""}',
    department = '${
      editedCampaign.studentDetails.department?.replace(/'/g, "''") || ""
    }',
    course = '${
      editedCampaign.studentDetails.course?.replace(/'/g, "''") ||
      "General Studies"
    }',
    batch = '${editedCampaign.studentDetails.batch?.replace(/'/g, "''") || ""}',
 
WHERE id = ${studentId};
`;
        console.log("SQL for student update:", studentSql);

        // Try API call first, but be prepared for it to fail
        try {
          console.log("Attempting to update student details via API...");
          console.log("Sending student data:", studentData);
          await institutionService.updateStudentDetails(studentId, studentData);
          console.log("Student details updated successfully via API");
          toast.success("Student details updated successfully");
        } catch (apiError) {
          console.error("API update failed:", apiError);

          // Show a message to the user about using SQL
          toast.info(
            "Student details update: Please use the SQL query in console to update student details manually"
          );

          // For development/testing, copy the SQL to clipboard
          try {
            // This is a browser API, so it might not be available in all environments
            navigator.clipboard
              .writeText(studentSql)
              .then(() => console.log("SQL copied to clipboard"))
              .catch((err) =>
                console.error("Could not copy SQL to clipboard:", err)
              );
          } catch (clipboardError) {
            console.error("Clipboard API not available:", clipboardError);
          }
        }
      }

      // Add a small delay before refreshing to ensure backend has processed changes
      console.log("Adding delay before refreshing data...");
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Refresh campaign data with cache-busting parameter
      console.log("Fetching updated campaign data...");
      try {
        // Force a fresh fetch with cache-busting
        const updatedCampaign = await institutionService.getCampaignDetails(
          campaignId,
          true
        );
        console.log("Updated campaign data received:", updatedCampaign);

        // Log student details specifically
        console.log("Updated student details:", updatedCampaign.studentDetails);

        // Update state with new data
        setCampaign(updatedCampaign);
        setEditedCampaign(updatedCampaign);

        // Set success status
        setStatus("success");
        setIsEditing(false);
      } catch (refreshError: any) {
        console.error("Error refreshing campaign data:", refreshError);
        const apiMessage = refreshError.response?.data?.message || "";
        const apiData = refreshError.response?.data?.data || {};
        let errorMessage = apiMessage;
        if (Object.keys(apiData).length > 0) {
          const validationErrors = Object.entries(apiData)
            .map(([field, error]) => `• ${field}: ${error}`)
            .join("\n");
          errorMessage = apiMessage + "\n\n" + validationErrors;
        }
        setErrorDetails({ message: errorMessage });
        setStatus("error");
      }
    } catch (error) {
      console.error("Error in save operation:", error);
      // Main error is already handled in the specific catch blocks
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedCampaign(campaign);
    setIsEditing(false);
  };

  const handleApprovalAction = (action: "ACTIVE" | "REJECTED") => {
    if (action === "ACTIVE") {
      navigate(`/institution/campaign/${id}/approve`);
    } else {
      navigate(`/institution/campaign/${id}/reject`);
    }
  };

  if (status === "success" || status === "error") {
    const isTermination =
      errorDetails?.message === "Campaign terminated successfully";
    const isSuccess = status === "success";

    return (
      <div className="flex">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="campaigns"
          onSignOut={handleSignOut}
        />
        <div className="flex-1 min-h-screen bg-gray-50 flex flex-col">
          <InstitutionHeader />

          {/* Main Content */}
          <div className="flex-grow">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Breadcrumb */}
              <Breadcrumb
                items={getBreadcrumbItems("campaign-details", {
                  campaignTitle: campaign?.campaignTitle || "Campaign Details",
                  campaignId: id,
                  source:
                    new URLSearchParams(window.location.search).get("from") ||
                    "campaigns",
                })}
                className="mb-6"
              />

              {/* Status Content */}
              <div
                className={`${
                  isSuccess ? "bg-green-50" : "bg-red-50"
                } flex items-center justify-center p-8 rounded-lg`}
              >
                <div className="max-w-md w-full text-center">
                  <div className="mb-6 flex justify-center">
                    {isSuccess ? (
                      <Check className="h-16 w-16 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-16 w-16 text-red-500" />
                    )}
                  </div>

                  <h1
                    className={`text-2xl font-bold ${
                      isSuccess ? "text-green-800" : "text-red-800"
                    } mb-4`}
                  >
                    {isSuccess
                      ? isTermination
                        ? "Campaign Terminated Successfully"
                        : "Campaign Updated Successfully"
                      : "Error"}
                  </h1>

                  <div className="text-gray-600 mb-8 leading-relaxed whitespace-pre-line">
                    {isSuccess
                      ? isTermination
                        ? "The campaign has been terminated successfully."
                        : "Your campaign details have been saved and updated successfully."
                      : errorDetails?.message ||
                        "An unexpected error occurred. Please try again later."}
                  </div>

                  <button
                    onClick={() => {
                      if (isSuccess) {
                        isTermination
                          ? navigate("/institution-dashboard")
                          : setStatus("idle");
                      } else {
                        setStatus("idle");
                        setErrorDetails(null);
                      }
                    }}
                    className={`${
                      isSuccess
                        ? "bg-green-600 hover:bg-green-700"
                        : "bg-red-600 hover:bg-red-700"
                    } text-white px-6 py-3 rounded-lg font-medium transition-colors`}
                  >
                    {isSuccess
                      ? isTermination
                        ? "Back to Dashboard"
                        : "Continue Editing"
                      : "Try Again"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="campaigns"
          onSignOut={handleSignOut}
        />
        <div className="flex-1 min-h-screen flex flex-col bg-gray-50">
          <InstitutionHeader />

          {/* Loading Indicator */}
          <div className="flex-grow flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading campaign details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!campaign || !editedCampaign || apiError) {
    return (
      <div className="flex">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="campaigns"
          onSignOut={handleSignOut}
        />
        <div className="flex-1 min-h-screen flex flex-col bg-gray-50">
          <InstitutionHeader />

          {/* Error Message */}
          <div className="flex-grow flex items-center justify-center">
            <div className="text-center">
              {apiError ? (
                <>
                  <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-4">API Error</h2>
                  <p className="text-gray-600 mb-6">{apiError}</p>
                </>
              ) : (
                <>
                  <h2 className="text-xl font-semibold mb-4">
                    Campaign Not Found
                  </h2>
                  <p className="text-gray-600 mb-6">
                    The campaign you're looking for doesn't exist or has been
                    removed.
                  </p>
                </>
              )}
              <button
                onClick={() => safeNavigate("/institution-dashboard")}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex">
      <InstitutionSidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        activeTab="campaigns"
        onSignOut={handleSignOut}
      />
      <div className="flex-1 min-h-screen bg-gray-50 flex flex-col">
        <InstitutionHeader />

        {/* Campaign View Tracker - Invisible component that tracks views */}
        <CampaignViewTracker
          campaignId={Number(id)}
          userId={user?.id?.toString()}
        />

        {/* Main Content */}
        <div className="flex-grow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <button
                  onClick={() =>
                    safeNavigate("/institution-dashboard?tab=campaigns")
                  }
                  className="flex items-center text-gray-600 hover:text-gray-900"
                >
                  <ChevronLeft className="h-5 w-5 mr-1" />
                  Back to Campaigns
                </button>
              </div>

              {/* Breadcrumb */}
              <Breadcrumb
                items={getBreadcrumbItems("campaign-details", {
                  campaignTitle: campaign?.campaignTitle || "Campaign Details",
                  campaignId: id,
                  source:
                    new URLSearchParams(window.location.search).get("from") ||
                    "campaigns",
                })}
              />

              <div className="bg-white rounded-lg shadow-lg p-6">
                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg border">
                  {campaign?.status === "PENDING" && (
                    <>
                      <button
                        onClick={() => handleApprovalAction("ACTIVE")}
                        className="flex items-center bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Approve Campaign
                      </button>
                      <button
                        onClick={() => handleApprovalAction("REJECTED")}
                        className="flex items-center bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Reject Campaign
                      </button>
                    </>
                  )}
                  {!isEditing ? (
                    <>
                      {canEdit && canEditCampaign ? (
                        <button
                          onClick={() => setIsEditing(true)}
                          className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                        >
                          <Edit2 className="h-4 w-4 mr-2" />
                          Edit Campaign
                        </button>
                      ) : (
                        <button
                          disabled
                          className="flex items-center bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed"
                          title={
                            !canEdit
                              ? "You don't have permission to edit campaigns"
                              : "Only pending campaigns can be edited"
                          }
                        >
                          <Edit2 className="h-4 w-4 mr-2" />
                          Edit Campaign
                        </button>
                      )}
                      {campaign?.status !== "PAUSED" &&
                        campaign?.status !== "COMPLETED" &&
                        campaign?.status !== "REJECTED" &&
                        (canTerminate ? (
                          <button
                            onClick={() => setShowTerminateModal(true)}
                            className="flex items-center bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Terminate Campaign
                          </button>
                        ) : (
                          <button
                            disabled
                            className="flex items-center bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed"
                            title="You don't have permission to terminate campaigns"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Terminate Campaign
                          </button>
                        ))}
                    </>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        className="flex items-center bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </button>
                      <button
                        onClick={handleCancel}
                        className="flex items-center bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </button>
                    </>
                  )}
                </div>

                <div className="space-y-6">
                  {/* Student Details */}
                  <div className="border-t pt-6 mb-2">
                    <div className="flex items-center mb-4">
                      <h3 className="text-lg font-semibold">
                        Student Details{" "}
                      </h3>
                      {campaign?.studentBatch && !isEditing && (
                        <span className="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                          Batch: {campaign.studentBatch}
                        </span>
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {/* Student Registration ID */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Registration ID
                        </label>
                        <p className="font-medium">
                          {campaign?.studentRegId || "Not available"}
                        </p>
                      </div>

                      {/* Student Phone */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone
                        </label>
                        <p>{campaign?.studentPhone || "Not available"}</p>
                      </div>

                      {/* Student Course */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Course
                        </label>
                        <p>{campaign?.studentCourse || "Not available"}</p>
                      </div>

                      {/* Student Year */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Year
                        </label>
                        <p>{campaign?.studentYear || "Not available"}</p>
                      </div>

                      {/* Student Department */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Department
                        </label>
                        <p>{campaign?.studentDepartment || "Not available"}</p>
                      </div>
                      {/* Display supported fields from API response */}
                      {Object.entries(campaign?.studentDetails || {}).map(
                        ([key, value]) =>
                          key !== "batch" && (
                            <div key={key}>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                {key.charAt(0).toUpperCase() +
                                  key.slice(1).replace(/([A-Z])/g, " $1")}
                              </label>
                              {isEditing ? (
                                <input
                                  type="text"
                                  value={
                                    editedCampaign?.studentDetails?.[
                                      key as keyof typeof editedCampaign.studentDetails
                                    ] || ""
                                  }
                                  onChange={(e) =>
                                    setEditedCampaign({
                                      ...editedCampaign,
                                      studentDetails: {
                                        ...editedCampaign.studentDetails,
                                        [key]: e.target.value,
                                      },
                                    })
                                  }
                                  className="w-full p-2 border rounded-md"
                                />
                              ) : (
                                <p>{value}</p>
                              )}
                            </div>
                          )
                      )}

                      {/* Add fields that might not be in the API response but we want to edit */}
                      {isEditing && (
                        <>
                          {/* Phone field - might not be in API response */}
                          {!campaign.studentDetails.hasOwnProperty("phone") && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Phone
                              </label>
                              <input
                                type="text"
                                value={
                                  editedCampaign.studentDetails.phone || ""
                                }
                                onChange={(e) =>
                                  setEditedCampaign({
                                    ...editedCampaign,
                                    studentDetails: {
                                      ...editedCampaign.studentDetails,
                                      phone: e.target.value,
                                    },
                                  })
                                }
                                className="w-full p-2 border rounded-md"
                              />
                            </div>
                          )}

                          {/* Department field - might not be in API response */}
                          {!campaign.studentDetails.hasOwnProperty(
                            "department"
                          ) && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Department
                              </label>
                              <input
                                type="text"
                                value={
                                  editedCampaign.studentDetails.department || ""
                                }
                                onChange={(e) =>
                                  setEditedCampaign({
                                    ...editedCampaign,
                                    studentDetails: {
                                      ...editedCampaign.studentDetails,
                                      department: e.target.value,
                                    },
                                  })
                                }
                                className="w-full p-2 border rounded-md"
                              />
                            </div>
                          )}
                        </>
                      )}
                      {isEditing && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Batch
                          </label>
                          <input
                            type="text"
                            value={editedCampaign.studentDetails.batch || ""}
                            onChange={(e) =>
                              setEditedCampaign({
                                ...editedCampaign,
                                studentDetails: {
                                  ...editedCampaign.studentDetails,
                                  batch: e.target.value,
                                },
                              })
                            }
                            className="w-full p-2 border rounded-md"
                            placeholder="e.g. 2022-2025 B.Sc"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Campaign Title */}
                  <div>
                    <h3 className="text-lg font-semibold pb-4">
                      Campaign Details{" "}
                    </h3>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Campaign Title
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editedCampaign?.campaignTitle || ""}
                        onChange={(e) =>
                          setEditedCampaign({
                            ...editedCampaign,
                            campaignTitle: e.target.value,
                          })
                        }
                        className="w-full p-2 border rounded-md"
                      />
                    ) : (
                      <p className="text-lg font-semibold">
                        {campaign?.campaignTitle || "No Title"}
                      </p>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    {isEditing ? (
                      <textarea
                        value={editedCampaign?.description || ""}
                        onChange={(e) =>
                          setEditedCampaign({
                            ...editedCampaign,
                            description: e.target.value,
                          })
                        }
                        className="w-full p-2 border rounded-md"
                        rows={4}
                      />
                    ) : (
                      <p className="text-gray-600">
                        {campaign?.description || "No description available"}
                      </p>
                    )}
                  </div>

                  {/* Campaign Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Target Amount */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Target Amount
                      </label>
                      {isEditing ? (
                        <input
                          type="number"
                          value={editedCampaign?.targetAmount || 0}
                          onChange={(e) =>
                            setEditedCampaign({
                              ...editedCampaign,
                              targetAmount: Number(e.target.value),
                            })
                          }
                          className="w-full p-2 border rounded-md"
                        />
                      ) : (
                        <p className="text-lg font-semibold">
                          ₹{campaign?.targetAmount || 0}
                        </p>
                      )}
                    </div>

                    {/* Raised Amount */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Raised Amount
                      </label>
                      <p className="text-lg font-semibold text-green-600">
                        ₹{campaign?.raisedAmount || 0}
                      </p>
                    </div>

                    {/* Status - Read only, cannot be edited */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium
                      ${
                        campaign?.status === "ACTIVE"
                          ? "bg-green-100 text-green-800"
                          : campaign?.status === "COMPLETED"
                          ? "bg-blue-100 text-blue-800"
                          : campaign?.status === "PENDING"
                          ? "bg-blue-100 text-blue-800"
                          : campaign?.status === "REJECTED"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                      >
                        {campaign?.status
                          ? campaign.status.charAt(0) +
                            campaign.status.slice(1).toLowerCase()
                          : "Unknown"}
                      </span>
                    </div>

                    {/* Start Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Date
                      </label>
                      {isEditing ? (
                        <input
                          type="date"
                          value={editedCampaign?.startDate || ""}
                          onChange={(e) =>
                            setEditedCampaign({
                              ...editedCampaign,
                              startDate: e.target.value,
                            })
                          }
                          className="w-full p-2 border rounded-md"
                        />
                      ) : (
                        <p className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-600 mr-2" />
                          {campaign?.startDate
                            ? new Date(campaign.startDate).toLocaleDateString()
                            : "No start date"}
                        </p>
                      )}
                    </div>

                    {/* End Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Date
                      </label>
                      {isEditing ? (
                        <input
                          type="date"
                          value={editedCampaign?.endDate || ""}
                          onChange={(e) =>
                            setEditedCampaign({
                              ...editedCampaign,
                              endDate: e.target.value,
                            })
                          }
                          className="w-full p-2 border rounded-md"
                        />
                      ) : (
                        <p className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-600 mr-2" />
                          {campaign?.endDate
                            ? new Date(campaign.endDate).toLocaleDateString()
                            : "No end date"}
                          {campaign?.endDateReached && (
                            <span className="ml-2 text-red-500 text-sm">
                              (Reached)
                            </span>
                          )}
                        </p>
                      )}
                    </div>

                    {/* Auto Close Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Auto Close Date
                      </label>
                      <p className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-600 mr-2" />
                        {campaign?.autoCloseDate
                          ? new Date(
                              campaign.autoCloseDate
                            ).toLocaleDateString()
                          : "Not set"}
                      </p>
                    </div>

                    {/* Auto Complete Amount */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Auto Complete Amount
                      </label>
                      <p className="text-lg font-semibold">
                        ₹{campaign?.autoCompleteAmount || 0}
                      </p>
                    </div>
                  </div>

                  {/* Campaign Closure Info */}
                  {!isEditing && (
                    <div className="mt-6">
                      <CampaignClosureInfo
                        campaignStatus={campaign?.status || "PENDING"}
                        startDate={campaign?.startDate || ""}
                        endDate={campaign?.endDate || ""}
                        targetAmount={campaign?.targetAmount || 0}
                        raisedAmount={campaign?.raisedAmount || 0}
                        rejectionReason={campaign?.rejectionReason}
                      />
                    </div>
                  )}

                  {/* Approval Information */}
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                      Approval Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Approved By
                        </label>
                        <p className="flex items-center">
                          {campaign?.approvedBy &&
                          campaign.approvedBy !== "—" ? (
                            <>
                              <Check className="h-4 w-4 text-green-600 mr-2" />
                              {campaign.approvedBy}
                            </>
                          ) : (
                            <>
                              <Clock className="h-4 w-4 text-yellow-600 mr-2" />
                              Pending Approval
                            </>
                          )}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Approval Date
                        </label>
                        <p className="flex items-center">
                          {campaign?.approvalDate ? (
                            <>
                              <Calendar className="h-4 w-4 text-green-600 mr-2" />
                              {new Date(
                                campaign.approvalDate
                              ).toLocaleDateString()}
                            </>
                          ) : (
                            <>
                              <Clock className="h-4 w-4 text-yellow-600 mr-2" />
                              Not approved yet
                            </>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Privacy Settings */}
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    {/* <h3 className="text-lg font-semibold mb-3 text-gray-800">Privacy Settings</h3> */}
                    {/*
                  <div className="flex items-start space-x-3">
                    {isEditing ? (
                      <input
                        type="checkbox"
                        id="showSupporters"
                        checked={editedCampaign?.showSupportersToOthers ?? true}
                        onChange={(e) => setEditedCampaign({
                          ...editedCampaign,
                          showSupportersToOthers: e.target.checked
                        })}
                        className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    ) : (
                      <div className={`mt-1 h-4 w-4 rounded border-2 flex items-center justify-center ${
                        campaign?.showSupportersToOthers !== false ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                      }`}>
                        {campaign?.showSupportersToOthers !== false && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                    )}
                    <div>
                      <label htmlFor="showSupporters" className="text-sm font-medium text-gray-700">
                        Show supporters list to other supporters
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        When enabled, supporters can see who else has contributed to this campaign.
                      </p>
                    </div>
                  </div>
                  */}
                  </div>

                  {/* Timestamps */}
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">
                      Timeline
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Created At
                        </label>
                        <p className="flex items-center">
                          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                          {campaign?.createdAt
                            ? campaign.createdAt
                            : "Not available"}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Last Updated
                        </label>
                        <p className="flex items-center">
                          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                          {campaign?.updatedAt
                            ? campaign.updatedAt
                            : "Not available"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Words of Support Section */}
                  <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">
                      Words of Support
                    </h3>

                    <div className="mb-6">
                      <textarea
                        value={wordsOfSupport}
                        onChange={(e) => setWordsOfSupport(e.target.value)}
                        placeholder="Share words of encouragement as an institution representative..."
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={3}
                        maxLength={500}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm text-gray-500">
                          {wordsOfSupport.length}/500
                        </span>
                        <button
                          onClick={async () => {
                            if (!id || !wordsOfSupport.trim()) return;
                            try {
                              const apiMsg = await feedbackService.postFeedback(
                                id,
                                {
                                  content: wordsOfSupport.trim(),
                                  parentId: null,
                                },
                                auth
                              );
                              const mapped = {
                                id: String(apiMsg.id),
                                name:
                                  apiMsg.authorName ??
                                  apiMsg.name ??
                                  "Institution",
                                email: apiMsg.authorEmail ?? apiMsg.email ?? "",
                                message: apiMsg.content ?? apiMsg.message ?? "",
                                date: formatDateTime(apiMsg.createdAt),
                                replies: apiMsg.replies,
                              };
                              setSupportMessages([mapped, ...supportMessages]);
                              setWordsOfSupport("");
                              toast.success("Message shared!");
                            } catch (err) {
                              console.error("Error posting feedback:", err);
                              toast.error("Failed to share message");
                            }
                          }}
                          disabled={!wordsOfSupport.trim()}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                        >
                          Share
                        </button>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {supportMessages.map((message) => (
                        <div
                          key={message.id}
                          className="bg-gray-50 p-4 rounded-lg"
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <p className="font-medium text-gray-900">
                                {message.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {message.email}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-500">
                                {message.date}
                              </span>
                              <button
                                onClick={() => handleDeleteFeedback(message.id)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <p className="text-gray-700 mb-3">
                            {message.message}
                          </p>

                          <button
                            onClick={() =>
                              setReplyingTo(
                                replyingTo === message.id ? null : message.id
                              )
                            }
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            {replyingTo === message.id ? "Cancel" : "Reply"}
                          </button>

                          {replyingTo === message.id && (
                            <div className="mt-3 pl-4 border-l-2 border-blue-200">
                              <textarea
                                value={replyText}
                                onChange={(e) => setReplyText(e.target.value)}
                                placeholder="Write your reply..."
                                className="w-full p-2 border rounded text-sm"
                                rows={2}
                                maxLength={300}
                              />
                              <div className="flex justify-between mt-2">
                                <span className="text-xs text-gray-500">
                                  {replyText.length}/300
                                </span>
                                <button
                                  onClick={() => handlePostReply(message.id)}
                                  disabled={!replyText.trim()}
                                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                                >
                                  Reply
                                </button>
                              </div>
                            </div>
                          )}

                          {message.replies && message.replies.length > 0 && (
                            <div className="mt-4 pl-4 border-l-2 border-gray-200 space-y-3">
                              {message.replies.map((reply) => (
                                <div
                                  key={reply.id}
                                  className="bg-white p-3 rounded"
                                >
                                  <div className="flex justify-between items-start mb-1">
                                    <div>
                                      <p className="font-medium text-sm text-blue-600">
                                        {reply.name}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {reply.email}
                                      </p>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs text-gray-500">
                                        {reply.date}
                                      </span>
                                      <button
                                        onClick={() =>
                                          handleDeleteReply(
                                            message.id,
                                            reply.id
                                          )
                                        }
                                        className="text-red-600 hover:text-red-800"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                  <p className="text-gray-700 text-sm">
                                    {reply.message}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                      {supportMessages.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <p>No messages yet. Share words of encouragement!</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Terminate Campaign Modal */}
        {showTerminateModal && (
          <TerminateCampaignModal
            campaignId={Number(id)}
            campaignTitle={campaign.campaignTitle}
            onClose={() => setShowTerminateModal(false)}
            onTerminate={handleTerminateCampaign}
          />
        )}
      </div>
    </div>
  );
}
