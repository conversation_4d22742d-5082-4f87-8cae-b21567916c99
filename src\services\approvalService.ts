import { ApprovalRequest } from './institutionService';
import api from './api';

// Define the types that need to be exported
export interface ApprovalRequestsResponse {
  content: ApprovalRequest[];
  totalPages: number;
  totalElements: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

export interface GetApprovalRequestsParams {
  page?: number;
  size?: number;
  keyword?: string;
}

// Import isTokenExpired function if it's not already imported
const isTokenExpired = (token: string): boolean => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    const { exp } = JSON.parse(jsonPayload);
    return exp * 1000 < Date.now();
  } catch (e) {
    console.error('Error checking token expiration:', e);
    return true; // Assume expired if we can't parse it
  }
};

// Function to get ID token from react-oidc-context auth object
const getIdToken = (auth?: any): string => {
  console.log('getIdToken called with auth:', {
    hasAuth: !!auth,
    hasUser: !!auth?.user,
    userKeys: auth?.user ? Object.keys(auth.user) : [],
    id_token: auth?.user?.id_token ? 'present' : 'missing',
    access_token: auth?.user?.access_token ? 'present' : 'missing'
  });

  // Try to get token from react-oidc-context auth object first
  let token = auth?.user?.id_token || auth?.user?.access_token;

  // Fallback to localStorage
  if (!token) {
    token = localStorage.getItem('id_token') || localStorage.getItem('access_token');
  }

  if (token) {
    if (isTokenExpired(token)) {
      console.warn('Token is expired.');
      throw new Error('Token expired');
    }
    console.log('Found valid token');
    return token;
  }
  console.warn('User is not authenticated - no token found.');
  throw new Error('User is not authenticated');
};

/**
 * Service for managing approval requests in the EduFund application
 */
const approvalService = {
  // Other methods...

  getInstitutionApprovals: async (
    page: number = 0,
    size: number = 10
  ): Promise<ApprovalRequestsResponse> => {
    try {
      console.log(`Fetching institution approval requests - page: ${page}, size: ${size}`);
      const response = await api.get(
        `/api/institution/v2/approval-request?page=${page}&size=${size}`,
        {
          headers: {
            'accept': '*/*'
          }
        }
      );
      
      console.log('Approval requests response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching institution approval requests:', error);
      return {
        content: [],
        totalPages: 0,
        totalElements: 0,
        size: size,
        number: page,
        first: page === 0,
        last: true,
        empty: true
      };
    }
  },

  /**
   * Search institution approval requests
   * @param keyword Search keyword
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Promise with paginated approval requests
   */
  searchInstitutionApprovals: async (
    keyword: string,
    page: number = 0,
    size: number = 10
  ): Promise<ApprovalRequestsResponse> => {
    try {
      console.log(`Searching institution approval requests - keyword: ${keyword}, page: ${page}, size: ${size}`);
      const response = await api.get(
        `/api/institution/v2/approval-request/search?keyword=${encodeURIComponent(keyword)}&page=${page}&size=${size}`,
        {
          headers: {
            'accept': '*/*'
          }
        }
      );
      
      console.log('Approval requests search response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error searching institution approval requests:', error);
      return {
        content: [],
        totalPages: 0,
        totalElements: 0,
        size: size,
        number: page,
        first: page === 0,
        last: true,
        empty: true
      };
    }
  },
  // Update approval request status
updateApprovalRequest: async (
  approvalId: string,
  data: {
    id: string;
    status: string;
    reason: string;
  },
  auth: any
): Promise<ApprovalRequest> => {
  try {
    console.log(`Updating approval request with ID: ${approvalId}`);
    const token = getIdToken(auth);

    const response = await api.put(
      `/api/institution/v2/approval-request/${approvalId}`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          Accept: '*/*',
        },
      }
    );

    console.log('Approval update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating approval request:', error);
    throw error;
  }
}

};

export default approvalService;



