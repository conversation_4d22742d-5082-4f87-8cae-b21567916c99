import React, { Suspense, lazy, ComponentType } from 'react';
import LoadingSpinner from '../components/LoadingSpinner';

/**
 * Utility function to lazy load components with a loading spinner
 * @param importFunc - Dynamic import function for the component
 * @param loadingMessage - Optional loading message to display
 * @returns Lazy loaded component wrapped in Suspense
 */
export function lazyLoad<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  loadingMessage?: string
) {
  const LazyComponent = lazy(importFunc);

  return (props: React.ComponentProps<T>): JSX.Element => (
    <Suspense fallback={<LoadingSpinner message={loadingMessage} />}>
      <LazyComponent {...props} />
    </Suspense>
  );
}

export default lazyLoad;
