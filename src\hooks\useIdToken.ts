import { useAuth } from 'react-oidc-context';
import { useCallback, useRef } from 'react';

export const useIdToken = () => {
  const auth = useAuth();
  const tokenCache = useRef<string | null>(null);

  const getIdToken = useCallback(async (): Promise<string> => {
    // First check cache
    if (tokenCache.current) {
      try {
        const payload = JSON.parse(atob(tokenCache.current.split('.')[1]));
        if (payload.exp * 1000 > Date.now()) {
          return tokenCache.current;
        }
        // Clear expired token from cache
        tokenCache.current = null;
      } catch (err) {
        console.error('Failed to decode cached token', err);
        tokenCache.current = null;
      }
    }

    // If no cache or expired, get fresh token
    if (auth.isAuthenticated && auth.user?.id_token) {
      try {
        const token = auth.user.id_token;
        const payload = JSON.parse(atob(token.split('.')[1]));
        
        if (payload.exp * 1000 < Date.now()) {
          console.warn('Token has expired. Redirecting to login.');
          await auth.signinRedirect();
          throw new Error('Token expired');
        }

        // Cache the valid token
        tokenCache.current = token;
        return token;
      } catch (err) {
        console.error('Failed to decode or validate token', err);
        throw new Error('Invalid token');
      }
    }

    console.warn('User not authenticated. Redirecting to login.');
    await auth.signinRedirect();
    throw new Error('Not authenticated');
  }, [auth]);

  const clearTokenCache = useCallback(() => {
    tokenCache.current = null;
  }, []);

  return { getIdToken, clearTokenCache };
}; 