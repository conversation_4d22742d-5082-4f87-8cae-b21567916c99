export interface Institution {
  id: string;
  name: string;
  logo: string;
  website: string;
  status: 'pending' | 'approved' | 'rejected';
  administrativeContact: Contact;
  principalContact: Contact;
  bankDetails: BankDetails;
  createdAt: Date;
  updatedAt: Date;
}

export interface Contact {
  name: string;
  email: string;
  mobile: string;
}

export interface BankDetails {
  accountName: string;
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  branch: string;
}

export interface Course {
  id: string;
  institutionId: string;
  name: string;
  description: string;
  feeAmount: number;
  academicYear: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Student {
  id: string;
  institutionId: string;
  studentId: string;
  name: string;
  email: string;
  mobile: string;
  photoIdUrl: string;
  courseId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Supporter {
  id: string;
  name: string;
  email: string;
  mobile: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FundingCampaign {
  id: string;
  studentId: string;
  courseId: string;
  targetAmount: number;
  raisedAmount: number;
  story: string;
  status: 'active' | 'completed' | 'cancelled';
  qrCode: string;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Donation {
  id: string;
  campaignId: string;
  supporterId: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  transactionId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Add pagination types
export interface PaginationParams {
  page: number;
  size: number;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
}

export interface ApprovalRequest {
  id: string;
  institutionId: string;
  institutionName: string;
  requestType: 'institution' | 'campaign' | 'student';
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  comments?: string;
}