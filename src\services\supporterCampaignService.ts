import publicApi from './publicApi';

export interface SupporterCampaign {
  id: string;
  title: string;
  status: string;
  duration: string;
  target: number;
  raised: number;
  supporters: number;
  daysLeft: number;
  student: {
    institutionName: string;
    course: string;
    email: string;
    phone: string;
  };
  description: string;
}

const supporterCampaignService = {
  /**
   * Get campaign by ID
   * @param id Campaign ID
   * @returns Promise with campaign details
   */
  getCampaignById: async (id: string): Promise<SupporterCampaign> => {
    console.log(`Fetching campaign with ID: ${id}`);
    try {
      const response = await publicApi.get(`/api/public/v1/campaigns/${id}`);
      console.log('Campaign API response:', response);
      return response.data.data;
    } catch (error) {
      console.error('Error in supporterCampaignService.getCampaignById:', error);
      throw error;
    }
  }
};

export default supporterCampaignService;
