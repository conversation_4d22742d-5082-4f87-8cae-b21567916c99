import React from 'react';

interface FooterProps {
  className?: string;
}

export function SupporterFooter({ className = '' }: FooterProps) {
  return (
    <footer className={`bg-white border-t ${className}`.trim()}>
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          <div className="text-gray-500 text-sm">
            © {new Date().getFullYear()}{' '}
            <a
              href="https://www.icamxperts.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              ICAMXPERTS
            </a>. All rights reserved.
          </div>
          <div className="flex items-center text-gray-500 text-sm">
            Supporter Module
          </div>
        </div>
      </div>
    </footer>
  );
}


export default SupporterFooter;