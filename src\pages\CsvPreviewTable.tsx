import React from "react";

type StudentRow = {
  studentId: string;
  name: string;
  course: string;
  year: string;
  feeAmount: string;
};

const CsvPreviewTable: React.FC<{ data: StudentRow[] }> = ({ data }) => {
  return (
    <div className="overflow-x-auto max-h-96 border rounded">
      <table className="min-w-full table-auto text-sm">
        <thead className="bg-gray-100 sticky top-0">
          <tr>
            <th className="px-4 py-2 border">Student ID</th>
            <th className="px-4 py-2 border">Name</th>
            <th className="px-4 py-2 border">Course</th>
            <th className="px-4 py-2 border">Year</th>
            <th className="px-4 py-2 border">Fee Amount</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr key={idx} className="hover:bg-gray-50">
              <td className="px-4 py-2 border">{row.studentId}</td>
              <td className="px-4 py-2 border">{row.name}</td>
              <td className="px-4 py-2 border">{row.course}</td>
              <td className="px-4 py-2 border">{row.year}</td>
              <td className="px-4 py-2 border">{row.feeAmount}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CsvPreviewTable;
