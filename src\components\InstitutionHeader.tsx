import React, { useEffect, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import institutionService from '../services/institutionService';

export function InstitutionHeader() {
  const auth = useAuth();
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const [institutionName, setInstitutionName] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (auth.isAuthenticated) {
        try {
          const [logo, profile] = await Promise.all([
            institutionService.getInstitutionLogo(auth),
            institutionService.getInstitutionProfile(auth),
          ]);
          if (logo) setLogoUrl(logo);
          if (profile) {
            setInstitutionName(profile.name || profile.institutionName || null);
          }
        } catch (error) {
          console.error('Failed to load institution data:', error);
        }
      }
    };
    fetchData();
  }, [auth.isAuthenticated]);

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center space-x-3">
            {logoUrl && (
              <img
                src={logoUrl}
                alt="Institution Logo"
                className="h-8 w-8 rounded-full object-cover"
              />
            )}
            {institutionName && (
              <span className="font-semibold text-gray-800 truncate">
                {institutionName}
              </span>
            )}
          </div>

          {auth.user && (
            <div className="flex items-center space-x-3">
              <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold">
                {auth.user.profile.given_name?.[0]}
                {auth.user.profile.family_name?.[0]}
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">
                  {auth.user.profile.given_name} {auth.user.profile.family_name}
                </div>
                <div className="text-xs text-gray-500">
                  {auth.user.profile.email}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

export default InstitutionHeader;
