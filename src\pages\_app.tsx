import Amplify from "../config/amplify";

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolClientId: process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID ?? "",
      userPoolId: process.env.NEXT_PUBLIC_USER_POOL_ID ?? "",
      loginWith: {
        oauth: {
          domain: process.env.OAUTH_DOMAIN ?? "",
          scopes: ['email'],
          redirectSignIn: [process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN ?? ""],
          redirectSignOut: [process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT ?? ""],
          responseType: "code"
        },
        username: false,
        email: true,
        phone: false,
      }
    }
  }
}); 