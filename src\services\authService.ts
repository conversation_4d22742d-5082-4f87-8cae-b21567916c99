import api from './api';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  id: string | number; // Updated to support UUID-style user IDs
  name: string;
  email: string;
  userType: string;
  profileId: number | string; // Updated to support UUID-style profile IDs
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  userType: string;
}

const authService = {
  // Generic login with email and password - using mock data since endpoint is not available
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    console.warn('Using mock login response as /auth/login endpoint is not available');
    // Return a mock response
    return {
      token: 'mock-token',
      id: 'mock-user-id',
      name: credentials.email.split('@')[0],
      email: credentials.email,
      userType: 'STUDENT',
      profileId: 'mock-profile-id'
    };
  },

  // Institution-specific login - using mock data since endpoint is not available
  loginInstitution: async (credentials: LoginRequest): Promise<LoginResponse> => {
    console.warn('Using mock institution login response as endpoint is not available');
    // Return a mock response
    return {
      token: 'mock-token',
      id: 'mock-institution-id',
      name: credentials.email.split('@')[0],
      email: credentials.email,
      userType: 'INSTITUTION',
      profileId: 'mock-profile-id'
    };
  },

  // Register a new user - using mock data since endpoint is not available
  register: async (userData: RegisterRequest) => {
    console.warn('Using mock register response as /auth/register endpoint is not available');
    // Return a mock response
    return {
      id: 'mock-user-id',
      name: userData.name,
      email: userData.email,
      userType: userData.userType,
      profileId: 'mock-profile-id'
    };
  },

  // Logout - clear storage
  logout: () => {
    // Clear both localStorage and sessionStorage to ensure complete logout
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
  },

  // Get current user from storage
  getCurrentUser: () => {
    // First check localStorage (for remembered users)
    let userStr = localStorage.getItem('user');

    // If not in localStorage, check sessionStorage (for non-remembered users)
    if (!userStr) {
      userStr = sessionStorage.getItem('user');
    }

    if (userStr) {
      return JSON.parse(userStr);
    }
    return null;
  },

  // Save user to storage based on rememberMe preference
  saveUser: (user: any, rememberMe = false) => {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem('user', JSON.stringify(user));
  },

  // Save token to storage based on rememberMe preference
  saveToken: (token: string, rememberMe = false) => {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem('token', token);
  },

  // Check if user is logged in
  isLoggedIn: () => {
    // Check both localStorage and sessionStorage for token
    return !!(localStorage.getItem('token') || sessionStorage.getItem('token'));
  }
};

export default authService;
