import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { toast } from 'react-toastify';
import { X, AlertTriangle } from 'lucide-react';
import institutionService from '../services/institutionService';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import StatusPage from '../components/StatusPage';
import type { User } from '../services';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';
import cognitoService from '../services/cognitoService';

const RevokeRolePage = () => {
  const navigate = useNavigate();
  const auth = useAuth();
  const { userId, roleName } = useParams<{ userId: string; roleName: string }>();
  
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [revoking, setRevoking] = useState(false);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);

  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');
    toast.success('Successfully signed out');
    cognitoService.redirectToLogout();
  };

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!userId) {
        setErrorDetails({ message: 'User ID is required' });
        setStatus('error');
        return;
      }

      try {
        setLoading(true);
        const users = await institutionService.getInstitutionUsers(auth);
        const user = users.find(u => u.id === userId);
        
        if (!user) {
          setErrorDetails({ message: 'User not found' });
          setStatus('error');
          return;
        }
        
        setSelectedUser(user);
        
        // Get user's current roles
        const roles = getUserRoles(user);
        setUserRoles(roles);
        
        // Validate if the role to revoke exists
        if (roleName && !roles.includes(roleName)) {
          setErrorDetails({ message: `Role ${roleName} not found for this user` });
          setStatus('error');
        }
      } catch (error) {
        console.error('Error fetching user details:', error);
        const apiMessage = (error as any).response?.data?.message || 'Failed to load user details';
        setErrorDetails({ message: apiMessage });
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [userId, roleName, auth, navigate]);

  // Get user's current roles
  const getUserRoles = (user: User): string[] => {
    if (!user) return [];
    
    if (Array.isArray(user.roles)) {
      return user.roles;
    } else if (Array.isArray(user.role)) {
      return user.role;
    } else if (user.role) {
      return [user.role];
    }
    
    return [];
  };

  const handleRevokeRole = async () => {
    if (!selectedUser || !roleName) {
      toast.error('User or role information is missing');
      return;
    }
    
    // Check if this is the last role
    if (userRoles.length === 1 && userRoles[0] === roleName) {
      toast.error('Cannot remove the last role. A user must have at least one role.');
      return;
    }
    
    try {
      setRevoking(true);
      await institutionService.revokeUserRole(selectedUser.id, roleName, auth);
      setStatus('success');
    } catch (error: any) {
      console.error('Error revoking role:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    } finally {
      setRevoking(false);
    }
  };

  // Get role description
  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'INSTITUTION_ADMIN':
        return 'Institution Admin has full access to all features including user management, campaign management, approvals, and financial reports.';
      case 'FINANCIAL_ADMIN':
        return 'Financial Admin can manage campaigns, approvals, bank details, and download reports, but cannot manage users or institution profile.';
      case 'CLERK':
        return 'Clerk has limited access with view-only permissions for most features.';
      default:
        return '';
    }
  };

  // Show status page after operation
  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Role Removed Successfully"
        message={`${roleName} role has been removed from ${selectedUser?.name || 'the user'}.`}
        actionText="Back to Settings"
        backUrl="/institution-dashboard?tab=settings"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="settings"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-100 p-4 md:p-8">
        {/* Breadcrumb */}
        <Breadcrumb items={getBreadcrumbItems('revoke-role')} />

      <div className="max-w-2xl mx-auto mt-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800">Revoke Role</h1>
            <button
              onClick={() => navigate('/institution-dashboard?tab=settings')}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading user details...</span>
            </div>
          ) : selectedUser ? (
            <>
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">User Information</h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">{selectedUser.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{selectedUser.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <p className="font-medium">
                        <span className={`px-2 py-1 text-xs rounded-full text-white ${
                          selectedUser.status?.toLowerCase() === 'active' ? 'bg-green-600' : 'bg-gray-500'
                        }`}>
                          {selectedUser.status}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Current Roles</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {userRoles.length > 0 ? (
                          userRoles.map(role => (
                            <div key={role} className={`bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ${
                              role === roleName ? 'border-2 border-red-400' : ''
                            }`}>
                              {role}
                              {role === roleName && <span className="ml-1">⚠️</span>}
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-gray-500">No roles assigned</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-800">Warning: Role Removal</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        You are about to remove the <strong>{roleName}</strong> role from this user. 
                        This will revoke all permissions associated with this role.
                      </p>
                      
                      {userRoles.length === 1 && userRoles[0] === roleName && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          <strong>Cannot proceed:</strong> This is the user's only role. A user must have at least one role.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">Role Details</h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-800">{roleName}</h3>
                  <p className="text-sm text-gray-600 mt-2">{getRoleDescription(roleName || '')}</p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-8">
                <button
                  onClick={() => navigate('/institution-dashboard?tab=settings')}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRevokeRole}
                  disabled={revoking || userRoles.length === 1}
                  className={`px-4 py-2 ${
                    revoking || userRoles.length === 1
                      ? 'bg-red-400 cursor-not-allowed'
                      : 'bg-red-600 hover:bg-red-700'
                  } text-white rounded flex items-center`}
                >
                  {revoking && (
                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                  )}
                  Revoke Role
                </button>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-red-600">User not found</p>
              <button
                onClick={() => navigate('/institution-dashboard?tab=settings')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Back to Settings
              </button>
            </div>
          )}
        </div>
        </div>
      </div>
      </div>
      
     
    </div>
  );
};

export default RevokeRolePage;