import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { useAuth } from "react-oidc-context";

import institutionService from "../services/institutionService";
import cognitoService from "../services/cognitoService";
import { Pagination, Select, MenuItem } from "@mui/material";
import StatusPage from "../components/StatusPage";


import "react-tabs/style/react-tabs.css";
import {
  approvalService,
  type Institution,
  type Campaign as ApiCampaign,
  type Student,
  type ApprovalRequest,
  type Activity,
  type User,
} from "../services";
import Breadcrumb from "../components/Breadcrumb";
import { getBreadcrumbItems } from "../utils/breadcrumbUtils";
import {
  Users,
  FileText,
  Bell,
  ChevronDown,
  X,
  ClipboardList,
  CreditCard,
  Loader,
  Plus,
  Search,
  Pencil,
  Check,
  AlertTriangle,
  UserPlus,
  XCircle,
  CheckCircle,
  Trash2,
  Upload,
  Image,
} from "lucide-react";
import api from "../services/api";
import type { InternalAxiosRequestConfig, AxiosError } from "axios";
import { Tab, TabList, TabPanel, Tabs } from "react-tabs";
import apiErrorToast from "../utils/apiErrorToast";
import WelcomeApiManager from "../utils/welcomeApiManager";
import InstitutionHeader from "../components/InstitutionHeader";
import InstitutionSidebar from "../components/InstitutionSidebar";

interface InstitutionStats {
  pendingApprovals: number;
  activeCampaigns: number;
  fundsRaised: number;
  totalStudents: number;
}

interface InstitutionProfile {
  institutionName: string;
  website: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  accountName: string;
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  branch: string;
  logoUrl?: string;
}

interface BankDetails {
  accountName: string;
  accountNumber: string;
  bankName: string;
  ifscCode: string;
  branch: string;
  lastUpdated: string;
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  goalAmount: number;
  raisedAmount: number;
  progressPercentage: number;
  studentRegId: string;
  studentName: string;
  studentEmail: string;
  institutionId: string;
  institutionName: string;
  status: string;
  category: string | null;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  supportersCount: number;

  // Optional or frontend-managed fields
  campaignTitle?: string;
  progress?: number;
  lastUpdated?: string;
  documents?: string[];
  milestones?: {
    title: string;
    status: "completed" | "pending" | "in-progress";
    dueDate: string;
  }[];
  studentDetails?: {
    course?: string;
    year?: string;
    phone?: string;
  };
}

interface DashboardSummary {
  pendingApprovals: number;
  activeCampaigns: number;
  fundsRaised: number;
  totalStudents: number;
}

interface RolePermissions {
  manageCampaigns: boolean;
  manageApprovals: boolean;
  manageInstitutionProfile: boolean;
  manageBankDetails: boolean;
  manageUsers: boolean;
  downloadReports: boolean;
}

const mapStats = (stats: any): DashboardSummary => ({
  pendingApprovals: stats.pendingApprovals,
  activeCampaigns: stats.activeCampaigns,
  fundsRaised: stats.fundsRaised || stats.totalFundsRaised || 0,
  totalStudents: stats.totalStudents,
});

const mapCampaign = (campaign: ApiCampaign): Campaign => ({
  id: String(campaign.id),
  title: campaign.title || "",
  description: campaign.description || "",
  goalAmount: campaign.goalAmount,
  raisedAmount: campaign.raisedAmount || 0,
  progressPercentage: campaign.progressPercentage ?? 0,
  studentRegId: String(campaign.studentRegId),
  studentName: campaign.studentName || "Unknown Student",
  studentEmail: campaign.studentEmail || "",
  institutionId: campaign.institutionId,
  institutionName: campaign.institutionName || "",
  status: campaign.status?.toLowerCase() || "active",
  category: campaign.category || "Other",
  startDate: campaign.startDate,
  endDate: campaign.endDate,
  createdAt: campaign.createdAt,
  updatedAt: campaign.updatedAt,
  supportersCount: campaign.supportersCount ?? 0,

  // Optional or frontend-managed fields
  campaignTitle: campaign.title,
  progress:
    campaign.goalAmount > 0
      ? Math.round(((campaign.raisedAmount || 0) / campaign.goalAmount) * 100)
      : 0,
  lastUpdated: new Date().toISOString(),
  documents: [],
  milestones: [],
  studentDetails: campaign.studentDetails,
});

const apiClient = api;

// Parse URL query parameters to set the active tab
const getInitialTab = ():
  | "dashboard"
  | "approvalRequests"
  | "settings"
  | "campaigns"
  | "reports" => {
  const params = new URLSearchParams(window.location.search);
  const tab = params.get("tab");

  if (!tab) {
    // Set dashboard as default tab in URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set("tab", "dashboard");
    window.history.replaceState({}, "", newUrl.toString());
    return "dashboard";
  }

  if (
    tab === "approvalRequests" ||
    tab === "settings" ||
    tab === "campaigns" ||
    tab === "dashboard" ||
    tab === "reports"
  ) {
    return tab;
  }

  return "dashboard";
};

export function InstitutionDashboard() {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth();

  const [isInitialized, setIsInitialized] = useState(false);
  const [showAccountNumber, setShowAccountNumber] = useState(false);

  const [users, setUsers] = useState<User[]>([]);

  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  const handleApiError = (error: any) => {
    const apiMessage = error?.response?.data?.message || error.message || 'An unexpected error occurred';
    const apiData = error?.response?.data?.data || {};
    let errorMessage = apiMessage;
    if (apiData && Object.keys(apiData).length > 0) {
      const validationErrors = Object.entries(apiData)
        .map(([field, err]) => `• ${field}: ${err}`)
        .join('\n');
      errorMessage = apiMessage + '\n\n' + validationErrors;
    }
    setErrorDetails({ message: errorMessage });
    setStatus('error');
  };

  // Simple interceptor that adds the token from react-oidc-context
  useEffect(() => {
    const hasValidAuth =
      auth.user && (auth.user.id_token || auth.user.access_token);
    if (!auth.isAuthenticated || !hasValidAuth) return;

    const interceptor = apiClient.interceptors.request.use(
      async (config) => {
        try {
          // Prefer id_token, fallback to access_token
          const token = auth.user?.id_token || auth.user?.access_token;
          if (token) {
            config.headers = {
              ...config.headers,
              Authorization: `Bearer ${token}`,
            };
            console.log("Added auth token to request:", config.url);
          }
        } catch (error) {
          console.error("Failed to get token for request:", error);
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    return () => {
      apiClient.interceptors.request.eject(interceptor);
    };
  }, [auth.isAuthenticated, auth.user]);

  const [activeTab, setActiveTab] = useState<
    "dashboard" | "approvalRequests" | "settings" | "campaigns" | "reports"
  >(getInitialTab());
  const [institutionProfile, setInstitutionProfile] =
    useState<InstitutionProfile>({
      institutionName: "",
      website: "",
      contactEmail: "",
      contactPhone: "",
      address: "",
      accountName: "",
      accountNumber: "",
      bankName: "",
      ifscCode: "",
      branch: "",
      logoUrl: "",
    });

  const [loadingProfile, setLoadingProfile] = useState(true);

  const [campaignFilter, setCampaignFilter] = useState<
    "all" | Campaign["status"]
  >("all");
  const [campaignSortField, setCampaignSortField] = useState("createdAt");
  const [campaignSortOrder, setCampaignSortOrder] = useState<"asc" | "desc">(
    "desc"
  );
  const [searchInput, setSearchInput] = useState("");
  const [searchStartDate, setSearchStartDate] = useState("");
  const [searchEndDate, setSearchEndDate] = useState("");
  const [searchStatus, setSearchStatus] = useState("all");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingBank, setIsEditingBank] = useState(false);
  const [showBankDetails, setShowBankDetails] = useState(false);
  const [profileForm, setProfileForm] = useState(institutionProfile);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [institutionLogo, setInstitutionLogo] = useState<string | null>(null);
  const [reportType, setReportType] = useState<string>("");
  const [campaignStatus, setCampaignStatus] = useState<string>("all");
  const [transactionType, setTransactionType] = useState<string>("all");
  const [timeFrame, setTimeFrame] = useState<string>("");
  const [customStartDate, setCustomStartDate] = useState<string>("");
  const [customEndDate, setCustomEndDate] = useState<string>("");

  const handleProfileChange = (field: string, value: string) => {
    setProfileForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size must be less than 5MB");
        return;
      }

      setLogoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

 const uploadLogo = async () => {
  if (!logoFile) {
    handleApiError({ response: { data: { message: 'Please select a logo file' } } });
    return;
  }

  try {
    setUploadingLogo(true);

    const formData = new FormData();
    formData.append("file", logoFile);

    const result = await institutionService.uploadInstitutionLogo(formData, auth);

    if (result.success) {
      toast.success("Logo uploaded successfully");
      setLogoFile(null);
      setLogoPreview(null);

      const profileData = await institutionService.getInstitutionProfile(auth);
      if (profileData) {
        setInstitutionProfile((prev) => ({
          ...prev,
          logoUrl: profileData.logoUrl || prev.logoUrl,
        }));
      }
    } else {
      handleApiError({ response: { data: { message: result.message || 'Failed to upload logo' } } });
    }
  } catch (error) {
    console.error("Logo upload error:", error);
    handleApiError(error);
  } finally {
    setUploadingLogo(false);
  }
};


  const handleToggleBankDetails = async () => {
    const newState = !showBankDetails;
    setShowBankDetails(newState);
    if (!showBankDetails && newState) {
      try {
        await institutionService.logBankDetailsView(auth);
      } catch (error) {
        console.error("Failed to log bank details view:", error);
      }
    }
  };

  // Add a new useEffect for welcome and roles API calls - only call once per session
  useEffect(() => {
    const callPostLoginAPIs = async () => {
      // For react-oidc-context, just check if user is authenticated and has user object
      const hasValidAuth = auth.isAuthenticated && auth.user;
      if (!auth.isAuthenticated || !hasValidAuth || !isInitialized) {
        console.log("Skipping post-login APIs. Auth not ready.");
        return;
      }

      // Use WelcomeApiManager to check if we should call the welcome API
      const currentUserId = auth.user?.sub || auth.user?.id || "unknown";

      if (WelcomeApiManager.shouldCallWelcomeApi(currentUserId)) {
        try {
          console.log("Calling welcome API from dashboard...");
          await institutionService.postInstitutionWelcome(auth);
          console.log("Welcome API called successfully from dashboard");

          // Mark that we've called the welcome API
          WelcomeApiManager.markWelcomeApiCalled(currentUserId);
        } catch (apiError) {
          console.error("Error calling welcome API from dashboard:", apiError);
          handleApiError(apiError);
        }
      } else {
        console.log(
          "Welcome API call skipped to prevent repetitive last_login logs"
        );
        // Log debug info for troubleshooting
        console.log(
          "Welcome API debug info:",
          WelcomeApiManager.getDebugInfo()
        );
      }

      // Always fetch roles (they might change)
      try {
        console.log("Calling roles API from dashboard...");
        const roles = await institutionService.getInstitutionRoles(auth);
        console.log("Roles API called successfully from dashboard:", roles);

        // Set user roles from API response
        if (roles && roles.roles) {
          const userRolesList = roles.roles;
          setUserRoles(userRolesList);

          // Set permissions based on roles
          const newPermissions: RolePermissions = {
            manageCampaigns: false,
            manageApprovals: false,
            manageInstitutionProfile: false,
            manageBankDetails: false,
            manageUsers: false,
            downloadReports: false,
          };

          // Apply permissions based on roles
          if (userRolesList.includes("INSTITUTION_ADMIN")) {
            // Institution Admin has all permissions
            newPermissions.manageCampaigns = true;
            newPermissions.manageApprovals = true;
            newPermissions.manageInstitutionProfile = true;
            newPermissions.manageBankDetails = true;
            newPermissions.manageUsers = true;
            newPermissions.downloadReports = true;
          } else if (userRolesList.includes("FINANCIAL_ADMIN")) {
            // Financial Admin has all except institution profile and user management
            newPermissions.manageCampaigns = true;
            newPermissions.manageApprovals = true;
            newPermissions.manageBankDetails = true;
            newPermissions.downloadReports = true;
          } else if (userRolesList.includes("CLERK")) {
            // Clerk can only create campaigns and download reports
            newPermissions.manageCampaigns = true;
            newPermissions.downloadReports = true;
          }
          // Other roles get no permissions (all remain false)

          setPermissions(newPermissions);

          // Store role as simple string (preferred format)
          const primaryRole = userRolesList[0]; // Take the first role as primary
          localStorage.setItem("institution_role", primaryRole);

          // Also store the full array for backward compatibility
          localStorage.setItem(
            "institution_roles",
            JSON.stringify(userRolesList)
          );
        }
        console.log("Roles stored in localStorage from dashboard");
      } catch (apiError) {
        console.error("Error calling roles API from dashboard:", apiError);
        handleApiError(apiError);
      }
    };

    callPostLoginAPIs();
  }, [auth.isAuthenticated, auth.user, isInitialized]);

  // Simple check for auth token and initialize

  useEffect(() => {
    const initializeDashboard = async () => {
      console.log("Initializing dashboard. Auth state:", {
        isLoading: auth.isLoading,
        isAuthenticated: auth.isAuthenticated,
        hasUser: !!auth.user,
        hasToken: !!auth.user?.id_token,
        userObject: auth.user,
        allAuthKeys: Object.keys(auth),
      });

      // Wait for auth to finish loading
      if (auth.isLoading) {
        console.log("Auth still loading, waiting...");
        return;
      }

      // Check OIDC authentication only
      if (!auth.isAuthenticated || !auth.user) {
        console.log("Not authenticated via OIDC, user needs to log in");
        // Don't redirect immediately, let the banner show
        return;
      }

      console.log("OIDC authentication check passed:", {
        isAuthenticated: auth.isAuthenticated,
        hasUser: !!auth.user,
      });

      // For react-oidc-context, check if user is authenticated and has user object
      // The tokens are managed internally by react-oidc-context
      console.log("Auth user object:", auth.user);
      console.log(
        "Auth user keys:",
        auth.user ? Object.keys(auth.user) : "no user"
      );

      // In react-oidc-context, if isAuthenticated is true and user exists, we have valid auth
      // Don't check for specific token properties as they may be stored differently
      // if (!auth.user) {
      //   console.log('No user object available, redirecting to login');
      //   toast.error('Authentication token missing. Please log in again.');
      //   navigate('/institution-login');
      //   return;
      // }

      try {
        console.log("Authenticated with valid token, initializing dashboard");
        setIsInitialized(true);
      } catch (error) {
        console.error("Error initializing dashboard:", error);
        handleApiError(error);
        // toast.error('Authentication error. Please log in again.');
        // navigate('/institution-login');
      }
    };

    initializeDashboard();
  }, [auth.isAuthenticated, auth.isLoading, auth.user, navigate]);

  const [activityPage, setActivityPage] = useState(1); // Pages are 1-based in UI
  const [activityPageSize, setActivityPageSize] = useState(5);
  const [totalActivityPages, setTotalActivityPages] = useState(1);

  const [activitySearchInput, setActivitySearchInput] = useState("");
  const [activitySearchKeyword, setActivitySearchKeyword] = useState("");


  const fetchDashboardData = async () => {
    // For react-oidc-context, just check if user is authenticated and has user object
    const hasValidAuth = auth.isAuthenticated && auth.user;

    if (
      activeTab !== "dashboard" ||
      !auth.isAuthenticated ||
      auth.isLoading ||
      !hasValidAuth ||
      !isInitialized
    ) {
      console.log("Skipping dashboard data fetch. Conditions not met:", {
        activeTab,
        isAuthenticated: auth.isAuthenticated,
        isLoading: auth.isLoading,
        hasValidAuth,
        isInitialized,
      });
      return;
    }

    try {
      setDashboardLoading(true);
      console.log("Loading dashboard data...");
      console.log("Auth object for API calls:", {
        isAuthenticated: auth.isAuthenticated,
        hasUser: !!auth.user,
        userKeys: auth.user ? Object.keys(auth.user) : [],
        id_token: auth.user?.id_token ? "present" : "missing",
        access_token: auth.user?.access_token ? "present" : "missing",
      });

      console.log("About to call API methods...");
      console.log("API base URL:", import.meta.env.VITE_API_BASE_URL);

      const activityPromise = institutionService
        .getDashboardActivities(auth, {
          keyword: activitySearchKeyword.trim() || undefined,
          page: activityPage - 1,
          size: activityPageSize,
          sort: "desc",
        })
        .catch((err) => {
          console.error("getDashboardActivities failed:", err);
          handleApiError(err);
          return { content: [], totalPages: 1 };
        });

      const [stats, activityResponse, logoUrl] = await Promise.all([
        institutionService.getDashboardSummary(auth).catch((err) => {
          console.error("getDashboardSummary failed:", err);
          handleApiError(err);
          return null;
        }),
        activityPromise,
        institutionService.getInstitutionLogo(auth).catch((err) => {
          console.error("getInstitutionLogo failed:", err);
          handleApiError(err);
          return null;
        }),
      ]);

      console.log("API responses received:", { stats, activityResponse, logoUrl });

      if (stats) {
        setDashboardStats({
          pendingApprovals: stats.pendingApprovals,
          activeCampaigns: stats.activeCampaigns,
          fundsRaised: stats.fundsRaised || stats.totalFundsRaised || 0,
          totalStudents: stats.totalStudents,
        });
      }

      if (activityResponse?.content) {
        setRecentActivities(activityResponse.content);
        setTotalActivityPages(activityResponse.totalPages || 1);
      }

      if (logoUrl) {
        setInstitutionLogo(logoUrl);
      }

      setError(null);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setError("Failed to load dashboard data. Please try again later.");
      handleApiError(error);
    } finally {
      setDashboardLoading(false);
    }
  };

  // Load dashboard tab data
  useEffect(() => {
    // For react-oidc-context, just check if user is authenticated and has user object
    const hasValidAuth = auth.isAuthenticated && auth.user;

    // Only fetch data if user is authenticated, not loading, has token, and dashboard is initialized
    if (
      auth.isAuthenticated &&
      !auth.isLoading &&
      hasValidAuth &&
      isInitialized &&
      activeTab === "dashboard"
    ) {
      console.log("Triggering fetchDashboardData from useEffect");
      fetchDashboardData();
    }
  }, [
    activeTab,
    auth.isAuthenticated,
    auth.isLoading,
    auth.user,
    isInitialized,
    activityPage,
    activityPageSize,
    activitySearchKeyword,
  ]);

  // Poll dashboard data every minute when the dashboard tab is active
  useEffect(() => {
    if (activeTab !== "dashboard") {
      return;
    }

    const id = setInterval(() => {
      fetchDashboardData();
    }, 60000);

    return () => clearInterval(id);
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === "settings") {
      const fetchSettingsData = async () => {
        try {
          setLoadingProfile(true);

          const [profileData, bankData, logoUrl] = await Promise.all([
            institutionService.getInstitutionProfile(auth),
            institutionService.getInstitutionBankDetails(auth),
            institutionService.getInstitutionLogo(auth),
          ]);

          setInstitutionProfile({
            institutionName: profileData?.name || "",
            website: profileData?.website || "",
            contactEmail: profileData?.email || "",
            contactPhone: profileData?.phone || "",
            address: profileData?.address || "",
            accountName: bankData?.accountName || "",
            accountNumber: bankData?.accountNumber || "",
            bankName: profileData?.bankName || "",
            ifscCode: bankData?.ifscCode || "",
            branch: profileData?.branch || "",
            logoUrl: logoUrl || profileData?.logoUrl || "",
          });
        } catch (error) {
          console.error("Failed to load institution settings:", error);
          handleApiError(error);
        } finally {
          setLoadingProfile(false);
        }
      };

      fetchSettingsData();
    }
  }, [activeTab]);

  useEffect(() => {
    if (institutionProfile) {
      setProfileForm(institutionProfile);
    }
  }, [institutionProfile]);

  // Handle sign out
  const handleSignOut = async () => {
    try {
      console.log("Starting institution sign out...");

      // Clear any local state first
      setDashboardStats(null);
      setRecentActivities([]);
      setCampaigns([]);
      setInstitution(null);

      // Clear any OIDC state from storage
      Object.keys(sessionStorage).forEach((key) => {
        if (key.startsWith("oidc.")) {
          sessionStorage.removeItem(key);
        }
      });

      // Clear any other auth-related storage
      localStorage.removeItem("id_token");
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("id_token");
      sessionStorage.removeItem("token");
      sessionStorage.removeItem("user");

      // Clear welcome API tracking
      WelcomeApiManager.clearWelcomeApiState();

      console.log("Local state and storage cleared");

      // Use custom Cognito logout instead of OIDC signout
      try {
        // Build the correct Cognito logout URL
        const cognitoDomain = import.meta.env.VITE_COGNITO_DOMAIN;
        const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
        const logoutUri = `${window.location.origin}${import.meta.env.VITE_BASE_PATH}/`;

        const logoutUrl = `${cognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(
          logoutUri
        )}`;

        console.log("Redirecting to Cognito logout URL:", logoutUrl);
        toast.success("Successfully signed out");

        // Redirect to Cognito logout
        window.location.href = logoutUrl;
      } catch (logoutError) {
        console.warn(
          "Custom logout failed, using direct redirect:",
          logoutError
        );

        // Fallback: Direct redirect to home page
        toast.success("Successfully signed out");
        window.location.href = "${import.meta.env.VITE_BASE_PATH}/";
      }
    } catch (error) {
      console.error("Error during sign out process:", error);
      handleApiError(error);

      // Always redirect to home page as fallback
      window.location.href = "${import.meta.env.VITE_BASE_PATH}/";
    }
  };

  // Handle refresh authentication
  const handleRefreshAuth = async () => {
    try {
      console.log("Refreshing authentication...");
      await auth.signinSilent();
      toast.success("Authentication refreshed successfully");
      window.location.reload(); // Reload to reinitialize
    } catch (error) {
      console.error("Failed to refresh authentication:", error);
      handleApiError(error);
      // navigate('/institution-login');
    }
  };

  // Update URL when component mounts
  useEffect(() => {
    updateTabInUrl(activeTab);
  }, []); // Run only once when component mounts

  // Sync active tab with URL changes
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get("tab") as
      | "dashboard"
      | "approvalRequests"
      | "settings"
      | "campaigns"
      | "reports"
      | null;
    const newTab = tabParam || "dashboard";
    if (newTab !== activeTab) {
      setActiveTab(newTab);
    }
  }, [location.search]);

  // Update URL when tab changes
  const updateTabInUrl = (
    tab: "dashboard" | "approvalRequests" | "settings" | "campaigns" | "reports"
  ) => {
    // Update the URL without reloading the page
    if (tab === "dashboard") {
      navigate("/institution-dashboard", { replace: true });
    } else {
      navigate(`/institution-dashboard?tab=${tab}`, { replace: true });
    }

    // Update the active tab state
    setActiveTab(tab);
  };
  // Removed selectedApproval state
  // Removed note modal state variables
  const [showBankModal, setShowBankModal] = useState(false);

  const [settingsTab, setSettingsTab] = useState<
    "profile" | "reports" | "users"
  >("profile");

  // New state variables for API integration
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [institution, setInstitution] = useState<Institution | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [campaignPage, setCampaignPage] = useState(1); // starts from 1 for MUI Pagination
  const [campaignPageSize, setCampaignPageSize] = useState(10);
  const [totalCampaignPages, setTotalCampaignPages] = useState(0);
  const [totalCampaignElements, setTotalCampaignElements] = useState(0);
  const [students, setStudents] = useState<Student[]>([]);
  const [pendingRegistrations, setPendingRegistrations] = useState<Student[]>(
    []
  );
  const [approvalRequests, setApprovalRequests] = useState<ApprovalRequest[]>(
    []
  );
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardSummary | null>(
    null
  );
  const [permissions, setPermissions] = useState<RolePermissions>({
    manageCampaigns: false,
    manageApprovals: false,
    manageInstitutionProfile: false,
    manageBankDetails: false,
    manageUsers: false,
    downloadReports: false,
  });
  const [userRoles, setUserRoles] = useState<string[]>([]);

  const [activeSubTab, setActiveSubTab] = useState<
    "profile" | "users" | "reports"
  >("profile");

  const [institutionUsers, setInstitutionUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [approvalSearchKeyword, setApprovalSearchKeyword] = useState("");

  const [searchKeyword, setSearchKeyword] = useState("");


  const searchTermDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [filterStartDate, setFilterStartDate] = useState<string>("");
  const [filterEndDate, setFilterEndDate] = useState<string>("");
  const [filterKeyword, setFilterKeyword] = useState<string>("");

  const [isFilterExpanded, setIsFilterExpanded] = useState<boolean>(false);

  // Report-related state variables
  const [reportStartDate, setReportStartDate] = useState("");
  const [reportEndDate, setReportEndDate] = useState("");
  const [reportCampaignStatus, setReportCampaignStatus] = useState("");
  const [reportApprovalStatus, setReportApprovalStatus] = useState("");
  const [reportTransactionType, setReportTransactionType] = useState("");
  const [reportFormats, setReportFormats] = useState({
    campaign: "pdf",
    approval: "pdf",
    transaction: "pdf",
  });
  const [isDownloading, setIsDownloading] = useState({
    campaign: false,
    approval: false,
    transaction: false,
  });

  // Delete user functionality moved to DeleteUserPage

  // Role management has been moved to AssignRolePage

  // Separate loading states for each tab
  const [dashboardLoading, setDashboardLoading] = useState(false);
  const [campaignsLoading, setCampaignsLoading] = useState(false);
  const [approvalRequestsLoading, setApprovalRequestsLoading] = useState(false);
  const [settingsLoading, setSettingsLoading] = useState(false);
  const [approvalPage, setApprovalPage] = useState(1); // MUI pages start at 1
  const [approvalPageSize, setApprovalPageSize] = useState(10);
  const [totalApprovalPages, setTotalApprovalPages] = useState(0);
  const [totalApprovalElements, setTotalApprovalElements] = useState(0);

  // State for multiple institutions
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [showInstitutionSelector, setShowInstitutionSelector] = useState(false);
  const institutionSelectorRef = React.useRef<HTMLDivElement>(null);

  // Frontend sorting has been removed as per requirements
  // Sorting should be handled by the backend API

  // Handle click outside to close institution selector
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        institutionSelectorRef.current &&
        !institutionSelectorRef.current.contains(event.target as Node)
      ) {
        setShowInstitutionSelector(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Load approval requests tab data
  // ✅ Define outside useEffect
  useEffect(() => {
    const fetchApprovalRequestsData = async () => {
      // For react-oidc-context, just check if user is authenticated and has user object
      const hasValidAuth = auth.isAuthenticated && auth.user;

      if (
        activeTab !== "approvalRequests" ||
        !auth.isAuthenticated ||
        auth.isLoading ||
        !hasValidAuth ||
        !isInitialized
      ) {
        return;
      }

      setApprovalRequestsLoading(true);

      try {
        console.log(
          `Loading approval requests — Page: ${approvalPage}, Size: ${approvalPageSize}`
        );

        let response;
        if (approvalSearchKeyword.trim()) {
          response = await approvalService.searchInstitutionApprovals(
            approvalSearchKeyword,
            approvalPage - 1,
            approvalPageSize
          );
          console.log("Search API used with keyword:", approvalSearchKeyword);
        } else {
          response = await approvalService.getInstitutionApprovals(
            approvalPage - 1,
            approvalPageSize
          );
          console.log("Default API used without search keyword");
        }

        setApprovalRequests(response.content);
        setTotalApprovalPages(response.totalPages);
        setTotalApprovalElements(response.totalElements);

        if (response.content.length === 0) {
          toast.info("No approval requests found");
        }
      } catch (error) {
        console.error("Error loading approval requests:", error);
        handleApiError(error);
      } finally {
        setApprovalRequestsLoading(false);
      }
    };

    fetchApprovalRequestsData();
  }, [
    activeTab,
    approvalPage,
    approvalPageSize,
    approvalSearchKeyword,
    auth.isAuthenticated,
    auth.isLoading,
    auth.user,
    isInitialized,
  ]); // ✅ Keyword triggers search

  // Remove duplicate renderApprovalRequestsTab function here

  // Fix getDashboardStats reference
  useEffect(() => {
    const fetchInstitutionProfile = async () => {
      // Ensure auth is ready before calling the API
      if (!auth.isAuthenticated || auth.isLoading || !auth.user || !isInitialized) {
        return;
      }

      try {
        setLoading(true);
        const profileData = await institutionService.getInstitutionProfile(auth);
        if (profileData) {
          setInstitution(profileData);
          document.title = `${profileData.name} Dashboard`;
        }
      } catch (error) {
        console.error("Error loading institution profile:", error);
        handleApiError(error);
      } finally {
        setLoading(false);
      }
    };

    fetchInstitutionProfile();
  }, [auth.user, auth.isAuthenticated, auth.isLoading, isInitialized]);

  // Remove duplicate renderApprovalRequestsTab function here

  const renderApprovalRequestsTab = () => {
    return (
        <div className="space-y-6 p-4 md:p-8">
        <div className="flex justify-between items-center">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
            Approval Management
          </h1>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-stretch sm:items-center">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search"
                className="w-full px-4 py-2 border rounded-lg"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    setApprovalSearchKeyword(searchTerm);
                    setApprovalPage(1);
                  }
                }}
              />
            </div>

            <button
              onClick={() => {
                setApprovalSearchKeyword(searchTerm);
                setApprovalPage(1);
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Search
            </button>

            <button
              onClick={() => {
                setSearchTerm("");
                setApprovalSearchKeyword("");
                setApprovalPage(1);
              }}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              Reset
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <p className="text-sm text-blue-700">
            <strong>Tip:</strong> Click on any row to view campaign details
          </p>
        </div>

        {/* Approval Requests Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {approvalRequests.length === 0 ? (
                <tr>
                  <td
                    colSpan={5}
                    className="px-6 py-4 text-center text-gray-500"
                  >
                    No approval requests found
                  </td>
                </tr>
              ) : (
                approvalRequests.map((request) => (
                  <tr
                    key={request.id}
                    onClick={() =>
                      navigate(
                        `/institution/campaign/${request.id}?from=approvals`
                      )
                    }
                    className="hover:bg-gray-50 cursor-pointer"
                  >
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">
                        {request.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {request.description}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {request.studentName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {request.studentEmail}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          request.status === "PENDING"
                            ? "bg-yellow-100 text-yellow-800"
                            : request.status === "APPROVED"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {request.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      ₹{request.goalAmount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4">
                      {request.status === "PENDING" && (
                        <div className="flex space-x-2">
                          {permissions.manageApprovals ? (
                            <>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(
                                    `/institution/campaign/${request.id}/approve`
                                  );
                                }}
                                className="text-green-600 hover:text-green-900"
                                title="Approve campaign"
                              >
                                <Check className="h-5 w-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(
                                    `/institution/campaign/${request.id}/reject`
                                  );
                                }}
                                className="text-red-600 hover:text-red-900"
                                title="Reject campaign"
                              >
                                <X className="h-5 w-5" />
                              </button>
                            </>
                          ) : (
                            <>
                              <button
                                disabled
                                className="text-gray-400 cursor-not-allowed"
                                title="You don't have permission to approve requests"
                              >
                                <Check className="h-5 w-5" />
                              </button>
                              <button
                                disabled
                                className="text-gray-400 cursor-not-allowed"
                                title="You don't have permission to reject requests"
                              >
                                <X className="h-5 w-5" />
                              </button>
                            </>
                          )}
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mt-6 px-4 py-2 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
            {/* Rows per page */}
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-gray-700 font-medium">Rows per page:</span>
              <select
                value={approvalPageSize}
                onChange={(e) => {
                  setApprovalPageSize(parseInt(e.target.value, 10));
                  setApprovalPage(1); // reset to first page
                }}
                className="px-2 py-1 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                {[5, 10, 20, 50].map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>

            {/* Pagination */}
            <div>
              <Pagination
                count={totalApprovalPages}
                page={approvalPage}
                onChange={(_, value) => setApprovalPage(value)}
                color="primary"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Load campaigns data
  const fetchCampaigns = async () => {
    setCampaignsLoading(true);
    setError(null);

    try {
      let response;

      const isSearchActive =
        filterKeyword ||
        filterStartDate ||
        filterEndDate ||
        campaignFilter !== "all";

      const sortParam = `${campaignSortField},${campaignSortOrder}`;

      if (isSearchActive) {
        console.log("📡 Calling: searchCampaigns() with filters:", {
         title: filterKeyword,

          startDate: filterStartDate,
          endDate: filterEndDate,
          status: campaignFilter !== "all" ? campaignFilter : undefined,
        });

        response = await institutionService.searchCampaigns(
          {
            title: filterKeyword,
            startDate: filterStartDate,
            endDate: filterEndDate,
            status: campaignFilter !== "all" ? campaignFilter : undefined,
          },
          campaignPage - 1,
          campaignPageSize,
          sortParam
        );
      } else {
        console.log("📡 Calling: getInstitutionCampaigns() with:", {
          page: campaignPage - 1,
          size: campaignPageSize,
          sort: sortParam,
        });

        response = await institutionService.getInstitutionCampaigns(
          campaignPage - 1,
          campaignPageSize,
          sortParam
        );
      }

      // 🔍 Debug: Log full response and check if ids are present
      console.log("✅ Campaigns fetched:", response.content);

      // Optional: Log all campaign ids specifically
      const campaignIds = response.content.map((c) => c.id);
      console.log("🆔 Campaign IDs:", campaignIds);

      setCampaigns(response.content); // make sure `id` is present here
      setTotalCampaignPages(response.totalPages);
      setTotalCampaignElements(response.totalElements);
    } catch (err) {
      console.error("❌ Failed to load campaigns:", err);
      setError("Failed to load campaigns");
    } finally {
      setCampaignsLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (campaignSortField === field) {
      setCampaignSortOrder(campaignSortOrder === "asc" ? "desc" : "asc");
    } else {
      setCampaignSortField(field);
      setCampaignSortOrder("asc");
    }

    // Reset to first page on sort
    setCampaignPage(1);
    fetchCampaigns();
  };

  useEffect(() => {
    if (activeTab === "campaigns") {
      fetchCampaigns();
    }
  }, [
    activeTab,
    campaignPage,
    campaignPageSize,
    filterKeyword,
    filterStartDate,
    filterEndDate,
    campaignFilter,
    campaignSortField,
    campaignSortOrder,
  ]);

  // For settings tab - fetch real users from API
  useEffect(() => {
    const fetchSettingsData = async () => {
      if (
        activeTab === "settings" &&
        institution &&
        auth.isAuthenticated &&
        auth.user
      ) {
        try {
          setSettingsLoading(true);
          console.log("Fetching institution users from API...");

          // Fetch real users from API
          const users = await institutionService.getInstitutionUsers(auth);
          console.log("Fetched users:", users);
          setInstitutionUsers(users);
          if (users.length === 0) {
            toast.info("No users found");
          }
        } catch (error) {
          console.error("Error fetching settings data:", error);
          handleApiError(error);
        } finally {
          setSettingsLoading(false);
        }
      }
    };

    fetchSettingsData();
  }, [activeTab, institution, auth.isAuthenticated, auth.user]);

  // Handle download report function
  const handleDownloadReport = (
    reportType: "campaign" | "approval" | "transaction"
  ) => {
    setIsDownloading((prev) => ({ ...prev, [reportType]: true }));

    let from = "";
    let to = "";
    let status = "";
    let type = "";

    const formatDate = (date: Date) => {
      return date.toLocaleDateString("en-CA"); // always gives yyyy-mm-dd
    };

    if (timeFrame === "custom") {
      from = customStartDate;
      to = customEndDate;
    } else {
      const now = new Date();
      let startDate: Date;
      let endDate: Date;

      switch (timeFrame) {
        case "this_month":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = now;
          break;
        case "this_quarter": {
          const currentQuarter = Math.floor(now.getMonth() / 3);
          startDate = new Date(now.getFullYear(), currentQuarter * 3, 1);
          endDate = now;
          break;
        }
        case "this_year":
          startDate = new Date(now.getFullYear(), 0, 1);
          endDate = now;
          break;
        case "last_month":
          startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          endDate = new Date(now.getFullYear(), now.getMonth(), 0);
          break;
        case "last_quarter": {
          const currentQuarter = Math.floor(now.getMonth() / 3);
          const lastQuarter = currentQuarter - 1;
          const year =
            lastQuarter < 0 ? now.getFullYear() - 1 : now.getFullYear();
          const startMonth = ((lastQuarter + 4) % 4) * 3;
          startDate = new Date(year, startMonth, 1);
          endDate = new Date(year, startMonth + 3, 0);
          break;
        }
        case "last_year":
          startDate = new Date(now.getFullYear() - 1, 0, 1);
          endDate = new Date(now.getFullYear() - 1, 11, 31);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = now;
      }

      from = formatDate(startDate);
      to = formatDate(endDate);
    }

    // Make sure both from and to are set
    if (!from || !to) {
      toast.error("Please select a valid date range");
      setIsDownloading((prev) => ({ ...prev, [reportType]: false }));
      return;
    }

    const format = reportFormats[reportType];

    // Set status for campaign reports (in uppercase)
   if (reportType === "campaign") {
  status = campaignStatus.toUpperCase() === "ALL" ? "" : campaignStatus.toUpperCase();
}


    // Set type for transaction reports (in uppercase)
   if (reportType === "transaction") {
  type = transactionType.toUpperCase() === "ALL" ? "" : transactionType.toUpperCase();
}


    // Prepare params object
    const params: any = { from, to, format };
    if (status) params.status = status;
    if (type) params.type = type;

    // Call your API with the correct parameters
    api
      .get(`/api/institution/v1/reports/${reportType}s`, {
        params,
        responseType: "blob", // Important for downloading files
      })
      .then((response) => {
        // Handle successful download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `${reportType}_report.${format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        toast.success(
          `${
            reportType.charAt(0).toUpperCase() + reportType.slice(1)
          } report downloaded successfully`
        );
      })
      .catch((error) => {
        console.error(`Error downloading ${reportType} report:`, error);
        handleApiError(error);
      })
      .finally(() => {
        setIsDownloading((prev) => ({ ...prev, [reportType]: false }));
      });
  };

  // Function to switch between institutions
  const switchInstitution = async (institutionId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Show toast to indicate switching is in progress
      const switchingToast = toast.loading(`Switching institution...`, {
        position: "top-center",
      });

      // Update the current institution
      const institutionData = await institutionService.getInstitutionById(
        institutionId
      );
      setInstitution(institutionData);

      // Close the institution selector
      setShowInstitutionSelector(false);

      // Update toast to success
      toast.update(switchingToast, {
        render: `Successfully switched to ${institutionData.name}`,
        type: "success",
        isLoading: false,
        autoClose: 3000,
        closeButton: true,
      });

      // The active tab's data will be loaded by the appropriate useEffect hook
    } catch (err: any) {
      console.error("Error switching institution:", err);

      let errorMessage = "Failed to switch institution. Please try again.";
      if (err.response) {
        if (err.response.status === 404) {
          errorMessage = "Institution not found. It may have been deleted.";
        } else if (err.response.status === 403) {
          errorMessage =
            "You do not have permission to access this institution.";
        }
      }

      setError(errorMessage);
      handleApiError(err);
    } finally {
      setLoading(false);
    }
  };

  const [bankDetails, setBankDetails] = useState<BankDetails>({
    accountName: "",
    accountNumber: "",
    bankName: "",
    ifscCode: "",
    branch: "",
    lastUpdated: new Date().toISOString().split("T")[0],
  });

  const [profileData, setProfileData] = useState({
    name: "",
    websiteUrl: "",
    email: "",
    contactPhone: "",
    address: "",
    city: "",
    state: "",
    postalCode: "",
    country: "",
  });

  const [notificationPreferences, setNotificationPreferences] = useState({
    emailNotifications: true,
    campaignUpdates: true,
    paymentAlerts: true,
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    passwordResetRequired: false,
    sessionTimeout: 30,
  });

  // Update institution data when loaded
  useEffect(() => {
    if (institution) {
      // Update bank details
      setBankDetails({
        accountName: institution.accountName || "",
        accountNumber: institution.accountNumber || "",
        bankName: institution.bankName || "",
        ifscCode: institution.ifscCode || "",
        branch: institution.bankBranch || "",
        lastUpdated: new Date().toISOString().split("T")[0],
      });

      // Update profile data with values from the institution object
      setProfileData({
        name: institution.name || "",
        websiteUrl: institution.websiteUrl || "",
        email: institution.email || "",
        contactPhone: institution.contactPhone || "",
        address: institution.address || "",
        city: institution.city || "",
        state: institution.state || "",
        postalCode: institution.postalCode || "",
        country: institution.country || "",
      });
    }
  }, [institution]);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const [activeApprovalTab, setActiveApprovalTab] = useState<
    "approvalRequests" | "studentRegistrations"
  >("approvalRequests");

  // Campaign table sorting has been removed as per requirements
  // Sorting should be handled by the backend API

  // Student registration approval/rejection
  const [showStudentApprovalConfirmation, setShowStudentApprovalConfirmation] =
    useState(false);
  const [
    showStudentRejectionConfirmation,
    setShowStudentRejectionConfirmation,
  ] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [studentApprovalReason, setStudentApprovalReason] = useState("");
  const [studentRejectionReason, setStudentRejectionReason] = useState("");

  const [selectedCampaignDetails, setSelectedCampaignDetails] =
    useState<Campaign | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Add new state for editing
  const [isEditing, setIsEditing] = useState(false);
  const [editableCampaign, setEditableCampaign] = useState<Campaign | null>(
    null
  );

  // Add validation function
  const validateCampaignDates = (
    startDate: string,
    endDate: string
  ): boolean => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    return start < end;
  };

  // Add save handler
  const handleSaveCampaign = async () => {
    if (!editableCampaign) return;

    if (
      !validateCampaignDates(
        editableCampaign.startDate,
        editableCampaign.endDate
      )
    ) {
      toast.error("End date must be after start date");
      return;
    }

    try {
      // Mock API call - replace with actual API call
      // await updateCampaign(editableCampaign);
      console.log("Saving campaign:", editableCampaign);
      toast.success("Campaign updated successfully");
      setIsEditing(false);
      // Update local state/refresh data
    } catch (error) {
      handleApiError(error);
    }
  };

  // Stats are now fetched from the API via dashboardStats

  // Users are now fetched from the API

  // All data is now fetched from the API

  // Student registrations are now fetched from the API via pendingRegistrations

  // Campaigns are now fetched from the API via campaigns state

  // Campaign details are now fetched from the API when needed

  // Removed isPotentiallyFakeCampaign function

  // Removed renderApprovalDetailsModal function

  // Removed unused renderApprovalRequests function

  const renderDashboardTab = () => {
    return (
      <div className="space-y-6 p-4 md:p-8">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Dashboard Overview</h2>
        </div>

        <div className="space-y-6">
          {/* Breadcrumb */}
          <Breadcrumb items={getBreadcrumbItems("dashboard")} />

          <div className="flex justify-between items-center">
            <div className="flex items-center">
              
              
              {institutions.length > 1 && (
                <div className="relative ml-4" ref={institutionSelectorRef}>
                  <button
                    onClick={() =>
                      setShowInstitutionSelector(!showInstitutionSelector)
                    }
                    className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 bg-blue-50 px-3 py-1 rounded-md"
                  >
                    <span>Switch Institution</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-4 w-4 transition-transform ${
                        showInstitutionSelector ? "rotate-180" : ""
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  {showInstitutionSelector && (
                    <div className="absolute z-10 mt-1 w-60 bg-white shadow-lg rounded-md border border-gray-200 py-1 max-h-60 overflow-y-auto">
                      {institutions.length === 0 ? (
                        <div className="px-4 py-2 text-gray-500">
                          No institutions found
                        </div>
                      ) : (
                        institutions.map((inst) => (
                          <button
                            key={inst.id}
                            onClick={() => {
                              if (institution?.id !== inst.id) {
                                switchInstitution(inst.id);
                              }
                              setShowInstitutionSelector(false);
                            }}
                            className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${
                              institution?.id === inst.id
                                ? "bg-blue-50 font-medium text-blue-600"
                                : ""
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span>{inst.name}</span>
                              {institution?.id === inst.id && (
                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                  Current
                                </span>
                              )}
                            </div>
                            {inst.address && (
                              <div className="text-xs text-gray-500 mt-1">
                                {inst.address}, {inst.city}
                              </div>
                            )}
                          </button>
                        ))
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className="text-right text-xs">
              <div>{auth.user?.name}</div>
              <div>{auth.user?.email}</div>
            </div>
          </div>

          {dashboardLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">
                Loading institution data...
              </span>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              <p className="font-medium">{error}</p>
              <p className="mt-2 text-sm">
                This could be due to one of the following reasons:
              </p>
              <ul className="list-disc ml-5 mt-1 text-sm">
                <li>
                  Your institution account is not properly set up in the
                  database
                </li>
                <li>There might be a connection issue with the server</li>
                <li>Your user account might not be linked to an institution</li>
              </ul>
              <div className="mt-4 flex flex-wrap gap-3">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200"
                >
                  Refresh Page
                </button>
                <button
                  onClick={() => {
                    if (auth.user && auth.isAuthenticated) {
                      setLoading(true);
                      setError(null);
                      // Try to fetch institutions again
                      // Get institution for current user
                      // institutionService.getInstitutionByUserId((auth.user as any)?.sub)
                      //   .then(institutionData => {
                      //     setInstitution(institutionData);
                      //     setInstitutions([institutionData]);
                      //     toast.success('Successfully reconnected to your institution');

                      //     // Check for auth token
                      //     const idToken = localStorage.getItem('id_token');
                      //     if (!idToken) {
                      //       throw new Error('No ID token found');
                      //     }

                      //     // Get dashboard summary
                      //     return institutionService.getDashboardSummary();
                      //   })
                      //   .then(stats => {
                      //     if (stats) {
                      //       setDashboardStats(stats);
                      //     }
                      //   })
                      //   .catch(err => {
                      //     console.error('Retry error:', err);
                      //     setError('Still unable to load your institution data. Please contact support.');
                      //   })
                      //   .finally(() => setLoading(false));

                      // Instead of calling API, just stop loading
                      setTimeout(() => {
                        toast.info("Skipped institution reload");
                        setLoading(false);
                      }, 1000);
                    }
                  }}
                  className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Stats Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                {[
                  {
                    label: "Pending Approvals",
                    value:
                      dashboardStats?.pendingApprovals ||
                      (Array.isArray(approvalRequests)
                        ? approvalRequests.filter(
                            (a) => a.status.toUpperCase() === "PENDING"
                          ).length
                        : 0),
                    color: "yellow",
                    icon: <ClipboardList className="h-6 w-6 text-yellow-500" />,
                  },
                  {
                    label: "Active Campaigns",
                    value:
                      dashboardStats?.activeCampaigns ||
                      (Array.isArray(campaigns)
                        ? campaigns.filter(
                            (c) => c.status.toUpperCase() === "ACTIVE"
                          ).length
                        : 0),
                    color: "green",
                    icon: <CheckCircle className="h-6 w-6 text-green-500" />,
                  },
                  {
                    label: "Funds Raised",
                    value: `₹ ${(
                      dashboardStats?.fundsRaised ||
                      (Array.isArray(campaigns)
                        ? campaigns.reduce(
                            (sum, campaign) =>
                              sum + (campaign.raisedAmount || 0),
                            0
                          )
                        : 0)
                    ).toLocaleString()}`,
                    color: "blue",
                    icon: <CreditCard className="h-6 w-6 text-blue-500" />,
                  },
                  {
                    label: "Total Students",
                    value:
                      dashboardStats?.totalStudents ||
                      (Array.isArray(students) ? students.length : 0),
                    color: "indigo",
                    icon: <Users className="h-6 w-6 text-indigo-500" />,
                  },
                ].map((stat, idx) => (
                  <div
                    key={idx}
                    className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">
                          {stat.label}
                        </p>
                        <p className="text-2xl font-semibold text-gray-900">
                          {stat.value}
                        </p>
                      </div>
                      {stat.icon}
                    </div>
                  </div>
                ))}
              </div>

              {/* Recent Activities */}
              <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                  <div className="flex items-center">
                    <h2 className="text-lg font-semibold">Recent Activities</h2>
                    {loading && (
                      <div className="ml-2 flex items-center">
                        <Loader className="h-4 w-4 animate-spin text-blue-500" />
                        <span className="ml-1 text-sm text-gray-500">
                          Refreshing...
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-2 md:mt-0">
                    <input
                      type="text"
                      value={activitySearchInput}
                      onChange={(e) => setActivitySearchInput(e.target.value)}
                      placeholder="Search activities"
                      className="px-2 py-1 border border-gray-300 rounded-md"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          setActivitySearchKeyword(activitySearchInput);
                          setActivityPage(1);
                        }
                      }}
                    />
                    <button
                      onClick={() => {
                        setActivitySearchKeyword(activitySearchInput);
                        setActivityPage(1);
                      }}
                      className="px-3 py-1 bg-blue-600 text-white rounded-md"
                    >
                      Search
                    </button>
                    {recentActivities.length > 0 && (
                      <button
                        onClick={async () => {
                          try {
                            setLoading(true);
                            setRecentActivities([]);

                            const activitiesResponse = await institutionService.getRecentActivities(
                              auth,
                              activityPage - 1,
                              activityPageSize
                            );

                            if (activitiesResponse?.content) {
                              setRecentActivities(activitiesResponse.content);
                              setTotalActivityPages(
                                activitiesResponse.totalPages || 1
                              );
                              toast.success("Activities refreshed");
                            } else {
                              console.error(
                                "Invalid response format:",
                                activitiesResponse
                              );
                              handleApiError({
                                response: { data: { message: 'Failed to refresh activities: Invalid response format' } }
                              });
                            }
                          } catch (err) {
                            console.error("Failed to refresh activities:", err);
                            handleApiError(err);
                          } finally {
                            setLoading(false);
                          }
                        }}
                        className="text-sm text-blue-600 hover:text-blue-800"
                        disabled={loading}
                      >
                        {loading ? "Loading..." : "Refresh"}
                      </button>
                    )}
                  </div>
                </div>
                <div className="space-y-4">
                  {loading && recentActivities.length === 0 ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader className="h-6 w-6 animate-spin text-blue-500" />
                      <span className="ml-2 text-gray-600">
                        Loading activities...
                      </span>
                    </div>
                  ) : recentActivities.length > 0 ? (
                    <>
                      {recentActivities.map((activity) => (
                        <div
                          key={activity.id}
                          onClick={() => navigate(`/activities/${activity.id}`)}
                          className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
                        >
                          <div className="flex-shrink-0">
                            {activity.action
                              ?.toLowerCase()
                              .includes("approve") ? (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            ) : activity.action
                                ?.toLowerCase()
                                .includes("reject") ? (
                              <XCircle className="h-5 w-5 text-red-500" />
                            ) : activity.action
                                ?.toLowerCase()
                                .includes("campaign") ? (
                              <FileText className="h-5 w-5 text-blue-500" />
                            ) : activity.action
                                ?.toLowerCase()
                                .includes("user") ? (
                              <UserPlus className="h-5 w-5 text-purple-500" />
                            ) : (
                              <Bell className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {activity.action}
                            </p>
                            <p className="text-sm text-gray-500">
                              {activity.description}
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              {new Date(activity.timestamp).toLocaleString(
                                "en-GB",
                                {
                                  day: "2-digit",
                                  month: "2-digit",
                                  year: "numeric",
                                  hour: "numeric",
                                  minute: "2-digit",
                                  second: "2-digit",
                                  hour12: true,
                                }
                              )}
                            </p>
                          </div>
                        </div>
                      ))}

                      {/* ✅ PAGINATION UI OUTSIDE LOOP */}
                      <div className="flex flex-col md:flex-row justify-between items-center gap-4 px-4 py-2 bg-gray-50 rounded-lg shadow-sm border border-gray-200 mt-4">
                        <div className="flex items-center space-x-2 text-sm">
                          <span className="text-gray-700 font-medium">
                            Rows per page:
                          </span>
                          <select
                            value={activityPageSize}
                            onChange={(e) => {
                              setActivityPageSize(parseInt(e.target.value, 10));
                              setActivityPage(1); // Reset to first page on size change
                            }}
                            className="px-2 py-1 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
                          >
                            {[5, 10, 20, 50].map((size) => (
                              <option key={size} value={size}>
                                {size}
                              </option>
                            ))}
                          </select>
                        </div>

                        <Pagination
                          count={totalActivityPages}
                          page={activityPage}
                          onChange={(_, value) => setActivityPage(value)}
                          color="primary"
                        />
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Bell className="h-10 w-10 text-gray-300 mx-auto mb-2" />
                      <p>No recent activities to display</p>
                      <p className="text-sm text-gray-400 mt-1">
                        Activities will appear here when there are updates to
                        campaigns, approvals, or user management
                      </p>
                      <button
                        onClick={async () => {
                          try {
                            const idToken = localStorage.getItem("id_token");
                            if (!idToken) {
                              handleApiError({
                                response: { data: { message: 'Authentication failed. Please log in again.' } }
                              });
                              return;
                            }

                            setLoading(true);
                            const data =
                              await institutionService.getRecentActivities(
                                auth,
                                activityPage - 1,
                                activityPageSize
                              );
                            setRecentActivities(data.content);
                            setTotalActivityPages(data.totalPages || 1);

                            if (data.content.length === 0) {
                              toast.info("No activities found in the database");
                            } else {
                              toast.success("Activities loaded successfully");
                            }
                          } catch (err) {
                            console.error("Failed to load activities:", err);
                            handleApiError(err);
                          } finally {
                            setLoading(false);
                          }
                        }}
                        className="mt-4 px-4 py-2 bg-blue-50 text-blue-600 rounded-md text-sm hover:bg-blue-100"
                      >
                        Try Again
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  const renderSettingsTab = () => {
    return (
      <Tabs>
        <TabList className="flex pl-2 flex-wrap sm:flex-nowrap gap-2 sm:space-x-4 border-b mb-4">
          <Tab className="px-4 py-2 font-medium cursor-pointer border-b-2 border-transparent hover:border-blue-500 focus:outline-none [&.react-tabs__tab--selected]:border-blue-600 [&.react-tabs__tab--selected]:text-blue-600">
            Profile
          </Tab>
          <Tab className="px-4 py-2 font-medium cursor-pointer border-b-2 border-transparent hover:border-blue-500 focus:outline-none [&.react-tabs__tab--selected]:border-blue-600 [&.react-tabs__tab--selected]:text-blue-600">
            Users
          </Tab>
        </TabList>

        <TabPanel>
          <div className=" p-6 rounded-lg shadow space-y-6  md:p-8">
            <h3 className="text-lg font-semibold text-gray-700 flex justify-between items-center">
              Institution Profile
              {isEditingProfile ? (
                permissions.manageInstitutionProfile ? (
                  <button
                    onClick={saveProfile}
                    disabled={savingProfile}
                    className={`${
                      savingProfile
                        ? "bg-green-400"
                        : "bg-green-600 hover:bg-green-700"
                    } text-white px-4 py-1 rounded flex items-center`}
                  >
                    {savingProfile ? (
                      <>
                        <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                ) : (
                  <button
                    disabled
                    className="bg-gray-400 text-white px-4 py-1 rounded cursor-not-allowed"
                    title="You don't have permission to save profile changes"
                  >
                    Save
                  </button>
                )
              ) : permissions.manageInstitutionProfile ? (
                <button
                  onClick={() => setIsEditingProfile(true)}
                  className="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700"
                >
                  Edit
                </button>
              ) : (
                <button
                  disabled
                  className="bg-gray-400 text-white px-4 py-1 rounded cursor-not-allowed"
                  title="You don't have permission to edit institution profile"
                >
                  Edit
                </button>
              )}
            </h3>

            {/* Institution Logo Section */}
            <div className="border-b pb-6">
              <h4 className="text-md font-medium text-gray-700 mb-4">
                Institution Logo
              </h4>
              <div className="flex items-start space-x-6">
                {/* Current Logo Display */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                    {institutionProfile.logoUrl || logoPreview ? (
                      <img
                        src={logoPreview || institutionProfile.logoUrl}
                        alt="Institution Logo"
                        className="w-full h-full object-contain rounded-lg"
                      />
                    ) : (
                      <div className="text-center">
                        <Image className="h-8 w-8 text-gray-400 mx-auto mb-1" />
                        <span className="text-xs text-gray-500">No Logo</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Logo Upload Controls */}
                <div className="flex-1">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Upload New Logo
                    </label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                        id="logo-upload"
                        disabled={!permissions.manageInstitutionProfile}
                      />
                      <label
                        htmlFor="logo-upload"
                        className={`${
                          permissions.manageInstitutionProfile
                            ? "cursor-pointer bg-blue-50 text-blue-600 hover:bg-blue-100"
                            : "cursor-not-allowed bg-gray-100 text-gray-400"
                        } px-4 py-2 rounded-md border border-blue-300 flex items-center`}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Choose File
                      </label>

                      {logoFile && (
                        <button
                          onClick={uploadLogo}
                          disabled={
                            uploadingLogo ||
                            !permissions.manageInstitutionProfile
                          }
                          className={`${
                            uploadingLogo
                              ? "bg-green-400"
                              : permissions.manageInstitutionProfile
                              ? "bg-green-600 hover:bg-green-700"
                              : "bg-gray-400 cursor-not-allowed"
                          } text-white px-4 py-2 rounded-md flex items-center`}
                        >
                          {uploadingLogo ? (
                            <>
                              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                              Uploading...
                            </>
                          ) : (
                            "Upload Logo"
                          )}
                        </button>
                      )}
                    </div>
                  </div>

                  {logoFile && (
                    <div className="text-sm text-gray-600 mb-2">
                      Selected: {logoFile.name} (
                      {(logoFile.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    <p>• Supported formats: JPG, PNG,SVG</p>
                    <p>• Maximum file size: 5MB</p>
                    <p>• Recommended size: 200x200 pixels</p>
                    <p>
                      • Logo will be displayed in campaigns and applications
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-600">
                  Institution Name
                </label>
              <input
  value={profileForm.institutionName}
  onChange={(e) => handleProfileChange("institutionName", e.target.value)}
  className={`input border px-3 py-2 w-full rounded-md ${
    !isEditingProfile
      ? "cursor-not-allowed focus:outline-none focus:ring-0 focus:border-gray-300"
      : ""
  }`}
  readOnly={!isEditingProfile}
/>


              </div>
              <div>
                <label className="block text-sm text-gray-600">Website</label>
                <input
                  value={profileForm.website}
                  onChange={(e) =>
                    handleProfileChange("website", e.target.value)
                  }
                 className={`input border px-3 py-2 w-full rounded-md ${
    !isEditingProfile
      ? "cursor-not-allowed focus:outline-none focus:ring-0 focus:border-gray-300"
      : ""
  }`}
                  readOnly={!isEditingProfile}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600">
                  Contact Email
                </label>
                <input
                  value={profileForm.contactEmail}
                  onChange={(e) =>
                    handleProfileChange("contactEmail", e.target.value)
                  }
                 className={`input border px-3 py-2 w-full rounded-md ${
    !isEditingProfile
      ? "cursor-not-allowed focus:outline-none focus:ring-0 focus:border-gray-300"
      : ""
  }`}
                  readOnly={!isEditingProfile}
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600">
                  Contact Phone
                </label>
                <input
                  value={profileForm.contactPhone}
                  onChange={(e) =>
                    handleProfileChange("contactPhone", e.target.value)
                  }
                 className={`input border px-3 py-2 w-full rounded-md ${
    !isEditingProfile
      ? "cursor-not-allowed focus:outline-none focus:ring-0 focus:border-gray-300"
      : ""
  }`}
                  readOnly={!isEditingProfile}
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm text-gray-600">Address</label>
                <textarea
                  value={profileForm.address}
                  onChange={(e) =>
                    handleProfileChange("address", e.target.value)
                  }
                 className={`input border px-3 py-2 w-full rounded-md ${
    !isEditingProfile
      ? "cursor-not-allowed focus:outline-none focus:ring-0 focus:border-gray-300"
      : ""
  }`}
                  readOnly={!isEditingProfile}
                />
              </div>
            </div>

            <div className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-700">
                  Bank Details
                </h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleToggleBankDetails}
                    className="bg-blue-50 text-blue-600 px-3 py-1 rounded hover:bg-blue-100 text-sm font-medium flex items-center"
                  >
                    {showBankDetails ? (
                      <>
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878a3 3 0 105.656 5.656m0 0L8.464 8.464M7.293 7.293l9.414 9.414"
                          />
                        </svg>
                        Hide Details
                      </>
                    ) : (
                      <>
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                        Show Details
                      </>
                    )}
                  </button>
                  {showBankDetails &&
                    (isEditingBank ? (
                      permissions.manageBankDetails ? (
                        <button
                          onClick={saveBankDetails}
                          disabled={savingBankDetails}
                          className={`${
                            savingBankDetails
                              ? "bg-green-400"
                              : "bg-green-600 hover:bg-green-700"
                          } text-white px-4 py-1 rounded flex items-center`}
                        >
                          {savingBankDetails ? (
                            <>
                              <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                              Saving...
                            </>
                          ) : (
                            "Save"
                          )}
                        </button>
                      ) : (
                        <button
                          disabled
                          className="bg-gray-400 text-white px-4 py-1 rounded cursor-not-allowed"
                          title="You don't have permission to save bank details"
                        >
                          Save
                        </button>
                      )
                    ) : permissions.manageBankDetails ? (
                      <button
                        onClick={() => setIsEditingBank(true)}
                        className="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700"
                      >
                        Edit
                      </button>
                    ) : (
                      <button
                        disabled
                        className="bg-gray-400 text-white px-4 py-1 rounded cursor-not-allowed"
                        title="You don't have permission to edit bank details"
                      >
                        Edit
                      </button>
                    ))}
                </div>
              </div>

              {!showBankDetails && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-yellow-600 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    <div>
                      <p className="text-sm font-medium text-yellow-800">
                        Bank details are hidden for security
                      </p>
                      <p className="text-xs text-yellow-700 mt-1">
                        Click "Show Details" to view and manage your bank
                        information
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {showBankDetails && (
              <div className="mt-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-blue-800 mb-2">
                    Bank Account Management
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>
                      • Ensure all bank details are accurate for successful fund
                      transfers
                    </li>
                    <li>
                      • Changes require verification and may take 24-48 hours to
                      process
                    </li>
                    <li>
                      • Contact support if you need to update the primary
                      account holder
                    </li>
                    <li>
                      • All transactions are secured with bank-grade encryption
                    </li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Account Holder Name
                    </label>
                    <input
                      value={profileForm.accountName}
                      onChange={(e) =>
                        handleProfileChange("accountName", e.target.value)
                      }
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        isEditingBank
                          ? "border-gray-300 bg-white"
                          : "border-gray-200 bg-gray-50"
                      }`}
                      readOnly={!isEditingBank}
                      placeholder="Enter account holder name"
                    />
                  </div>
                  <div className="relative">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Account Number
                    </label>
                    <input
                      value={profileForm.accountNumber}
                      onChange={(e) =>
                        handleProfileChange("accountNumber", e.target.value)
                      }
                      className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        isEditingBank
                          ? "border-gray-300 bg-white"
                          : "border-gray-200 bg-gray-50"
                      }`}
                      readOnly={!isEditingBank}
                      placeholder="Enter account number"
                      type={showAccountNumber ? "text" : "password"}
                    />
                    <button
                      type="button"
                      onClick={() => setShowAccountNumber(!showAccountNumber)}
                      className="absolute right-3 top-[38px] text-gray-600 focus:outline-none"
                    >
                      {showAccountNumber ? "🔒" : "👁️"}
                    </button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      IFSC Code
                    </label>
                    <input
                      value={profileForm.ifscCode}
                      onChange={(e) =>
                        handleProfileChange("ifscCode", e.target.value)
                      }
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        isEditingBank
                          ? "border-gray-300 bg-white"
                          : "border-gray-200 bg-gray-50"
                      }`}
                      readOnly={!isEditingBank}
                      placeholder="Enter IFSC code"
                    />
                  </div>
                </div>

                {isEditingBank && (
                  <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-start">
                      <svg
                        className="w-5 h-5 text-amber-600 mr-2 mt-0.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-amber-800">
                          Important Security Notice
                        </p>
                        <p className="text-xs text-amber-700 mt-1">
                          Double-check all information before saving. Incorrect
                          bank details may result in failed transactions or
                          delays in fund transfers.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </TabPanel>

        <TabPanel>
          <div className=" p-6 rounded-lg shadow">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 mb-4">
              <h3 className="text-lg font-semibold text-gray-700">
                Institution Users
              </h3>
              {permissions.manageUsers ? (
                <button
                  onClick={() => navigate("/institution/add-user")}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  + Add User
                </button>
              ) : (
                <button
                  disabled
                  className="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed"
                  title="You don't have permission to add users"
                >
                  + Add User
                </button>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-blue-800 mb-2">
                User Management Instructions
              </h4>
              <ul className="text-sm text-blue-700 space-y-2">
                <li>
                  <span className="font-medium">Adding a role:</span> Click the
                  "+ Add" button next to a user's roles to assign additional
                  roles.
                </li>
                <li>
                  <span className="font-medium">Removing a role:</span> Click
                  the "X" icon next to any role to remove it. A confirmation
                  page will appear.
                </li>
                <li>
                  <span className="font-medium">Deleting a user:</span> Click
                  the trash icon to permanently remove a user from the system.
                </li>
                <li>
                  <span className="font-medium">Note:</span> Users must have at
                  least one role. You cannot remove a user's last remaining
                  role.
                </li>
              </ul>
            </div>
            <div className="overflow-x-auto -mx-4 sm:mx-0">
              <table className="min-w-full table-auto border">
                <thead>
                  <tr className="bg-gray-100 text-left">
                    <th className="px-4 py-2">Name</th>
                    <th className="px-4 py-2">Email</th>
                    <th className="px-4 py-2">Role</th>
                    <th className="px-4 py-2">Status</th>
                    <th className="px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {institutionUsers.map((user, index) => (
                    <tr key={index} className="border-t">
                      <td className="px-4 py-2">{user.name}</td>
                      <td className="px-4 py-2">{user.email}</td>
                      <td className="px-4 py-2">
                        <div className="flex flex-wrap gap-1">
                          {Array.isArray(user.roles) ? (
                            user.roles.map((role) => (
                              <div
                                key={role}
                                className="flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                              >
                                <span>{role}</span>
                                {permissions.manageUsers && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigate(
                                        `/institution/revoke-role/${user.id}/${role}`
                                      );
                                    }}
                                    className="ml-1 text-blue-600 hover:text-blue-800"
                                    title={`Remove ${role} role`}
                                  >
                                    <X className="h-3 w-3" />
                                  </button>
                                )}
                              </div>
                            ))
                          ) : Array.isArray(user.role) ? (
                            user.role.map((role) => (
                              <div
                                key={role}
                                className="flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                              >
                                <span>{role}</span>
                                {permissions.manageUsers && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      navigate(
                                        `/institution/revoke-role/${user.id}/${role}`
                                      );
                                    }}
                                    className="ml-1 text-blue-600 hover:text-blue-800"
                                    title={`Remove ${role} role`}
                                  >
                                    <X className="h-3 w-3" />
                                  </button>
                                )}
                              </div>
                            ))
                          ) : (
                            <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {user.role}
                            </div>
                          )}
                          {permissions.manageUsers && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/institution/assign-role/${user.id}`);
                              }}
                              className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full hover:bg-gray-200"
                              title="Add role"
                            >
                              + Add
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        <span
                          className={`px-2 py-1 text-xs rounded-full text-white ${
                            user.status.toLowerCase() === "active"
                              ? "bg-green-600"
                              : "bg-gray-500"
                          }`}
                        >
                          {user.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex justify-center">
                          {/* Delete Button */}
                          {permissions.manageUsers ? (
                            <button
                              onClick={() =>
                                navigate(`/institution/delete-user/${user.id}`)
                              }
                              className="text-red-600 hover:text-red-800 p-1"
                              title="Delete user"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          ) : (
                            <button
                              disabled
                              className="text-gray-400 hover:text-gray-500 cursor-not-allowed p-1"
                              title="You don't have permission to delete users"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Delete User functionality moved to DeleteUserPage */}

            {/* Role assignment has been moved to a separate page */}
          </div>
        </TabPanel>
      </Tabs>
    );
  };

  // Removed renderNotificationPreviewModal function

  const renderReportsTab = () => {
    return (
      <div className="space-y-6 p-4 md:p-8">
        <div className="flex justify-between items-center">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
            Reports
          </h1>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">Reports</h3>
          <p className="mb-6 text-sm text-gray-600">
            Download detailed reports for campaigns, approvals, and
            transactions.
          </p>
{/* Report Type Filters */}
<div className="mb-6">
  <h4 className="text-sm font-medium text-gray-700 mb-2">
    Report Type Filters
  </h4>

  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
    {/* Report Type */}
    <div>
      <label htmlFor="reportType" className="block text-sm text-gray-600 mb-1">
        Report Type
      </label>
      <select
        id="reportType"
        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        onChange={(e) => setReportType(e.target.value)}
        value={reportType}
      >
        <option value="">Select Report Type</option>
        <option value="campaign">Campaign Report</option>
        <option value="approval">Approval Summary</option>
        <option value="transaction">Transaction History</option>
      </select>
    </div>

    {/* Time Frame */}
    <div>
      <label htmlFor="timeFrame" className="block text-sm text-gray-600 mb-1">
        Time Frame
      </label>
      <select
        id="timeFrame"
        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        onChange={(e) => setTimeFrame(e.target.value)}
        value={timeFrame}
      >
        <option value="">Select Time Frame</option>
        <option value="this_month">This Month</option>
        <option value="this_quarter">This Quarter</option>
        <option value="this_year">This Year</option>
        <option value="last_month">Last Month</option>
        <option value="last_quarter">Last Quarter</option>
        <option value="last_year">Last Year</option>
        <option value="custom">Custom Date Range</option>
      </select>
    </div>

    {/* Status / Type */}
    <div>
      {reportType === "campaign" && (
        <>
          <label className="block text-sm text-gray-600 mb-1">Status</label>
          <select
            value={campaignStatus}
            onChange={(e) => setCampaignStatus(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Statuses</option>
            <option value="PENDING">Pending</option>
            <option value="ACTIVE">Active</option>
            <option value="REJECTED">Rejected</option>
            <option value="TERMINATED">Terminated</option>
            <option value="COMPLETED">Completed</option>
          </select>
        </>
      )}

      {reportType === "transaction" && (
        <>
          <label className="block text-sm text-gray-600 mb-1">Type</label>
          <select
            value={transactionType}
            onChange={(e) => setTransactionType(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="SUCCESS">Success</option>
            <option value="FAILED">Failed</option>
            <option value="PENDING">Pending</option>
          </select>
        </>
      )}
    </div>

    {/* Custom Date Range Fields */}
    {timeFrame === "custom" && (
      <>
        <div>
          <label htmlFor="customStartDate" className="block text-sm text-gray-600 mb-1">
            Custom Start Date
          </label>
          <input
            type="date"
            id="customStartDate"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onChange={(e) => setCustomStartDate(e.target.value)}
            value={customStartDate}
          />
        </div>
        <div>
          <label htmlFor="customEndDate" className="block text-sm text-gray-600 mb-1">
            Custom End Date
          </label>
          <input
            type="date"
            id="customEndDate"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            onChange={(e) => setCustomEndDate(e.target.value)}
            value={customEndDate}
            min={customStartDate}
          />
        </div>
      </>
    )}
  </div>
</div>



          {/* Download Buttons */}
          <div className="flex flex-col sm:flex-row flex-wrap gap-4 mb-6">
            {permissions.downloadReports ? (
              <>
                {/* Campaign Report */}
                <div className="flex flex-col space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <select
                      className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={reportFormats.campaign}
                      onChange={(e) =>
                        setReportFormats({
                          ...reportFormats,
                          campaign: e.target.value,
                        })
                      }
                    >
                      <option value="pdf">PDF</option>
                      <option value="csv">CSV</option>
                    </select>
                    <button
                      className={`flex items-center px-4 py-2 rounded ${
                        !reportType || !timeFrame || reportType !== "campaign"
                          ? "bg-gray-400 cursor-not-allowed"
                          : isDownloading.campaign
                          ? "bg-blue-400 cursor-wait"
                          : "bg-blue-600 hover:bg-blue-700"
                      } text-white`}
                      disabled={
                        !reportType ||
                        !timeFrame ||
                        reportType !== "campaign" ||
                        isDownloading.campaign
                      }
                      onClick={() => handleDownloadReport("campaign")}
                    >
                      {isDownloading.campaign ? (
                        <>
                          <Loader className="h-4 w-4 animate-spin mr-2" />
                          Downloading...
                        </>
                      ) : (
                        <>Download Campaign Report</>
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Includes all campaign data, fundraising progress, and
                    student details
                  </p>
                </div>

                {/* Approval Report */}
                <div className="flex flex-col space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <select
                      className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={reportFormats.approval}
                      onChange={(e) =>
                        setReportFormats({
                          ...reportFormats,
                          approval: e.target.value,
                        })
                      }
                    >
                      <option value="pdf">PDF</option>
                      <option value="csv">CSV</option>
                    </select>
                    <button
                      className={`flex items-center px-4 py-2 rounded ${
                        !reportType || !timeFrame || reportType !== "approval"
                          ? "bg-gray-400 cursor-not-allowed"
                          : isDownloading.approval
                          ? "bg-green-400 cursor-wait"
                          : "bg-green-600 hover:bg-green-700"
                      } text-white`}
                      disabled={
                        !reportType ||
                        !timeFrame ||
                        reportType !== "approval" ||
                        isDownloading.approval
                      }
                      onClick={() => handleDownloadReport("approval")}
                    >
                      {isDownloading.approval ? (
                        <>
                          <Loader className="h-4 w-4 animate-spin mr-2" />
                          Downloading...
                        </>
                      ) : (
                        <>Download Approval Summary</>
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Includes approval requests, status changes, and reviewer
                    information
                  </p>
                </div>

                {/* Transaction Report */}
                <div className="flex flex-col space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <select
                      className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={reportFormats.transaction}
                      onChange={(e) =>
                        setReportFormats({
                          ...reportFormats,
                          transaction: e.target.value,
                        })
                      }
                    >
                      <option value="pdf">PDF</option>
                      <option value="csv">CSV</option>
                    </select>
                    <button
                      className={`flex items-center px-4 py-2 rounded ${
                        !reportType ||
                        !timeFrame ||
                        reportType !== "transaction"
                          ? "bg-gray-400 cursor-not-allowed"
                          : isDownloading.transaction
                          ? "bg-purple-400 cursor-wait"
                          : "bg-purple-600 hover:bg-purple-700"
                      } text-white`}
                      disabled={
                        !reportType ||
                        !timeFrame ||
                        reportType !== "transaction" ||
                        isDownloading.transaction
                      }
                      onClick={() => handleDownloadReport("transaction")}
                    >
                      {isDownloading.transaction ? (
                        <>
                          <Loader className="h-4 w-4 animate-spin mr-2" />
                          Downloading...
                        </>
                      ) : (
                        <>Download Transaction History</>
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Includes all financial transactions, payment methods, and
                    status
                  </p>
                </div>
              </>
            ) : (
              <>
                <button
                  disabled
                  className="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed"
                  title="You don't have permission to download reports"
                >
                  Download Campaign Report
                </button>
                <button
                  disabled
                  className="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed"
                  title="You don't have permission to download reports"
                >
                  Download Approval Summary
                </button>
                <button
                  disabled
                  className="bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed"
                  title="You don't have permission to download reports"
                >
                  Download Transaction History
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderCampaignsTab = () => {
    // If loading, show loading indicator
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <Loader className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">Loading campaigns...</span>
        </div>
      );
    }

    // If error, show error message
    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p className="font-medium">{error}</p>
          <p className="mt-2 text-sm">
            We're having trouble loading your campaigns. This could be because:
          </p>
          <ul className="list-disc ml-5 mt-1 text-sm">
            <li>Your institution account might not have any campaigns yet</li>
            <li>There might be a connection issue with the server</li>
            <li>The database might be experiencing temporary issues</li>
          </ul>
          <div className="mt-4 flex flex-wrap gap-3">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200"
            >
              Refresh Page
            </button>
            <button
              onClick={() => {
                if (institution) {
                  setLoading(true);
                  setError(null);
                  Promise.all([
                    //  institutionService.getInstitutionCampaigns(),
                    institutionService.getDashboardSummary(auth),
                  ])
                    .then(([campaignsData, stats]) => {
                      setCampaigns(campaignsData.map(mapCampaign));
                      const mappedStats: DashboardSummary = {
                        pendingApprovals: stats.pendingApprovals,
                        activeCampaigns: stats.activeCampaigns,
                        fundsRaised:
                          stats.fundsRaised || stats.totalFundsRaised || 0,
                        totalStudents: stats.totalStudents,
                      };
                      setDashboardStats(mappedStats);
                      toast.success("Campaigns loaded successfully");
                    })
                    .catch((err) => {
                      console.error("Failed to load campaigns:", err);
                      setError(
                        "Failed to load campaigns. Please contact support if this issue persists."
                      );
                      handleApiError(err);
                    })
                    .finally(() => setLoading(false));
                } else {
                  // If no institution is loaded, skip getInstitutionByUserId
                  // and notify user
                  setLoading(true);
                  // institutionService.getInstitutionByUserId(auth.user.id)
                  //   .then(institutionData => {
                  //     setInstitution(institutionData);
                  //     return Promise.all([
                  //       institutionService.getInstitutionCampaigns(),
                  //       institutionService.getDashboardStats()
                  //     ]);
                  //   })
                  //   .then(([campaignsData, stats]) => {
                  //     if (campaignsData && stats) {
                  //       setCampaigns(campaignsData.map(mapCampaign));
                  //       const mappedStats: DashboardSummary = {
                  //         pendingApprovals: stats.pendingApprovals,
                  //         activeCampaigns: stats.activeCampaigns,
                  //         fundsRaised: stats.fundsRaised || stats.totalFundsRaised || 0,
                  //         totalStudents: stats.totalStudents
                  //       };
                  //       setDashboardStats(mappedStats);
                  //       toast.success('Data loaded successfully');
                  //       setError(null);
                  //     }
                  //   })
                  //   .catch(err => {
                  //     console.error('Failed to load institution and campaigns:', err);
                  //     setError('Failed to load your institution data. Please try logging out and back in.');
                  //   })
                  //   .finally(() => setLoading(false));

                  // Fallback behavior if institution is null
                  setTimeout(() => {
                    toast.info(
                      "Skipped institution fetch. Set mock data if needed."
                    );
                    setLoading(false);
                  }, 1000);
                }
              }}
              className="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
            >
              Try Again
            </button>

            <button
              onClick={() => navigate("/institution/create-campaign")}
              className="px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
            >
              Create New Campaign
            </button>
          </div>
        </div>
      );
    }

    // Check if campaigns is an array before mapping
    console.log("Campaigns data:", campaigns);

    // Map API campaigns to the format expected by the UI
    const mappedCampaigns = Array.isArray(campaigns)
      ? campaigns.map((campaign) => ({
          id: campaign.id,
          title: campaign.title || "",
          description: campaign.description || "",
          goalAmount: campaign.goalAmount,
          studentName: campaign.studentName || "Unknown Student",
          studentRegId: campaign.studentRegId?.toString() || "Unknown ID",
          campaignTitle: campaign.title,
          raisedAmount: campaign.raisedAmount || 0,
          startDate: campaign.startDate,
          endDate: campaign.endDate,
          status: campaign.status?.toLowerCase() || "active",
          category: campaign.category || "Other",
          progress:
            campaign.goalAmount > 0
              ? Math.round(
                  ((campaign.raisedAmount || 0) / campaign.goalAmount) * 100
                )
              : 0,
          supporters: 0,
          lastUpdated: new Date().toISOString(),
          updatedAt: campaign.updatedAt,
          documents: [],
          milestones: [],
          studentDetails: campaign.studentDetails,
        }))
      : [];

    const filteredCampaigns = mappedCampaigns.filter((campaign) => {
      // Filter by status
      if (campaignFilter !== "all" && campaign.status !== campaignFilter) {
        return false;
      }

      // Filter by student name
     if (
  filterKeyword &&
  !campaign.studentName.toLowerCase().includes(filterKeyword.toLowerCase()) &&
  !campaign.campaignTitle?.toLowerCase().includes(filterKeyword.toLowerCase())
)
 {
        return false;
      }

      // Filter by creation timeframe (start date)
      if (filterStartDate) {
        const campaignStartDate = new Date(campaign.startDate);
        const filterStart = new Date(filterStartDate);
        if (campaignStartDate < filterStart) {
          return false;
        }
      }

      // Filter by creation timeframe (end date)
      if (filterEndDate) {
        const campaignStartDate = new Date(campaign.startDate);
        const filterEnd = new Date(filterEndDate);
        // Set the filter end date to the end of the day
        filterEnd.setHours(23, 59, 59, 999);
        if (campaignStartDate > filterEnd) {
          return false;
        }
      }

      return true;
    });

    // Navigation to campaign details is now handled directly in the UI

    return (
      <div className="space-y-6 p-4 md:p-8">
        {/* Breadcrumb */}
        <Breadcrumb items={getBreadcrumbItems("campaigns")} />

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
          <h1 className="text-2xl font-bold">Campaign Management</h1>
          <div className="flex space-x-2 sm:space-x-4">
            {permissions.manageCampaigns ? (
              <button
                onClick={() => navigate("/institution/create-campaign")}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Campaign
              </button>
            ) : (
              <button
                disabled
                className="bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed flex items-center"
                title="You don't have permission to create campaigns"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Campaign
              </button>
            )}
            <button
              onClick={() => setViewMode(viewMode === "list" ? "grid" : "list")}
              className="border rounded-lg px-3 py-2"
            >
              {viewMode === "list" ? "Grid View" : "List View"}
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <p className="text-sm text-blue-700">
            <strong>Tip:</strong> Click on any row to view campaign details
          </p>
        </div>

        {/* Search Panel */}
       <div className="bg-white rounded-lg shadow-md p-6">
  {/* Header */}
  <div className="flex items-center mb-6">
    <Search className="h-5 w-5 text-gray-400 mr-2" />
    <h2 className="text-lg font-semibold text-gray-800">Search Campaigns</h2>
  </div>

  {/* Filter Form */}
  <form
    onSubmit={(e) => {
      e.preventDefault();
     setFilterKeyword(searchInput);

      setFilterStartDate(searchStartDate);
      setFilterEndDate(searchEndDate);
      setCampaignFilter(searchStatus);
      setCampaignPage(1);
    }}
  >
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-4 lg:gap-8 items-end">
      {/* Search Input */}
      <div>
        <label
          htmlFor="studentName"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Search
        </label>
        <input
          type="text"
          id="studentName"
          placeholder="Campaign Search"
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              setFilterKeyword(searchInput);
              setFilterStartDate(searchStartDate);
              setFilterEndDate(searchEndDate);
              setCampaignFilter(searchStatus);
              setCampaignPage(1);
            }
          }}
        />
      </div>

      {/* Start Date */}
      <div>
        <label
          htmlFor="startDate"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Start Date
        </label>
        <input
          id="startDate"
          type="date"
          value={searchStartDate}
          onChange={(e) => setSearchStartDate(e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>

      {/* End Date */}
      <div>
        <label
          htmlFor="endDate"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          End Date
        </label>
        <input
          id="endDate"
          type="date"
          value={searchEndDate}
          onChange={(e) => setSearchEndDate(e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </div>

      {/* Status */}
      <div>
        <label
          htmlFor="status"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Status
        </label>
        <select
          id="status"
          value={searchStatus}
          onChange={(e) => setSearchStatus(e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          <option value="all">All</option>
          <option value="active">Active</option>
          <option value="completed">Completed</option>
          <option value="pending">Pending</option>
        </select>
      </div>
    </div>

    {/* Search and Reset Buttons */}
    <div className="flex justify-center sm:justify-end mt-6">
      <button
        type="submit"
        className="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm font-medium"
      >
        Search
      </button>
      <button
        type="button"
        onClick={() => {
          setSearchInput("");
          setSearchStartDate("");
          setSearchEndDate("");
          setSearchStatus("all");

         setFilterKeyword("");

          setFilterStartDate("");
          setFilterEndDate("");
          setCampaignFilter("all");
          setCampaignPage(1);
          fetchCampaigns();
        }}
        className="ml-2 px-5 py-2.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm font-medium"
      >
        Reset
      </button>
    </div>
  </form>
</div>


        {viewMode === "list" ? (
          <div className="overflow-x-auto -mx-4 sm:mx-0">
            {filteredCampaigns.length > 0 ? (
              <>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        onClick={() => handleSort("title")}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      >
                        <div className="flex items-center">
                          <span>Campaign Details</span>
                          {campaignSortField === "title" && (
                            <span className="ml-1">
                              {campaignSortOrder === "asc" ? "▲" : "▼"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th
                        onClick={() => handleSort("studentName")}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      >
                        <div className="flex items-center">
                          <span>Student</span>
                          {campaignSortField === "studentName" && (
                            <span className="ml-1">
                              {campaignSortOrder === "asc" ? "▲" : "▼"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th
                        onClick={() => handleSort("startDate")}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      >
                        <div className="flex items-center">
                          <span>Duration</span>
                          {campaignSortField === "startDate" && (
                            <span className="ml-1">
                              {campaignSortOrder === "asc" ? "▲" : "▼"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progress
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th
                        onClick={() => handleSort("goalAmount")}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      >
                        <div className="flex items-center">
                          <span>Amount</span>
                          {campaignSortField === "goalAmount" && (
                            <span className="ml-1">
                              {campaignSortOrder === "asc" ? "▲" : "▼"}
                            </span>
                          )}
                        </div>
                      </th>
                      <th
                        onClick={() => handleSort("updatedAt")}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      >
                        <div className="flex items-center">
                          <span>Last Modified</span>
                          {campaignSortField === "updatedAt" && (
                            <span className="ml-1">
                              {campaignSortOrder === "asc" ? "▲" : "▼"}
                            </span>
                          )}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredCampaigns.map((campaign) => (
                      <tr
                        key={campaign.id}
                        onClick={() =>
                          navigate(
                            `/institution/campaign/${campaign.id}?from=campaigns`
                          )
                        }
                        className="hover:bg-gray-50 cursor-pointer"
                      >
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900">
                            {campaign.campaignTitle}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            {campaign.studentName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {campaign.studentRegId}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            {new Date(campaign.startDate).toLocaleDateString(
                              "en-IN",
                              {
                                day: "numeric",
                                month: "short",
                                year: "numeric",
                              }
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            to{" "}
                            {new Date(campaign.endDate).toLocaleDateString(
                              "en-IN",
                              {
                                day: "numeric",
                                month: "short",
                                year: "numeric",
                              }
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full"
                              style={{ width: `${campaign.progress}%` }}
                            ></div>
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            ₹{(campaign.raisedAmount || 0).toLocaleString()} (
                            {campaign.progress}%)
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              campaign.status === "active"
                                ? "bg-green-100 text-green-800"
                                : campaign.status === "completed"
                                ? "bg-blue-100 text-blue-800"
                                : campaign.status === "paused"
                                ? "bg-yellow-100 text-yellow-800"
                                : campaign.status === "pending"
                                ? "bg-orange-100 text-orange-800"
                                : campaign.status === "rejected"
                                ? "bg-red-100 text-red-800"
                                : campaign.status === "terminated"
                                ? "bg-purple-100 text-purple-800"
                                : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {campaign.status.charAt(0).toUpperCase() +
                              campaign.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            ₹
                            {campaign.goalAmount != null
                              ? campaign.goalAmount.toLocaleString()
                              : "N/A"}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            {campaign.updatedAt}
                          </div>
                        
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* ✅ Pagination controls */}
                <div className="flex flex-col md:flex-row justify-between items-center gap-4 px-4 py-2 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
                  {/* Rows per page */}
                  <div className="flex items-center space-x-2 text-sm">
                    <span className="text-gray-700 font-medium">
                      Rows per page:
                    </span>
                    <select
                      value={campaignPageSize}
                      onChange={(e) => {
                        setCampaignPageSize(parseInt(e.target.value, 10));
                        setCampaignPage(1); // Reset to first page
                      }}
                      className="px-2 py-1 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      {[5, 10, 20, 50].map((size) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Page controls */}
                  <Pagination
                    count={totalCampaignPages}
                    page={campaignPage}
                    onChange={(_, value) => setCampaignPage(value)}
                    color="primary"
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Campaigns Found
                </h3>
                <p className="text-gray-500 mb-6">
                  There are no campaigns matching your current filter criteria.
                </p>
                <div className="flex flex-wrap justify-center gap-3">
                  <button
                    onClick={() => setCampaignFilter("all")}
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100"
                  >
                    View All Campaigns
                  </button>
                  {permissions.manageCampaigns ? (
                    <button
                      onClick={() => navigate("/institution/create-campaign")}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Create New Campaign
                    </button>
                  ) : (
                    <button
                      disabled
                      className="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed"
                      title="You don't have permission to create campaigns"
                    >
                      Create New Campaign
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="grid-view-container">
            {filteredCampaigns.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCampaigns.map((campaign) => (
                  <div
                    key={campaign.id}
                    className="bg-white rounded-lg shadow p-6"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-medium">
                          {campaign.campaignTitle}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {campaign.studentName}
                        </p>
                      </div>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          campaign.status === "active"
                            ? "bg-green-100 text-green-800"
                            : campaign.status === "completed"
                            ? "bg-blue-100 text-blue-800"
                            : campaign.status === "paused"
                            ? "bg-yellow-100 text-yellow-800"
                            : campaign.status === "pending"
                            ? "bg-orange-100 text-orange-800"
                            : campaign.status === "rejected"
                            ? "bg-red-100 text-red-800"
                            : campaign.status === "terminated"
                            ? "bg-purple-100 text-purple-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {campaign.status.charAt(0).toUpperCase() +
                          campaign.status.slice(1)}
                      </span>
                    </div>
                    <div className="mt-4 text-sm text-gray-500">
                      <div className="flex justify-between mb-2">
                        <span>Start Date:</span>
                        <span>
                          {new Date(campaign.startDate).toLocaleDateString(
                            "en-IN",
                            {
                              day: "numeric",
                              month: "short",
                              year: "numeric",
                            }
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between mb-4">
                        <span>End Date:</span>
                        <span>
                          {new Date(campaign.endDate).toLocaleDateString(
                            "en-IN",
                            {
                              day: "numeric",
                              month: "short",
                              year: "numeric",
                            }
                          )}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${campaign.progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm text-gray-500 mt-1">
                        <span>₹{campaign.raisedAmount.toLocaleString()}</span>
                        <span>{campaign.progress}%</span>
                      </div>
                    </div>
                    <button
                      onClick={() =>
                        navigate(
                          `/institution/campaign/${campaign.id}?from=campaigns`
                        )
                      }
                      className="mt-4 w-full bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100"
                    >
                      View Details
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-white rounded-lg shadow">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Campaigns Found
                </h3>
                <p className="text-gray-500 mb-6">
                  There are no campaigns matching your current filter criteria.
                </p>
                <div className="flex flex-wrap justify-center gap-3">
                  <button
                    onClick={() => setCampaignFilter("all")}
                    className="px-4 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100"
                  >
                    View All Campaigns
                  </button>
                  {permissions.manageCampaigns ? (
                    <button
                      onClick={() => navigate("/institution/create-campaign")}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      Create New Campaign
                    </button>
                  ) : (
                    <button
                      disabled
                      className="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed"
                      title="You don't have permission to create campaigns"
                    >
                      Create New Campaign
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Campaign Details Modal */}
        {showDetailsModal && selectedCampaignDetails && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-6">
                  <h2 className="text-2xl font-semibold">
                    {isEditing
                      ? "Edit Campaign"
                      : selectedCampaignDetails.campaignTitle}
                  </h2>
                  <div className="flex gap-2">
                    {!isEditing ? (
                      permissions.manageCampaigns ? (
                        <button
                          onClick={() => {
                            setIsEditing(true);
                            setEditableCampaign({
                              ...selectedCampaignDetails,
                              category:
                                selectedCampaignDetails.category as Campaign["category"],
                              status:
                                selectedCampaignDetails.status as Campaign["status"],
                              milestones:
                                selectedCampaignDetails.milestones.map(
                                  (m: any) => ({
                                    ...m,
                                    status: m.status as
                                      | "completed"
                                      | "in-progress"
                                      | "pending",
                                  })
                                ),
                            } as Campaign);
                          }}
                          className="text-blue-600 hover:text-blue-800 px-3 py-1 rounded border border-blue-600"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          disabled
                          className="text-gray-400 px-3 py-1 rounded border border-gray-400 cursor-not-allowed"
                          title="You don't have permission to edit campaigns"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      )
                    ) : (
                      <>
                        <button
                          onClick={handleSaveCampaign}
                          className="text-green-600 hover:text-green-800 px-3 py-1 rounded border border-green-600"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            setEditableCampaign(null);
                          }}
                          className="text-red-600 hover:text-red-800 px-3 py-1 rounded border border-red-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => {
                        setShowDetailsModal(false);
                        setIsEditing(false);
                        setEditableCampaign(null);
                      }}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                {/* Campaign Title */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Campaign Title
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editableCampaign?.campaignTitle}
                      onChange={(e) =>
                        setEditableCampaign((prev) =>
                          prev
                            ? { ...prev, campaignTitle: e.target.value }
                            : null
                        )
                      }
                      className="w-full p-2 border rounded-md"
                    />
                  ) : (
                    <div className="text-lg">
                      {selectedCampaignDetails.campaignTitle}
                    </div>
                  )}
                </div>

                {/* Campaign Duration */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <h3 className="text-lg font-medium mb-3">
                    Campaign Duration
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm text-gray-500 mb-1">
                        Start Date
                      </label>
                      {isEditing ? (
                        <input
                          type="date"
                          value={editableCampaign?.startDate}
                          onChange={(e) =>
                            setEditableCampaign((prev) =>
                              prev
                                ? { ...prev, startDate: e.target.value }
                                : null
                            )
                          }
                          className="w-full p-2 border rounded-md"
                        />
                      ) : (
                        <div className="font-medium">
                          {new Date(
                            selectedCampaignDetails.startDate
                          ).toLocaleDateString("en-IN", {
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                          })}
                        </div>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm text-gray-500 mb-1">
                        End Date
                      </label>
                      {isEditing ? (
                        <input
                          type="date"
                          value={editableCampaign?.endDate}
                          onChange={(e) =>
                            setEditableCampaign((prev) =>
                              prev ? { ...prev, endDate: e.target.value } : null
                            )
                          }
                          className="w-full p-2 border rounded-md"
                        />
                      ) : (
                        <div className="font-medium">
                          {new Date(
                            selectedCampaignDetails.endDate
                          ).toLocaleDateString("en-IN", {
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Target Amount */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Amount
                  </label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editableCampaign?.goalAmount}
                      onChange={(e) =>
                        setEditableCampaign((prev) =>
                          prev
                            ? { ...prev, goalAmount: Number(e.target.value) }
                            : null
                        )
                      }
                      className="w-full p-2 border rounded-md"
                    />
                  ) : (
                    <div className="text-lg">
                      ₹{selectedCampaignDetails.goalAmount.toLocaleString()}
                    </div>
                  )}
                </div>

                {/* Campaign Status */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  {isEditing ? (
                    <select
                      value={editableCampaign?.status}
                      onChange={(e) =>
                        setEditableCampaign((prev) =>
                          prev
                            ? {
                                ...prev,
                                status: e.target.value as Campaign["status"],
                              }
                            : null
                        )
                      }
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="active">Active</option>
                      <option value="completed">Completed</option>
                      <option value="paused">Paused</option>
                    </select>
                  ) : (
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        selectedCampaignDetails.status === "active"
                          ? "bg-green-100 text-green-800"
                          : selectedCampaignDetails.status === "completed"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {selectedCampaignDetails.status.charAt(0).toUpperCase() +
                        selectedCampaignDetails.status.slice(1)}
                    </span>
                  )}
                </div>

                {/* Campaign Progress */}
                <div className="mb-6">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-semibold">
                      ₹{selectedCampaignDetails.raisedAmount.toLocaleString()}{" "}
                      of ₹{selectedCampaignDetails.goalAmount.toLocaleString()}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{ width: `${selectedCampaignDetails.progress}%` }}
                    />
                  </div>
                  <div className="flex justify-between mt-2 text-sm text-gray-500">
                    <span>
                      Total Supporters: {selectedCampaignDetails.supporters}
                    </span>
                    <span>{selectedCampaignDetails.progress}% Funded</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Campaign Details</h3>
                      <p className="text-gray-600">
                        {selectedCampaignDetails.description}
                      </p>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Progress</h3>
                      <div className="flex items-center space-x-2">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{
                              width: `${selectedCampaignDetails.progress}%`,
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">
                          {selectedCampaignDetails.progress}%
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Target Amount</p>
                        <p className="text-lg font-medium">
                          ₹{selectedCampaignDetails.goalAmount}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Raised Amount</p>
                        <p className="text-lg font-medium">
                          ₹{selectedCampaignDetails.raisedAmount}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Student Details</h3>
                      <div className="space-y-2">
                        <p>
                          <span className="font-medium">Name:</span>{" "}
                          {selectedCampaignDetails.studentName}
                        </p>
                        <p>
                          <span className="font-medium">ID:</span>{" "}
                          {selectedCampaignDetails.studentRegId}
                        </p>
                        <p>
                          <span className="font-medium">Course:</span>{" "}
                          {selectedCampaignDetails.studentDetails?.course}
                        </p>
                        <p>
                          <span className="font-medium">Year:</span>{" "}
                          {selectedCampaignDetails.studentDetails?.year}
                        </p>

                        {selectedCampaignDetails?.studentDetails?.phone && (
                          <p>
                            <span className="font-medium">Phone:</span>{" "}
                            {selectedCampaignDetails.studentDetails.phone}
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Milestones</h3>
                      <div className="space-y-2">
                        {selectedCampaignDetails.milestones.map(
                          (milestone: any, index: number) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  milestone.status === "completed"
                                    ? "bg-green-500"
                                    : milestone.status === "in-progress"
                                    ? "bg-yellow-500"
                                    : "bg-gray-500"
                                }`}
                              ></div>
                              <p className="text-sm">{milestone.title}</p>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Add state for sidebar visibility
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [operationStatus, setOperationStatus] = useState<"idle" | "success" | "error">("idle");
  const [operationType, setOperationType] = useState<
    "profile" | "bank" | "report"
  >("profile");

  // Add toggle function
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Add these state variables for loading indicators
  const [savingProfile, setSavingProfile] = useState(false);
  const [savingBankDetails, setSavingBankDetails] = useState(false);

  const saveProfile = async () => {
    try {
      // Set loading state
      setSavingProfile(true);

      // Check if auth is available
      if (!auth.isAuthenticated || !auth.user) {
        toast.error("Authentication required");
        setSavingProfile(false);
        return;
      }

      // Pass both profile data and auth object
      const result = await institutionService.updateInstitutionProfile(
        {
          name: profileForm.institutionName,
          website: profileForm.website,
          email: profileForm.contactEmail,
          phone: profileForm.contactPhone,
          address: profileForm.address,
        },
        auth
      );

      if (result.success) {
        setOperationType("profile");
        setOperationStatus("success");
        setIsEditingProfile(false);

        // Refresh the profile data
        const profileData = await institutionService.getInstitutionProfile(
          auth
        );
        if (profileData) {
          setInstitutionProfile((prev) => ({
            ...prev,
            institutionName: profileData.name || prev.institutionName,
            website: profileData.website || prev.website,
            contactEmail: profileData.email || prev.contactEmail,
            contactPhone: profileData.phone || prev.contactPhone,
            address: profileData.address || prev.address,
          }));
        }
      } else {
        setOperationType("profile");
        setOperationStatus("error");
        setIsEditingProfile(false);
      }
    } catch (error) {
      setOperationType("profile");
      setOperationStatus("error");
      console.error(error);
      setIsEditingProfile(false);
    } finally {
      // Reset loading state
      setSavingProfile(false);
    }
  };

  const saveBankDetails = async () => {
    try {
      // Set loading state
      setSavingBankDetails(true);

      const result = await institutionService.updateInstitutionBankDetails(
        {
          accountName: profileForm.accountName,
          accountNumber: profileForm.accountNumber,
          ifscCode: profileForm.ifscCode,
        },
        auth
      );

      if (result.success) {
        setOperationType("bank");
        setOperationStatus("success");
        setIsEditingBank(false);

        // Refresh bank details
        const bankData = await institutionService.getInstitutionBankDetails(
          auth
        );
        if (bankData) {
          setInstitutionProfile((prev) => ({
            ...prev,
            accountName: bankData.accountName || prev.accountName,
            accountNumber: bankData.accountNumber || prev.accountNumber,
            ifscCode: bankData.ifscCode || prev.ifscCode,
          }));
        }
      } else {
        setOperationType("bank");
        setOperationStatus("error");
        setIsEditingBank(false);
      }
    } catch (error) {
      setOperationType("bank");
      setOperationStatus("error");
      console.error(error);
      setIsEditingBank(false);
    } finally {
      // Reset loading state
      setSavingBankDetails(false);
    }
  };

  // Role management is now handled in the AssignRolePage

  // Role management is now handled in the RevokeRolePage

  // Role assignment is now handled in the AssignRolePage

  // Status page handling
  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  if (operationStatus === "success") {
    const messages = {
      profile: {
        title: "Profile Updated Successfully",
        message: "Your institution profile has been updated and saved.",
        actionText: "Back to Settings",
      },
      bank: {
        title: "Bank Details Updated Successfully",
        message: "Your bank details have been updated and saved.",
        actionText: "Back to Settings",
      },
      report: {
        title: "Report Downloaded Successfully",
        message:
          "Your report has been generated and downloaded to your device.",
        actionText: "Back to Reports",
      },
    };

    return (
      <StatusPage
        type="success"
        title={messages[operationType].title}
        message={messages[operationType].message}
        actionText={messages[operationType].actionText}
        onAction={() => setOperationStatus("idle")}
      />
    );
  }

  if (operationStatus === "error") {
    const messages = {
      profile: {
        title: "Failed to Update Profile",
        message: "We couldn't save your profile changes. Please try again.",
        actionText: "Try Again",
      },
      bank: {
        title: "Failed to Update Bank Details",
        message: "We couldn't save your bank details. Please try again.",
        actionText: "Try Again",
      },
      report: {
        title: "Failed to Download Report",
        message: "We couldn't generate your report. Please try again.",
        actionText: "Try Again",
      },
    };

    return (
      <StatusPage
        type="error"
        title={messages[operationType].title}
        message={messages[operationType].message}
        actionText={messages[operationType].actionText}
        onAction={() => setOperationStatus("idle")}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab={activeTab}
          onSignOut={handleSignOut}
        />

        {/* Main Content */}
        <div className="flex-1 overflow-auto bg-gray-100">
        {activeTab === "dashboard" &&
          (dashboardLoading ? (
            <div className="flex p-4 md:p-8 justify-center items-center h-64">
              <Loader className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">
                Loading dashboard data...
              </span>
            </div>
          ) : (
            renderDashboardTab()
          ))}

        {activeTab === "settings" &&
          (settingsLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">
                Loading settings data...
              </span>
            </div>
          ) : (
            renderSettingsTab()
          ))}

        {activeTab === "approvalRequests" &&
          (approvalRequestsLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">
                Loading approval requests data...
              </span>
            </div>
          ) : (
            renderApprovalRequestsTab()
          ))}

        {activeTab === "campaigns" &&
          (campaignsLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader className="h-8 w-8 animate-spin text-blue-500" />
              <span className="ml-2 text-gray-600">
                Loading campaigns data...
              </span>
            </div>
          ) : (
            renderCampaignsTab()
          ))}

        {activeTab === "reports" && renderReportsTab()}
        </div>
      </div>

      {/* Removed note modal */}

      {/* Bank Details Update Modal */}
      {showBankModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Update Bank Details</h2>
              <button
                onClick={() => setShowBankModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault();
                // Handle form submission here
                setShowBankModal(false);
                toast.success("Bank details updated successfully");
              }}
            >
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Account Holder Name
                  </label>
                  <input
                    type="text"
                    defaultValue={bankDetails.accountName}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Account Number
                  </label>
                  <input
                    type="text"
                    defaultValue={bankDetails.accountNumber}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Bank Name
                  </label>
                  <input
                    type="text"
                    defaultValue={bankDetails.bankName}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    IFSC Code
                  </label>
                  <input
                    type="text"
                    defaultValue={bankDetails.ifscCode}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Branch
                  </label>
                  <input
                    type="text"
                    defaultValue={bankDetails.branch}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
                  />
                </div>

                <div className="mt-6 flex items-center p-4 bg-yellow-50 rounded-md">
                  <CreditCard className="h-5 w-5 text-yellow-400" />
                  <p className="ml-3 text-sm text-yellow-700">
                    Changes to bank details require verification. Please ensure
                    all information is accurate.
                  </p>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={() => setShowBankModal(false)}
                    className="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Update Details
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Removed approval details and notification preview modals */}

      {/* Campaign Details Modal */}
      {selectedCampaignDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Campaign Details
              </h2>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      selectedCampaignDetails.status === "active"
                        ? "bg-green-100 text-green-800"
                        : selectedCampaignDetails.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedCampaignDetails.status === "completed"
                        ? "bg-blue-100 text-blue-800"
                        : selectedCampaignDetails.status === "paused"
                        ? "bg-gray-100 text-gray-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {selectedCampaignDetails.status?.charAt(0).toUpperCase() +
                      selectedCampaignDetails.status?.slice(1)}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-2">
                    {!isEditing ? (
                      permissions.manageCampaigns ? (
                        <button
                          onClick={() => {
                            setIsEditing(true);
                            setEditableCampaign({
                              ...selectedCampaignDetails,
                              category:
                                selectedCampaignDetails.category as Campaign["category"],
                              status:
                                selectedCampaignDetails.status as Campaign["status"],
                              milestones:
                                selectedCampaignDetails.milestones.map(
                                  (m: any) => ({
                                    ...m,
                                    status: m.status as
                                      | "completed"
                                      | "in-progress"
                                      | "pending",
                                  })
                                ),
                            } as Campaign);
                          }}
                          className="text-blue-600 hover:text-blue-800 px-3 py-1 rounded border border-blue-600"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          disabled
                          className="text-gray-400 px-3 py-1 rounded border border-gray-400 cursor-not-allowed"
                          title="You don't have permission to edit campaigns"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      )
                    ) : (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            setEditableCampaign(null);
                          }}
                          className="text-gray-600 hover:text-gray-800 px-3 py-1 rounded border border-gray-600"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSaveCampaign}
                          className="text-green-600 hover:text-green-800 px-3 py-1 rounded border border-green-600"
                        >
                          Save
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => setSelectedCampaignDetails(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Student Approval Confirmation Modal */}
      {showStudentApprovalConfirmation && selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirm Student Approval
            </h3>
            <p className="mb-4 text-gray-700">
              Are you sure you want to approve the registration for{" "}
              <span className="font-medium">{selectedStudent.name}</span>?
            </p>

            <div className="mb-4">
              <label
                htmlFor="studentApprovalReason"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Reason for Approval (<span className="italic text-gray-500">Optional</span>)
              </label>
              <textarea
                id="studentApprovalReason"
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter reason for approval..."
                value={studentApprovalReason}
                onChange={(e) => setStudentApprovalReason(e.target.value)}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowStudentApprovalConfirmation(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  try {
                    setLoading(true);
                    // Update student approval status with reason
                    await institutionService.updateApprovalStatus(
                      selectedStudent.id,
                      "approved",
                      studentApprovalReason || undefined
                    );

                    toast.success(
                      `Student ${selectedStudent.name} approved successfully`
                    );

                    // Refresh the pending registrations
                    const pendingRegs =
                      await institutionService.getPendingStudentRegistrations();
                    setPendingRegistrations(pendingRegs);

                    setShowStudentApprovalConfirmation(false);
                  } catch (error) {
                    console.error("Error approving student:", error);
                    handleApiError(error);
                  } finally {
                    setLoading(false);
                  }
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Approve
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Campaign Details Modal */}
      {selectedCampaignDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                Campaign Details
              </h2>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span
                    className={`px-3 py-1 rounded-full text-sm font-medium ${
                      selectedCampaignDetails.status === "active"
                        ? "bg-green-100 text-green-800"
                        : selectedCampaignDetails.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedCampaignDetails.status === "completed"
                        ? "bg-blue-100 text-blue-800"
                        : selectedCampaignDetails.status === "paused"
                        ? "bg-gray-100 text-gray-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {selectedCampaignDetails.status?.charAt(0).toUpperCase() +
                      selectedCampaignDetails.status?.slice(1)}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-2">
                    {!isEditing ? (
                      permissions.manageCampaigns ? (
                        <button
                          onClick={() => {
                            setIsEditing(true);
                            setEditableCampaign({
                              ...selectedCampaignDetails,
                              category:
                                selectedCampaignDetails.category as Campaign["category"],
                              status:
                                selectedCampaignDetails.status as Campaign["status"],
                              milestones:
                                selectedCampaignDetails.milestones.map(
                                  (m: any) => ({
                                    ...m,
                                    status: m.status as
                                      | "completed"
                                      | "in-progress"
                                      | "pending",
                                  })
                                ),
                            } as Campaign);
                          }}
                          className="text-blue-600 hover:text-blue-800 px-3 py-1 rounded border border-blue-600"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          disabled
                          className="text-gray-400 px-3 py-1 rounded border border-gray-400 cursor-not-allowed"
                          title="You don't have permission to edit campaigns"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      )
                    ) : (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            setEditableCampaign(null);
                          }}
                          className="text-gray-600 hover:text-gray-800 px-3 py-1 rounded border border-gray-600"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleSaveCampaign}
                          className="text-green-600 hover:text-green-800 px-3 py-1 rounded border border-green-600"
                        >
                          Save
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <button
                  onClick={() => setSelectedCampaignDetails(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Student Approval Confirmation Modal */}
      {showStudentApprovalConfirmation && selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirm Student Approval
            </h3>
            <p className="mb-4 text-gray-700">
              Are you sure you want to approve the registration for{" "}
              <span className="font-medium">{selectedStudent.name}</span>?
            </p>

            <div className="mb-4">
              <label
                htmlFor="studentApprovalReason"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Reason for Approval (<span className="italic text-gray-500">Optional</span>)
              </label>
              <textarea
                id="studentApprovalReason"
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter reason for approval..."
                value={studentApprovalReason}
                onChange={(e) => setStudentApprovalReason(e.target.value)}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowStudentApprovalConfirmation(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  try {
                    setLoading(true);
                    // Update student approval status with reason
                    await institutionService.updateApprovalStatus(
                      selectedStudent.id,
                      "approved",
                      studentApprovalReason || undefined
                    );

                    toast.success(
                      `Student ${selectedStudent.name} approved successfully`
                    );

                    // Refresh the pending registrations
                    const pendingRegs =
                      await institutionService.getPendingStudentRegistrations();
                    setPendingRegistrations(pendingRegs);

                    setShowStudentApprovalConfirmation(false);
                  } catch (error) {
                    console.error("Error approving student:", error);
                    handleApiError(error);
                  } finally {
                    setLoading(false);
                  }
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Approve
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Student Rejection Confirmation Modal */}
      {showStudentRejectionConfirmation && selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirm Student Rejection
            </h3>
            <p className="mb-4 text-gray-700">
              Are you sure you want to reject the registration for{" "}
              <span className="font-medium">{selectedStudent.name}</span>?
            </p>

            <div className="mb-4">
              <label
                htmlFor="studentRejectionReason"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Reason for Rejection <span className="text-red-500">*</span>
              </label>
              <textarea
                id="studentRejectionReason"
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter reason for rejection..."
                value={studentRejectionReason}
                onChange={(e) => setStudentRejectionReason(e.target.value)}
                required
              />
              {studentRejectionReason.trim() === "" && (
                <p className="text-sm text-red-500 mt-1">
                  Please provide a reason for rejection
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowStudentRejectionConfirmation(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  if (studentRejectionReason.trim() === "") {
                    toast.error("Please provide a reason for rejection");
                    return;
                  }

                  try {
                    setLoading(true);
                    // Update student rejection status with reason
                    await institutionService.updateApprovalStatus(
                      selectedStudent.id,
                      "rejected",
                      studentRejectionReason
                    );

                    toast.success(
                      `Student ${selectedStudent.name} rejected successfully`
                    );

                    // Refresh the pending registrations
                    const pendingRegs =
                      await institutionService.getPendingStudentRegistrations();
                    setPendingRegistrations(pendingRegs);

                    setShowStudentRejectionConfirmation(false);
                  } catch (error) {
                    console.error("Error rejecting student:", error);
                    handleApiError(error);
                  } finally {
                    setLoading(false);
                  }
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                disabled={studentRejectionReason.trim() === ""}
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
