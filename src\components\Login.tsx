import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudentAuth } from '../context/StudentAuthContext';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import { toast } from 'react-toastify';
import { SocialLogin } from './SocialLogin';
import { ChevronLeft, Mail, Lock, Eye, EyeOff } from 'lucide-react';
 import studentService from '../services/studentService'; // Make sure this is imported
import StatusPage from './StatusPage';

interface LoginProps {
  userType: 'student' | 'supporter' | 'institution';
}



export function Login({ userType }: LoginProps) {
  const navigate = useNavigate();
  const { login: studentLogin, loading: studentLoading } = useStudentAuth();
  const { login: supporterLogin, loading: supporterLoading } = useSupporterAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);


  // Color schemes for different user types
  const colorSchemes = {
    student: {
      primary: 'bg-blue-600',
      hover: 'hover:bg-blue-700',
      light: 'bg-blue-50',
      text: 'text-blue-600',
      border: 'border-blue-600',
    },
    supporter: {
      primary: 'bg-green-600',
      hover: 'hover:bg-green-700',
      light: 'bg-green-50',
      text: 'text-green-600',
      border: 'border-green-600',
    },
    institution: {
      primary: 'bg-purple-600',
      hover: 'hover:bg-purple-700',
      light: 'bg-purple-50',
      text: 'text-purple-600',
      border: 'border-purple-600',
    }
  };

  // Get color scheme based on user type
  const colors = colorSchemes[userType];






const handleFormSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setIsLoading(true);

  try {
    const credentials = {
      email: formData.email,
      password: formData.password
    };

    // Call the appropriate login method based on user type
    let response;
    if (userType === 'student') {
      response = await studentLogin(credentials, rememberMe);
    } else if (userType === 'supporter') {
      response = await supporterLogin(credentials, rememberMe);
    }

    toast.success('Welcome back! 👋');

    // Navigate based on user type
    if (userType === 'student') {
      const check = await studentService.checkLoggedInStudent();
      if (check.exists && check.status === 'ACTIVE' || check.status === 'INVITED') {
        navigate('/students');
      } else {
         if (check.exists && check.status !== 'ACTIVE' && check.status !== 'INVITED') {
          toast.info('Your student account is not active yet.');
        }
        navigate('/student-registration');
      }
    } else if (userType === 'supporter') {
      // Check if there's a redirect URL stored in session storage
      const redirectUrl = sessionStorage.getItem('login_redirect');
      if (redirectUrl) {
        // Clear the stored URL to prevent future unwanted redirects
        sessionStorage.removeItem('login_redirect');
        navigate(redirectUrl);
      } else {
        navigate('/supporters');
      }
    }

 } catch (error) {
  console.error('Login error:', error);
  const message = error instanceof Error ? error.message : 'Login failed. Please check your credentials.';
  setErrorDetails({ message });
  setStatus('error');
  setFormData(prev => ({ ...prev, password: '' }));
}
 finally {
    setIsLoading(false);
  }
};

if (status === 'error') {
  return (
    <StatusPage
      type="error"
      title="Login Failed"
      message={errorDetails?.message || ''}
      actionText="Try Again"
      onAction={() => {
        setStatus('idle');
        setErrorDetails(null);
      }}
    />
  );
}



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white shadow-sm">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/auth')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <span className="ml-2 font-semibold text-xl">EDU-FUND</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto mt-12 px-4">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-2">
              Welcome Back
            </h2>
            <p className="text-gray-600">
              Sign in to your {userType} account
            </p>
          </div>

          {/* Social Login - Moved to top for student and supporter */}
          {userType !== 'institution' && (
            <div className="mb-8">
              <SocialLogin userType={userType as 'student' | 'supporter'} />

              <div className="mt-8 relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
              </div>
            </div>
          )}

          {/* Login Form */}
          {userType === 'institution' && (
            <form onSubmit={handleFormSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className={`block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none ${colors.text.replace('text-', 'focus:ring-')} ${colors.text.replace('text-', 'focus:border-')}`}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="current-password"
                    required
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className={`block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none ${colors.text.replace('text-', 'focus:ring-')} ${colors.text.replace('text-', 'focus:border-')}`}
                    placeholder="••••••••"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className={`h-4 w-4 ${colors.text.replace('text-', 'text-')} focus:ring-purple-500 border-gray-300 rounded`}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                    Remember me
                  </label>
                </div>


              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${colors.primary} ${colors.hover} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 ${
                    isLoading ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isLoading ? 'Signing in...' : 'Sign in'}
                </button>
              </div>


            </form>
          )}
        </div>
      </div>
    </div>
  );
}





