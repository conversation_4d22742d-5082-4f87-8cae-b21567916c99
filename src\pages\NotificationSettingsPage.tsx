// import React, { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { ChevronLeft, Save, Bell, Mail, MessageSquare, Clock, Users, AlertTriangle, Info, Check } from 'lucide-react';
// import { toast } from 'react-toastify';
// import { useAuth } from '../context/AuthContext';
// import institutionService from '../services/institutionService';
// import StatusPage from '../components/StatusPage';
// import InstitutionSidebar from '../components/InstitutionSidebar';

// interface NotificationSettings {
//   emailNotifications: boolean;
//   smsNotifications: boolean;
//   appNotifications: boolean;
//   campaignUpdates: boolean;
//   approvalAlerts: boolean;
//   paymentAlerts: boolean;
//   dailyDigest: boolean;
//   weeklyReport: boolean;
// }

// interface NotificationChannel {
//   id: string;
//   name: string;
//   description: string;
//   icon: React.ReactNode;
//   enabled: boolean;
// }

// interface NotificationEvent {
//   id: string;
//   name: string;
//   description: string;
//   channels: {
//     email: boolean;
//     sms: boolean;
//     app: boolean;
//   };
// }

// export function NotificationSettingsPage() {
//   const navigate = useNavigate();
//   const { user, logout } = useAuth();
//   const [isLoading, setIsLoading] = useState(true);
//   const [isSaving, setIsSaving] = useState(false);
//   const [hasChanges, setHasChanges] = useState(false);
//   const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

//   const [sidebarOpen, setSidebarOpen] = useState(true);

//   const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

//   const handleSignOut = () => {
//     logout('/');
//     toast.success('Successfully signed out');
//     setTimeout(() => {
//       window.location.href = '${import.meta.env.VITE_BASE_PATH}/';
//     }, 100);
//   };
  
//   // Basic notification settings
//   const [settings, setSettings] = useState<NotificationSettings>({
//     emailNotifications: true,
//     smsNotifications: false,
//     appNotifications: true,
//     campaignUpdates: true,
//     approvalAlerts: true,
//     paymentAlerts: true,
//     dailyDigest: false,
//     weeklyReport: true
//   });
  
//   // Notification channels
//   const [channels, setChannels] = useState<NotificationChannel[]>([
//     {
//       id: 'email',
//       name: 'Email Notifications',
//       description: 'Receive notifications via email',
//       icon: <Mail className="h-5 w-5 text-blue-500" />,
//       enabled: true
//     },
//     {
//       id: 'sms',
//       name: 'SMS Notifications',
//       description: 'Receive notifications via SMS',
//       icon: <MessageSquare className="h-5 w-5 text-green-500" />,
//       enabled: false
//     },
//     {
//       id: 'app',
//       name: 'In-App Notifications',
//       description: 'Receive notifications within the application',
//       icon: <Bell className="h-5 w-5 text-purple-500" />,
//       enabled: true
//     }
//   ]);
  
//   // Notification events
//   const [events, setEvents] = useState<NotificationEvent[]>([
//     {
//       id: 'new_campaign',
//       name: 'New Campaign Submission',
//       description: 'When a student submits a new campaign for approval',
//       channels: { email: true, sms: false, app: true }
//     },
//     {
//       id: 'campaign_update',
//       name: 'Campaign Updates',
//       description: 'When a campaign is updated or modified',
//       channels: { email: true, sms: false, app: true }
//     },
//     {
//       id: 'approval_request',
//       name: 'Approval Requests',
//       description: 'When a campaign needs your approval',
//       channels: { email: true, sms: true, app: true }
//     },
//     {
//       id: 'approval_reminder',
//       name: 'Approval Reminders',
//       description: 'Reminders for pending approval requests',
//       channels: { email: true, sms: false, app: true }
//     },
//     {
//       id: 'campaign_approved',
//       name: 'Campaign Approved',
//       description: 'When a campaign is approved by an administrator',
//       channels: { email: true, sms: false, app: true }
//     },
//     {
//       id: 'campaign_rejected',
//       name: 'Campaign Rejected',
//       description: 'When a campaign is rejected by an administrator',
//       channels: { email: true, sms: false, app: true }
//     },
//     {
//       id: 'campaign_completed',
//       name: 'Campaign Completed',
//       description: 'When a campaign reaches its target or end date',
//       channels: { email: true, sms: false, app: true }
//     }
//   ]);
  
//   // Fetch notification settings
//   useEffect(() => {
//     const fetchSettings = async () => {
//       if (!user?.profileId) return;
      
//       try {
//         setIsLoading(true);
        
//         // In a real implementation, this would fetch from the API
//         // For now, we'll simulate a delay and use default values
//         await new Promise(resolve => setTimeout(resolve, 1000));
        
//         // Simulate API response
//         const response = {
//           emailNotifications: true,
//           smsNotifications: false,
//           appNotifications: true,
//           campaignUpdates: true,
//           approvalAlerts: true,
//           paymentAlerts: true,
//           dailyDigest: false,
//           weeklyReport: true
//         };
        
//         setSettings(response);
//         setHasChanges(false);
//       } catch (error) {
//         console.error('Error fetching notification settings:', error);
//         toast.error('Failed to load notification settings');
//       } finally {
//         setIsLoading(false);
//       }
//     };
    
//     fetchSettings();
//   }, [user]);
  
//   // Toggle a channel
//   const toggleChannel = (channelId: string) => {
//     const updatedChannels = channels.map(channel => 
//       channel.id === channelId 
//         ? { ...channel, enabled: !channel.enabled } 
//         : channel
//     );
    
//     setChannels(updatedChannels);
//     setHasChanges(true);
//   };
  
//   // Toggle an event channel
//   const toggleEventChannel = (eventId: string, channelId: keyof NotificationEvent['channels']) => {
//     const updatedEvents = events.map(event => 
//       event.id === eventId 
//         ? { 
//             ...event, 
//             channels: { 
//               ...event.channels, 
//               [channelId]: !event.channels[channelId] 
//             } 
//           } 
//         : event
//     );
    
//     setEvents(updatedEvents);
//     setHasChanges(true);
//   };
  
//   // Toggle a setting
//   const toggleSetting = (settingId: keyof NotificationSettings) => {
//     setSettings(prev => ({
//       ...prev,
//       [settingId]: !prev[settingId]
//     }));
    
//     setHasChanges(true);
//   };
  
//   // Save settings
//   const saveSettings = async () => {
//     if (!user?.profileId) return;
    
//     try {
//       setIsSaving(true);
      
//       // In a real implementation, this would call the API
//       // For now, we'll simulate a delay
//       await new Promise(resolve => setTimeout(resolve, 1500));
      
//       // Combine all settings for the API call
//       const combinedSettings = {
//         ...settings,
//         channels: channels.reduce((acc, channel) => ({
//           ...acc,
//           [channel.id]: channel.enabled
//         }), {}),
//         events: events.reduce((acc, event) => ({
//           ...acc,
//           [event.id]: event.channels
//         }), {})
//       };
      
//       console.log('Saving notification settings:', combinedSettings);
      
//       // Simulate API call
//       // await institutionService.updateNotificationPreferences(user.profileId, combinedSettings);
      
//       setStatus('success');
//       setHasChanges(false);
//     } catch (error) {
//       console.error('Error saving notification settings:', error);
//       setStatus('error');
//     } finally {
//       setIsSaving(false);
//     }
//   };
  
//   if (status === 'success') {
//     return (
//       <StatusPage
//         type="success"
//         title="Settings Saved Successfully"
//         message="Your notification preferences have been updated and will take effect immediately."
//         actionText="Back to Settings"
//         onAction={() => setStatus('idle')}
//       />
//     );
//   }

//   if (status === 'error') {
//     return (
//       <StatusPage
//         type="error"
//         title="Failed to Save Settings"
//         message="We couldn't save your notification preferences. Please try again."
//         actionText="Try Again"
//         onAction={() => setStatus('idle')}
//       />
//     );
//   }

//   return (
//     <div className="flex">
//       <InstitutionSidebar
//         sidebarOpen={sidebarOpen}
//         toggleSidebar={toggleSidebar}
//         activeTab="settings"
//         onSignOut={handleSignOut}
//       />
//       <div className="flex-1 min-h-screen bg-gray-50 py-8">
//         <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="mb-6">
//           <button
//             onClick={() => navigate('/institution-dashboard')}
//             className="flex items-center text-blue-600 hover:text-blue-800"
//           >
//             <ChevronLeft className="h-5 w-5 mr-1" />
//             Back to Dashboard
//           </button>
//         </div>
        
//         <div className="bg-white rounded-lg shadow-md p-6">
//           <div className="flex justify-between items-center mb-6">
//             <div>
//               <h1 className="text-2xl font-bold text-gray-900">Notification Settings</h1>
//               <p className="text-gray-600 mt-1">
//                 Configure how and when you receive notifications about campaigns and approvals
//               </p>
//             </div>
            
//             <div>
//               <button
//                 onClick={saveSettings}
//                 disabled={!hasChanges || isSaving}
//                 className={`flex items-center px-4 py-2 rounded-md ${
//                   hasChanges && !isSaving
//                     ? 'bg-blue-600 text-white hover:bg-blue-700'
//                     : 'bg-gray-300 text-gray-500 cursor-not-allowed'
//                 }`}
//               >
//                 {isSaving ? (
//                   <>
//                     <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
//                     Saving...
//                   </>
//                 ) : (
//                   <>
//                     <Save className="h-4 w-4 mr-2" />
//                     Save Changes
//                   </>
//                 )}
//               </button>
//             </div>
//           </div>
          
//           {isLoading ? (
//             <div className="flex justify-center items-center py-12">
//               <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
//               <span className="ml-3 text-gray-600">Loading settings...</span>
//             </div>
//           ) : (
//             <div className="space-y-8">
//               {/* Notification Channels */}
//               <div>
//                 <h2 className="text-lg font-semibold mb-4 flex items-center">
//                   <Bell className="h-5 w-5 mr-2 text-blue-600" />
//                   Notification Channels
//                 </h2>
                
//                 <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//                   {channels.map(channel => (
//                     <div 
//                       key={channel.id}
//                       className="border rounded-lg p-4 hover:shadow-md transition-shadow"
//                     >
//                       <div className="flex items-start justify-between">
//                         <div className="flex items-start">
//                           <div className="mt-0.5">{channel.icon}</div>
//                           <div className="ml-3">
//                             <h3 className="font-medium">{channel.name}</h3>
//                             <p className="text-sm text-gray-500">{channel.description}</p>
//                           </div>
//                         </div>
                        
//                         <label className="relative inline-flex items-center cursor-pointer">
//                           <input
//                             type="checkbox"
//                             className="sr-only peer"
//                             checked={channel.enabled}
//                             onChange={() => toggleChannel(channel.id)}
//                           />
//                           <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
//                         </label>
//                       </div>
//                     </div>
//                   ))}
//                 </div>
//               </div>
              
//               {/* Notification Events */}
//               <div>
//                 <h2 className="text-lg font-semibold mb-4 flex items-center">
//                   <Clock className="h-5 w-5 mr-2 text-blue-600" />
//                   Notification Events
//                 </h2>
                
//                 <div className="overflow-x-auto">
//                   <table className="min-w-full divide-y divide-gray-200">
//                     <thead className="bg-gray-50">
//                       <tr>
//                         <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           Event
//                         </th>
//                         <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           Email
//                         </th>
//                         <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           SMS
//                         </th>
//                         <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
//                           In-App
//                         </th>
//                       </tr>
//                     </thead>
//                     <tbody className="bg-white divide-y divide-gray-200">
//                       {events.map(event => (
//                         <tr key={event.id} className="hover:bg-gray-50">
//                           <td className="px-6 py-4 whitespace-nowrap">
//                             <div>
//                               <div className="text-sm font-medium text-gray-900">{event.name}</div>
//                               <div className="text-sm text-gray-500">{event.description}</div>
//                             </div>
//                           </td>
//                           <td className="px-6 py-4 whitespace-nowrap text-center">
//                             <label className="relative inline-flex items-center cursor-pointer">
//                               <input
//                                 type="checkbox"
//                                 className="sr-only peer"
//                                 checked={event.channels.email}
//                                 onChange={() => toggleEventChannel(event.id, 'email')}
//                                 disabled={!channels.find(c => c.id === 'email')?.enabled}
//                               />
//                               <div className={`w-11 h-6 ${
//                                 channels.find(c => c.id === 'email')?.enabled 
//                                   ? 'bg-gray-200 peer-focus:ring-blue-300 peer-checked:bg-blue-600' 
//                                   : 'bg-gray-300 cursor-not-allowed'
//                               } peer-focus:outline-none peer-focus:ring-4 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}></div>
//                             </label>
//                           </td>
//                           <td className="px-6 py-4 whitespace-nowrap text-center">
//                             <label className="relative inline-flex items-center cursor-pointer">
//                               <input
//                                 type="checkbox"
//                                 className="sr-only peer"
//                                 checked={event.channels.sms}
//                                 onChange={() => toggleEventChannel(event.id, 'sms')}
//                                 disabled={!channels.find(c => c.id === 'sms')?.enabled}
//                               />
//                               <div className={`w-11 h-6 ${
//                                 channels.find(c => c.id === 'sms')?.enabled 
//                                   ? 'bg-gray-200 peer-focus:ring-blue-300 peer-checked:bg-blue-600' 
//                                   : 'bg-gray-300 cursor-not-allowed'
//                               } peer-focus:outline-none peer-focus:ring-4 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}></div>
//                             </label>
//                           </td>
//                           <td className="px-6 py-4 whitespace-nowrap text-center">
//                             <label className="relative inline-flex items-center cursor-pointer">
//                               <input
//                                 type="checkbox"
//                                 className="sr-only peer"
//                                 checked={event.channels.app}
//                                 onChange={() => toggleEventChannel(event.id, 'app')}
//                                 disabled={!channels.find(c => c.id === 'app')?.enabled}
//                               />
//                               <div className={`w-11 h-6 ${
//                                 channels.find(c => c.id === 'app')?.enabled 
//                                   ? 'bg-gray-200 peer-focus:ring-blue-300 peer-checked:bg-blue-600' 
//                                   : 'bg-gray-300 cursor-not-allowed'
//                               } peer-focus:outline-none peer-focus:ring-4 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}></div>
//                             </label>
//                           </td>
//                         </tr>
//                       ))}
//                     </tbody>
//                   </table>
//                 </div>
//               </div>
              
//               {/* Additional Settings */}
//               <div>
//                 <h2 className="text-lg font-semibold mb-4 flex items-center">
//                   <Users className="h-5 w-5 mr-2 text-blue-600" />
//                   Additional Settings
//                 </h2>
                
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                   <div className="border rounded-lg p-4">
//                     <div className="flex items-center justify-between">
//                       <div>
//                         <h3 className="font-medium">Daily Digest</h3>
//                         <p className="text-sm text-gray-500">Receive a daily summary of all activities</p>
//                       </div>
                      
//                       <label className="relative inline-flex items-center cursor-pointer">
//                         <input
//                           type="checkbox"
//                           className="sr-only peer"
//                           checked={settings.dailyDigest}
//                           onChange={() => toggleSetting('dailyDigest')}
//                         />
//                         <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
//                       </label>
//                     </div>
//                   </div>
                  
//                   <div className="border rounded-lg p-4">
//                     <div className="flex items-center justify-between">
//                       <div>
//                         <h3 className="font-medium">Weekly Report</h3>
//                         <p className="text-sm text-gray-500">Receive a weekly summary report</p>
//                       </div>
                      
//                       <label className="relative inline-flex items-center cursor-pointer">
//                         <input
//                           type="checkbox"
//                           className="sr-only peer"
//                           checked={settings.weeklyReport}
//                           onChange={() => toggleSetting('weeklyReport')}
//                         />
//                         <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
//                       </label>
//                     </div>
//                   </div>
//                 </div>
//               </div>
              
//               {/* Important Note */}
//               <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
//                 <div className="flex">
//                   <div className="flex-shrink-0">
//                     <Info className="h-5 w-5 text-blue-600" />
//                   </div>
//                   <div className="ml-3">
//                     <h3 className="text-sm font-medium text-blue-800">About Approval Notifications</h3>
//                     <div className="mt-2 text-sm text-blue-700">
//                       <p>
//                         Approval notifications are sent to designated approvers when a new campaign is submitted for review. 
//                         These notifications include details about the campaign and provide direct links to review and approve or reject the request.
//                       </p>
//                       <p className="mt-2">
//                         To customize email templates for approval notifications, please visit the{' '}
//                         <button 
//                           onClick={() => navigate('/email-templates')}
//                           className="font-medium underline hover:text-blue-800"
//                         >
//                           Email Templates
//                         </button>{' '}
//                         page.
//                       </p>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// }

// export default NotificationSettingsPage;
