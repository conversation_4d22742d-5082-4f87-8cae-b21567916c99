import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AlertTriangle, Check, ChevronLeft } from 'lucide-react';
import { toast } from 'react-toastify';
import StudentSidebar from '../components/StudentSidebar';
import StudentHeader from '../components/StudentHeader';
import StatusPage from '../components/StatusPage';
import externalStudentService from '../services/externalStudentService';
import { useStudentAuth } from '../context/StudentAuthContext';
import { StudentFooter } from '../components/StudentFooter';

export function StudentCampaignTerminationPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [campaignTitle, setCampaignTitle] = useState('');
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'reason' | 'confirm'>('reason');
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { logout } = useStudentAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [profile, setProfile] = useState<{ name?: string; studentRegId?: string }>({});
  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const handleSignOut = () => {
    logout('${import.meta.env.VITE_BASE_PATH}/student-login');
    toast.success('Successfully signed out');
  };

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const data = await externalStudentService.getStudentProfile();
        setProfile({ name: data.name, studentRegId: data.studentRegId });
      } catch (err) {
        console.error('Error loading profile:', err);
      }
    };
    loadProfile();
  }, []);

  useEffect(() => {
    if (!id) return;
    const loadCampaign = async () => {
      try {
        const data = await externalStudentService.getStudentCampaignDetails(id);
        setCampaignTitle(data.title);
      } catch (err) {
        console.error('Error loading campaign:', err);
      }
    };
    loadCampaign();
  }, [id]);

  const handleSubmit = async () => {
    if (!reason.trim()) {
      setErrorMessage('Please provide a reason for termination');
      setStatus('error');
      return;
    }

    if (step === 'reason') {
      setStep('confirm');
      return;
    }

    if (!id) return;
    try {
      setIsSubmitting(true);
      await externalStudentService.terminateStudentCampaign(id, reason);
      toast.success('Campaign terminated successfully');
      setStatus('success');
    } catch (error: any) {
      console.error('Error terminating campaign:', error);
      const apiMessage = error?.response?.data?.message;
      const message = apiMessage || error?.message || 'Failed to terminate campaign';
      setErrorMessage(message);
      setStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Campaign Terminated Successfully"
        message="Your campaign has been terminated and is no longer accepting donations."
        actionText="Back to Dashboard"
        backUrl="/students?tab=campaigns"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorMessage || ''}
        actionText="Retry"
        onAction={() => {
          setStatus('idle');
          setErrorMessage(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StudentHeader />
      <div className="flex pt-16 pb-16">
        <StudentSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="dashboard"
          onSignOut={handleSignOut}
          name={profile.name}
          studentRegId={profile.studentRegId}
        />
        <div className="flex-1 py-8 px-4 md:px-8 md:ml-64">
        <div className=" mx-auto bg-white rounded-lg shadow p-6">
          <div className="mb-4">
            <button
              onClick={() => navigate(id ? `/student/campaign/${id}` : '/students')}
              className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Campaign
            </button>
            <h1 className="text-2xl font-bold">
              {step === 'reason' ? 'Terminate Campaign' : 'Confirm Termination'}
            </h1>
          </div>

          {step === 'reason' ? (
            <>
              <div className="mb-6">
                <p className="text-gray-600 mb-2">You are about to terminate the campaign:</p>
                <p className="font-medium text-gray-800">{campaignTitle}</p>
              </div>

              <div className="mb-6">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for Termination <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="reason"
                  rows={4}
                  value={reason}
                  onChange={e => setReason(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please provide a detailed reason for terminating this campaign..."
                />
                <p className="mt-1 text-sm text-gray-500">
                  This reason will be communicated to the student and included in the audit log.
                </p>
              </div>
            </>
          ) : (
            <div className="mb-6">
              <div className="flex items-start mb-4">
                <AlertTriangle className="h-6 w-6 text-amber-500" />
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">Confirm Termination</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This action cannot be undone. The campaign will be permanently terminated and all stakeholders will be notified.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
                <h4 className="font-medium text-gray-800 mb-2">Termination Reason:</h4>
                <p className="text-gray-600">{reason}</p>
              </div>

              <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
                <h4 className="font-medium text-amber-800 mb-2">What happens next?</h4>
                <ul className="list-disc list-inside text-sm text-amber-700 space-y-1">
                  <li>The campaign status will be changed to "Terminated"</li>
                  <li>The student will be notified via email</li>
                  <li>No further donations will be accepted</li>
                  <li>The termination will be recorded in the audit log</li>
                </ul>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t mt-6">
            <button
              type="button"
              onClick={() => navigate(id ? `/student/campaign/${id}` : '/students')}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              className={`flex items-center px-4 py-2 rounded-md text-white ${
                step === 'reason' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : step === 'reason' ? (
                'Continue'
              ) : (
                <>
                  <Check className="h-4 w-4 mr-1" />
                  Confirm Termination
                </>
              )}
            </button>
          </div>
        </div>
        </div>
      </div>
      <StudentFooter />
    </div>
  );
}

export default StudentCampaignTerminationPage;