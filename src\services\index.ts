// Export all services from a single file for easier imports

export { default as api } from './api';
export { default as publicApi } from './publicApi';
export { default as authService } from './authService';
export { default as institutionService } from './institutionService';
export { default as studentService } from './studentService';
export { default as campaignService } from './campaignService';
export { default as donationService } from './donationService';
export { default as supporterService } from './supporterService'; // 👈 add this line
export { default as cognitoService } from './cognitoService';
export { default as activityService } from './activityService';
export { default as approvalService } from './approvalService';
export { default as paymentService } from './paymentService';
export { default as feedbackService } from './feedbackService';


// Export types from services
export type { Institution, Campaign, CampaignDetails, Student, ApprovalRequest, Activity, User } from './institutionService';
export type { StudentCreateRequest, StudentUpdateRequest } from './studentService';
export type { CampaignCreateRequest } from './campaignService';
export type { Donation } from './donationService';
export type { ApprovalRequestsResponse, GetApprovalRequestsParams } from './approvalService';
export type { PaginatedResponse, PaginationParams } from '../types';
export type { FeedbackMessage, PostFeedbackPayload } from './feedbackService';


