import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Menu, X, Home, User, LogOut } from 'lucide-react';
import getInitials from '../utils/getInitials';

export type StudentTab = 'dashboard' | 'profile';

interface StudentSidebarProps {
  sidebarOpen: boolean;
  toggleSidebar: () => void;
  activeTab?: StudentTab;
  onSignOut: () => void;
  name?: string;
  studentRegId?: string;
  onSelectTab?: (tab: StudentTab) => void;
}

export function StudentSidebar({
  sidebarOpen,
  toggleSidebar,
  activeTab = 'dashboard',
  onSignOut,
  name,
  studentRegId,
  onSelectTab,
}: StudentSidebarProps) {
  const navigate = useNavigate();

  const handleSelect = (tab: StudentTab) => {
    if (onSelectTab) {
      onSelectTab(tab);
    }
    navigate(`/students?tab=${tab}`);
  };

  const initials = getInitials(name);

  return (
    <>
      {/* Hamburger menu for mobile */}
      <button
        onClick={toggleSidebar}
        className="md:hidden fixed top-20 left-4 z-40 p-2 rounded-md bg-white shadow-md"
        aria-label="Toggle sidebar"
      >
        <Menu className="h-6 w-6 text-gray-600" />
      </button>

      {/* Mobile Sidebar */}
      <div
        className={`md:hidden fixed top-16 bottom-16 left-0 z-30 w-[85%] bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        <div className="flex flex-col h-full p-6">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <span className="text-lg font-bold text-blue-600">{initials}</span>
              </div>
              <div>
                <h2 className="font-semibold">{name || 'Student'}</h2>
                <p className="text-sm text-gray-600">{studentRegId || ''}</p>
              </div>
            </div>
            <button
              onClick={toggleSidebar}
              className="p-1 rounded-full bg-gray-100 hover:bg-gray-200"
              aria-label="Close sidebar"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>
          </div>

          <nav className="space-y-1 flex-1">
            {[
              { icon: Home, label: 'Dashboard', value: 'dashboard' },
              { icon: User, label: 'Profile', value: 'profile' },
              { icon: LogOut, label: 'Sign Out', value: 'signout' },
            ].map((item) => (
              <button
                key={item.value}
                onClick={() => {
                  if (item.value === 'signout') {
                    onSignOut();
                  } else {
                    handleSelect(item.value as StudentTab);
                    toggleSidebar();
                  }
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  item.value === 'signout'
                    ? 'text-red-600 hover:bg-red-50'
                    : activeTab === item.value
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50'
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop Sidebar */}
      <div className="hidden md:flex w-64 bg-white border-r border-gray-200 absolute left-0 top-16 bottom-0 flex-col overflow-y-auto z-10 shadow-md">
        <div className="flex-1 p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-lg font-bold text-blue-600">{initials}</span>
            </div>
            <div>
              <h2 className="font-semibold">{name || 'Student'}</h2>
              <p className="text-sm text-gray-600">{studentRegId || ''}</p>
            </div>
          </div>
          <nav className="space-y-1">
            {[
              { icon: Home, label: 'Dashboard', value: 'dashboard' },
              { icon: User, label: 'Profile', value: 'profile' },
              { icon: LogOut, label: 'Sign Out', value: 'signout' },
            ].map((item) => (
              <button
                key={item.value}
                onClick={() => {
                  if (item.value === 'signout') {
                    onSignOut();
                  } else {
                    handleSelect(item.value as StudentTab);
                  }
                }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  item.value === 'signout'
                    ? 'text-red-600 hover:bg-red-50'
                    : activeTab === item.value
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50'
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Mobile Navigation - Bottom Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10">
        <div className="flex justify-around p-2">
          {[
            { icon: Home, label: 'Dashboard', value: 'dashboard' },
            { icon: User, label: 'Profile', value: 'profile' },
          ].map((item) => (
            <button
              key={item.value}
              onClick={() => handleSelect(item.value as StudentTab)}
              className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                activeTab === item.value ? 'text-blue-600' : 'text-gray-600'
              }`}
            >
              <item.icon className="h-6 w-6" />
              <span className="text-xs mt-1">{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </>
  );
}

export default StudentSidebar;