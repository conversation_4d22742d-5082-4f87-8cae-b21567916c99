import { Navigate, useLocation } from "react-router-dom"; 
import { useAuth } from "react-oidc-context";
import { useEffect, useState } from "react";

const CognitoProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const auth = useAuth();
  const location = useLocation();
  const [authChecked, setAuthChecked] = useState(false);

  // ✅ Store last visited full URL when user navigates
  useEffect(() => {
    if (auth.isAuthenticated && !sessionStorage.getItem("authState")) {
      localStorage.setItem("lastVisitedPage", window.location.href);
      localStorage.setItem("authState", "authenticated-demo");
      sessionStorage.setItem("authState", "authenticated");
    }
  }, [auth.isAuthenticated]);
  


  // ✅ Restore last visited page on tab refresh or new tab open
useEffect(() => {
  const lastVisitedPage = localStorage.getItem("lastVisitedPage");
  const storedAuth = sessionStorage.getItem("authState");

  if (auth.isAuthenticated) {
    setAuthChecked(true);

    // ✅ Prevent redirecting when already on the correct page
    if (!storedAuth && lastVisitedPage && window.location.pathname !== new URL(lastVisitedPage).pathname) {
      sessionStorage.setItem("authState", "authenticated");

      // ✅ Only redirect if the user is on `/assessment/` (base page)
      if (window.location.pathname === "/assessment/") {
        // Use navigate instead of window.location.replace to prevent getting stuck
        setTimeout(() => {
          window.history.pushState({}, "", lastVisitedPage);
          window.location.reload();
        }, 0);
      }
      
    }

  } else if (!auth.isAuthenticated && storedAuth) {
    sessionStorage.removeItem("authState"); // ✅ Clear session if logged out
    setAuthChecked(false);
  }
}, [auth.isAuthenticated]);

  
  

  // ✅ Sync authentication state across tabs
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === "authState" && event.newValue !== event.oldValue) {
        window.location.replace(window.location.href); // ✅ Only replace, no full reload
      }
    };
  
    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);
  

  // ✅ Prevent infinite loading state
  useEffect(() => {
    if (!auth.isLoading) {
      setAuthChecked(true);
    }
  }, [auth.isLoading]);
  
  
  

  // ✅ Ensure authentication check completes before rendering
  if (auth.isLoading || !authChecked) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
        <p className="mt-4 text-lg text-gray-700">Authenticating... Please wait</p>
      </div>
    );
  }

  // ✅ Redirect only if authentication is not confirmed
  if (!auth.isAuthenticated) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  return children;
};

export default CognitoProtectedRoute;
