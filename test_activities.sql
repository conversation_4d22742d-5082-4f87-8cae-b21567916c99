-- Insert test activities for institution with ID 1
-- Make sure to replace the institution_id, entity_id, and user_id with actual values from your database

-- Campaign Created activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Campaign Created', 
    '<PERSON> created a new campaign for B.Sc Physics Tuition Fees', 
    NOW() - INTERVAL '5 days', 
    'John Student', 
    1, -- Replace with actual user ID
    1, -- Replace with actual campaign ID
    'CAMPAIGN', 
    1  -- Replace with actual institution ID
);

-- Campaign Approved activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Campaign Approved', 
    '<PERSON>''s campaign was approved', 
    NOW() - INTERVAL '3 days', 
    'Admin', 
    2, -- Replace with actual admin user ID
    1, -- Replace with actual campaign ID
    'CAMPAIGN', 
    1  -- Replace with actual institution ID
);

-- Donation Received activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Donation Received', 
    '<PERSON>''s campaign received a donation of ₹10,000', 
    NOW() - INTERVAL '2 days', 
    '<PERSON><PERSON><PERSON>', 
    3, -- Replace with actual donor user ID
    1, -- Replace with actual campaign ID
    'DONATION', 
    1  -- Replace with actual institution ID
);

-- Campaign Updated activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Campaign Updated', 
    'John Student updated campaign milestone', 
    NOW() - INTERVAL '1 day', 
    'John Student', 
    1, -- Replace with actual user ID
    1, -- Replace with actual campaign ID
    'MILESTONE', 
    1  -- Replace with actual institution ID
);

-- Milestone Completed activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Milestone completed', 
    'John Student''s milestone ''First Semester Fees'' is now completed', 
    NOW() - INTERVAL '12 hours', 
    'John Student', 
    1, -- Replace with actual user ID
    2, -- Replace with actual milestone ID
    'MILESTONE', 
    1  -- Replace with actual institution ID
);

-- Another Campaign Created activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Campaign Created', 
    'Aadam Khan created a new campaign for M.Tech Computer Science Conference', 
    NOW() - INTERVAL '4 days', 
    'Aadam Khan', 
    3, -- Replace with actual user ID
    2, -- Replace with actual campaign ID
    'CAMPAIGN', 
    1  -- Replace with actual institution ID
);

-- Another Donation Received activity
INSERT INTO activities (
    action, 
    description, 
    timestamp, 
    user_name, 
    user_id, 
    entity_id, 
    entity_type, 
    institution_id
) VALUES (
    'Donation Received', 
    'Aadam Khan''s campaign received a donation of ₹5,000', 
    NOW() - INTERVAL '1 day', 
    'Anonymous', 
    NULL, -- Anonymous donor
    2, -- Replace with actual campaign ID
    'DONATION', 
    1  -- Replace with actual institution ID
);
