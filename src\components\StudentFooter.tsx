interface FooterProps {
  className?: string;
}

export function StudentFooter({ className = '' }: FooterProps) {
  return (
    <footer className={`bg-white border-t absolute bottom-0 left-0 right-0 z-10 ${className}`.trim()}>
      <div className="py-4 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          <div className="text-gray-500 text-sm">
            © {new Date().getFullYear()}{' '}
            <a
              href="https://www.icamxperts.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              ICAMXPERTS
            </a>. All rights reserved.
          </div>
          <div className="flex items-center text-gray-500 text-sm">
            Student Module
          </div>
        </div>
      </div>
    </footer>
  );
}

export default StudentFooter;