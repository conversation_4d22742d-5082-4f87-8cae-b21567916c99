import React, { useState, useEffect } from 'react';
import { GoogleLogin as ReactGoogleLogin } from '@react-oauth/google';
import { FcGoogle } from 'react-icons/fc';

interface DevGoogleLoginProps {
  buttonText?: string;
  onSuccess: (response: any) => void;
  onError: () => void;
  customClass?: string;
  iconClass?: string;
  textClass?: string;
}

export function DevGoogleLogin({
  buttonText = "Continue with Google",
  onSuccess,
  onError,
  customClass,
  iconClass,
  textClass
}: DevGoogleLoginProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Check if we're in development mode
  const isDevelopment = import.meta.env.DEV;
  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  useEffect(() => {
    // Log configuration for debugging
    console.log('Google OAuth Configuration:', {
      isDevelopment,
      clientId: clientId ? `${clientId.substring(0, 20)}...` : 'Not set',
      origin: window.location.origin
    });
  }, []);

  // Enhanced error handler with detailed logging
  const handleError = (error: any) => {
    console.error('Google login error details:', error);
    setIsLoading(false);
    
    // Determine error type and show appropriate message
    if (error?.message?.includes('origin') || error?.message?.includes('client_id')) {
      setErrorMessage('OAuth configuration issue. Please check Google Cloud Console settings.');
      setShowFallback(true);
    } else if (error?.message?.includes('FedCM') || error?.message?.includes('NetworkError')) {
      setErrorMessage('Browser security restrictions detected.');
      setShowFallback(true);
    } else {
      setErrorMessage('Google login failed. Please try again.');
      onError();
    }
  };

  // Enhanced success handler
  const handleSuccess = (response: any) => {
    setIsLoading(false);
    setErrorMessage('');
    onSuccess(response);
  };

  // Mock login for development (when OAuth is not configured)
  const handleMockLogin = () => {
    if (!isDevelopment) return;
    
    console.log('Using mock Google login for development');
    
    // Create a mock credential response
    const mockCredential = btoa(JSON.stringify({
      iss: 'https://accounts.google.com',
      aud: clientId,
      sub: '*********',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://via.placeholder.com/150',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000)
    }));

    const mockResponse = {
      credential: `header.${mockCredential}.signature`,
      select_by: 'btn'
    };

    onSuccess(mockResponse);
  };

  // Use custom classes if provided, otherwise use default classes
  const buttonClassName = customClass || "w-full flex items-center justify-center gap-2 bg-white text-gray-700 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors";
  const googleIconClassName = iconClass || "w-5 h-5";
  const textClassName = textClass || "";

  // Show fallback options if there are configuration issues
  if (showFallback) {
    return (
      <div className="space-y-3">
        {errorMessage && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">{errorMessage}</p>
          </div>
        )}
        
        {isDevelopment && (
          <button
            onClick={handleMockLogin}
            disabled={isLoading}
            className={`${buttonClassName} bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100`}
          >
            <FcGoogle className={googleIconClassName} />
            <span className={textClassName}>
              {isLoading ? 'Signing in...' : 'Mock Google Login (Dev)'}
            </span>
          </button>
        )}
        
        <div className="text-center">
          <button
            onClick={() => setShowFallback(false)}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
          >
            Try Google login again
          </button>
        </div>
        
        {isDevelopment && (
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-xs text-gray-600">
              <strong>Development Mode:</strong> Add these URLs to your Google OAuth client:
            </p>
            <ul className="text-xs text-gray-600 mt-1 space-y-1">
              <li>• {window.location.origin}</li>
              <li>• {window.location.origin}/auth/google/callback</li>
            </ul>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <ReactGoogleLogin
        onSuccess={handleSuccess}
        onError={handleError}
        useOneTap={false}
        theme="filled_blue"
        text="continue_with"
        shape="rectangular"
        width="100%"
        type="standard"
        context="signin"
        auto_select={false}
        cancel_on_tap_outside={false}
        itp_support={true}
      />
      
     
    </div>
  );
}
