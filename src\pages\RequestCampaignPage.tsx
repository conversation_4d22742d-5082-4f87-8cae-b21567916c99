import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronLeft, Save } from "lucide-react";
import { toast } from "react-toastify";
import externalStudentService from "../services/externalStudentService";
import StatusPage from '../components/StatusPage';
import StudentSidebar from '../components/StudentSidebar';
import { useStudentAuth } from '../context/StudentAuthContext';

interface CampaignFormData {
  title: string;
  description: string;
  targetAmount: string;
  duration: string;
  startDate: string;
  endDate: string;
  documents: File[];
}

export function RequestCampaignPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<CampaignFormData>({
    title: "",
    description: "",
    targetAmount: "",
    duration: "",
    startDate: "",
    endDate: "",
    documents: [],
  });
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
  const { logout } = useStudentAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [profile, setProfile] = useState<{ name?: string; studentRegId?: string }>({});

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const handleSignOut = () => {
    logout('${import.meta.env.VITE_BASE_PATH}/student-login');
    toast.success('Successfully signed out');
  };

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const data = await externalStudentService.getStudentProfile();
        setProfile({ name: data.name, studentRegId: data.studentRegId });
      } catch (err) {
        console.error('Error loading profile:', err);
      }
    };
    loadProfile();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: keyof CampaignFormData
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  const formatWithCommas = (value: number | string) => {
    if (!value) return '';
    return Number(value).toLocaleString('en-IN');
  };

  const handleFormattedChange = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const rawValue = e.target.value.replace(/,/g, '');
    const numericValue = Number(rawValue);
    if (isNaN(numericValue) || numericValue < 0) return;
    setFormData({ ...formData, [field]: numericValue.toString() });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await externalStudentService.createStudentCampaign({
        title: formData.title,
        description: formData.description,
        goalAmount: Number(formData.targetAmount),
        startDate: formData.startDate,
        endDate: formData.endDate,
        validDateRange: true,
      });
      setStatus('success');
    } catch (error: any) {
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    }
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Campaign Request Submitted"
        message="Your campaign request has been submitted successfully and is now pending approval from your institution."
        actionText="Back to Dashboard"
        backUrl="/students"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="flex">
      <StudentSidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        activeTab="dashboard"
        onSignOut={handleSignOut}
        name={profile.name}
        studentRegId={profile.studentRegId}
      />
      <div className="flex-1 min-h-screen bg-gray-50 py-8 px-4 md:px-8 md:ml-64 flex flex-col">
        <div className="flex-grow">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <button
              onClick={() => navigate("/students")}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Dashboard
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold mb-6">Request New Campaign</h1>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign Title
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleChange(e, "title")}
                  className="w-full border rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleChange(e, "description")}
                  className="w-full border rounded-lg px-3 py-2 h-32"
                  required
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Amount (₹)
                  </label>
                  <input
                    type="text"
                    inputMode="numeric"
                    value={formatWithCommas(formData.targetAmount)}
                    onChange={(e) => handleFormattedChange(e, 'targetAmount')}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleChange(e, "startDate")}
                    className="w-full border rounded-lg px-3 py-2"
                    min={new Date().toISOString().split('T')[0]}
                    max={formData.endDate || undefined}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleChange(e, "endDate")}
                    className="w-full border rounded-lg px-3 py-2"
                    min={formData.startDate || undefined}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Supporting Documents
                </label>
                <div className="border-dashed border-2 border-gray-300 rounded-lg p-4 text-center">
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    id="document-upload"
                    onChange={(e) => {
                      if (e.target.files) {
                        setFormData((prev) => ({
                          ...prev,
                          documents: Array.from(e.target.files || []),
                        }));
                      }
                    }}
                  />
                  <label
                    htmlFor="document-upload"
                    className="cursor-pointer text-blue-600 hover:text-blue-800"
                  >
                    Click to upload documents
                  </label>
                  <p className="text-sm text-gray-500 mt-1">
                    (Fee receipts, income certificates, etc.)
                  </p>
                </div>
                {formData.documents.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">
                      {formData.documents.length} file(s) selected
                    </p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
                  onClick={() => navigate("/students")}
                  className="px-4 py-2 border rounded-lg text-gray-600 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Submit Request
                </button>
              </div>
            </form>
          </div>
        </div>
        </div>
       
      </div>
    </div>
  );
}

export default RequestCampaignPage;
