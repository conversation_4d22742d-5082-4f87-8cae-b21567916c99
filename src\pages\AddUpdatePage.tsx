import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Calendar, ChevronLeft, Save } from 'lucide-react';
import { toast } from 'react-toastify';
import StatusPage from '../components/StatusPage';
import StudentSidebar from '../components/StudentSidebar';
import { StudentFooter } from '../components/StudentFooter';
import { useStudentAuth } from '../context/StudentAuthContext';
import externalStudentService from '../services/externalStudentService';

interface UpdateFormData {
  title: string;
  content: string;
  type: 'achievement' | 'project' | 'academic';
  date: string;
}

export function AddUpdatePage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<UpdateFormData>({
    title: '',
    content: '',
    type: 'achievement',
    date: new Date().toISOString().split('T')[0],
  });
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const { logout } = useStudentAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [profile, setProfile] = useState<{ name?: string; studentRegId?: string }>({});
  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const handleSignOut = () => {
    logout('${import.meta.env.VITE_BASE_PATH}/student-login');
    toast.success('Successfully signed out');
  };

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const data = await externalStudentService.getStudentProfile();
        setProfile({ name: data.name, studentRegId: data.studentRegId });
      } catch (err) {
        console.error('Error loading profile:', err);
      }
    };
    loadProfile();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('Submitting update:', formData);
      setStatus('success');
    } catch (error) {
      setStatus('error');
    }
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Update Added Successfully"
        message={`Your ${formData.type} update "${formData.title}" has been added to your profile.`}
        actionText="Back to Dashboard"
        backUrl="/students"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Failed to Add Update"
        message="We couldn't add your update. Please check all fields and try again."
        actionText="Try Again"
        onAction={() => setStatus('idle')}
      />
    );
  }

  return (
    <div className="flex">
      <StudentSidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        activeTab="dashboard"
        onSignOut={handleSignOut}
        name={profile.name}
        studentRegId={profile.studentRegId}
      />
      <div className="flex-1 min-h-screen bg-gray-50 py-8 px-4 md:px-8 md:ml-64 flex flex-col">
        <div className="flex-grow">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <button
              onClick={() => navigate('/students')}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Dashboard
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold mb-6">Add New Update</h1>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter update title"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                  Update Type
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="achievement">Achievement</option>
                  <option value="project">Project</option>
                  <option value="academic">Academic</option>
                </select>
              </div>

              <div>
                <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    value={formData.date}
                    onChange={handleChange}
                    min={new Date().toISOString().split('T')[0]}
                    required
                    className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                  Content
                </label>
                <textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleChange}
                  required
                  rows={5}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your update..."
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => navigate('/students')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-3 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Update
                </button>
              </div>
            </form>
          </div>
        </div>
        </div>
        <StudentFooter />
      </div>
    </div>
  );
}

export default AddUpdatePage;
