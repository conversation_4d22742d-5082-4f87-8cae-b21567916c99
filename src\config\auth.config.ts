import { AuthProviderProps } from "react-oidc-context";
import { WebStorageStateStore } from "oidc-client-ts";
import { COGNITO_CLIENT_ID, COGNITO_REDIRECT_URI, COGNITO_DOMAIN } from './env';

// Client secret for Cognito authentication
const COGNITO_CLIENT_SECRET = 'm3f9iqp19o98j79bvlgkecsd77q3bpghqsqarrdirqb4dj1mjo';

export const oidcConfig: AuthProviderProps = {
  // Use the Cognito domain directly without discovery
  authority: COGNITO_DOMAIN, // Already includes https://
  client_id: COGNITO_CLIENT_ID,
  client_secret: COGNITO_CLIENT_SECRET, // Required for Cognito token exchange
  redirect_uri: COGNITO_REDIRECT_URI,
  post_logout_redirect_uri: `${window.location.origin}${import.meta.env.VITE_BASE_PATH}/`,
  response_type: 'code',
  scope: "openid profile email",

  // Cognito-specific settings
  client_authentication: 'client_secret_post', // Cognito uses POST method for client auth

  // Manually specify endpoints since Cognito doesn't support discovery
  metadata: {
    issuer: COGNITO_DOMAIN,
    authorization_endpoint: `${COGNITO_DOMAIN}/oauth2/authorize`,
    token_endpoint: `${COGNITO_DOMAIN}/oauth2/token`,
    userinfo_endpoint: `${COGNITO_DOMAIN}/oauth2/userInfo`,
    end_session_endpoint: `${COGNITO_DOMAIN}/logout`,
    jwks_uri: `${COGNITO_DOMAIN}/.well-known/jwks.json`,
    token_endpoint_auth_methods_supported: ['client_secret_post', 'client_secret_basic'],
  },

  // Fix state management issues - use a more robust state store
  stateStore: new WebStorageStateStore({ 
    store: window.sessionStorage,
    prefix: 'oidc.state'
  }),

  // Enable refresh token functionality
  loadUserInfo: true, // Enable to get user info
  automaticSilentRenew: true, // Enable automatic token refresh
  monitorSession: false, // Disable session monitoring to avoid conflicts
  includeIdTokenInSilentRenew: true, // Include ID token in silent renewal

  // Silent renew settings
  silent_redirect_uri: COGNITO_REDIRECT_URI, // Use same redirect URI for silent renewal

  // Enhanced callback handling
  onSigninCallback: (user) => {
    console.log('OIDC signin callback triggered:', user);
    // Clean up the URL after successful signin
    window.history.replaceState({}, document.title, window.location.pathname);
  },

  // Add error handling
  onSigninError: (error) => {
    console.error('OIDC signin error:', error);
    
    // Handle state mismatch specifically
    if (error.message?.includes('No matching state found')) {
      console.log('State mismatch detected, clearing all OIDC state');
      clearAllOidcState();
      // Redirect to login with a message
      window.location.href = '${import.meta.env.VITE_BASE_PATH}/institution-login?error=state_mismatch';
      return;
    }
    
    // Clear any corrupted state for other errors
    clearAllOidcState();
  },

  // Add silent renew error handling
  onSigninSilentError: (error) => {
    console.error('OIDC silent signin error:', error);
  },

  // Add signout callback handling
  onSignoutCallback: () => {
    console.log('OIDC signout callback triggered');
    // Clean up any remaining state
    Object.keys(window.sessionStorage).forEach(key => {
      if (key.startsWith('oidc.')) {
        window.sessionStorage.removeItem(key);
      }
    });
    // Redirect to home page
    window.location.href = '${import.meta.env.VITE_BASE_PATH}/';
  },

  // Add signout error handling
  onSignoutError: (error) => {
    console.error('OIDC signout error:', error);
    // Even if signout fails, clear local state and redirect
    clearAllOidcState();
    window.location.href = '${import.meta.env.VITE_BASE_PATH}/';
  }
};

// Helper function to clear all OIDC state
function clearAllOidcState() {
  // Clear from both sessionStorage and localStorage
  [window.sessionStorage, window.localStorage].forEach(storage => {
    Object.keys(storage).forEach(key => {
      if (key.startsWith('oidc.')) {
        storage.removeItem(key);
      }
    });
  });
}


