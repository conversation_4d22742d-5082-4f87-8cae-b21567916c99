import React, { useState, useEffect } from 'react';
import { useStudentAuth } from '../context/StudentAuthContext';
import externalStudentService from '../services/externalStudentService';
import logoService from '../services/logoService';
import getInitials from '../utils/getInitials';

export function StudentHeader() {
  const { user } = useStudentAuth();
  const [profile, setProfile] = useState<{ name?: string }>({});
  const [logoUrl, setLogoUrl] = useState<string>('');
  
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load profile data
        const profileData = await externalStudentService.getStudentProfile();
        setProfile({ name: profileData.name });
        
        // Load logo
        const logo = await logoService.getLogo();
        setLogoUrl(logo);
      } catch (err) {
        console.error('Error loading data:', err);
      }
    };
    loadData();
  }, []);

  const displayName = profile.name || user?.name;
  const initials = getInitials(displayName);

  return (
    <nav className="bg-white shadow-sm absolute top-0 left-0 right-0 z-20">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center space-x-3">
  <img src={logoUrl} alt="Institution Logo" className="h-10 w-auto" />
  <h1 className="text-xl font-bold text-blue-600">EduFund</h1>
</div>

          {user && (
            <div className="flex items-center space-x-3">
              <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold">
                {initials}
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">{displayName}</div>
                <div className="text-xs text-gray-500">{user.email}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

export default StudentHeader;