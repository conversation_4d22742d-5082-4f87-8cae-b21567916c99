import { Home, Settings, FileText, UserPlus, FileCheck2, <PERSON>Circle, UserCog } from 'lucide-react';
import { BreadcrumbItem } from '../components/Breadcrumb';
import React from 'react';

// Base paths
export const PATHS = {
  HOME: '/',
  INSTITUTION_DASHBOARD: '/institution-dashboard',
  CAMPAIGNS: '/institution-dashboard?tab=campaigns',
  APPROVAL_REQUESTS: '/institution-dashboard?tab=approvalRequests',
  APPROVAL_REQUEST_DETAILS: '/approval-requests',
  SETTINGS: '/institution-dashboard?tab=settings',
  CREATE_CAMPAIGN: '/institution/create-campaign',
  ADD_USER: '/institution/add-user',
  ASSIGN_ROLE: '/institution/assign-role',
  STUDENT_DASHBOARD: '/students?tab=dashboard',
  STUDENT_PROFILE: '/students?tab=profile',
  SUPPORTER_DASHBOARD: '/supporters',
  SUPPORTER_BROWSE: '/supporters?tab=browse',
  SUPPORTER_DONATIONS: '/supporters?tab=donations',
  DONATION_DETAILS: '/donation-details',
  ACTIVITIES: '/activities',
  ACTIVITY_DETAILS: '/activities',
};

// Common breadcrumb items
export const HOME_CRUMB: BreadcrumbItem = {
  label: 'Home',
  path: PATHS.HOME,
  icon: React.createElement(Home, { className: 'h-4 w-4' }),
};

export const DASHBOARD_CRUMB: BreadcrumbItem = {
  label: 'Dashboard',
  path: PATHS.INSTITUTION_DASHBOARD,
  icon: React.createElement(Home, { className: 'h-4 w-4' }),
};

export const CAMPAIGNS_CRUMB: BreadcrumbItem = {
  label: 'Campaigns',
  path: PATHS.CAMPAIGNS,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

export const APPROVAL_REQUESTS_CRUMB: BreadcrumbItem = {
  label: 'Approval Requests',
  path: PATHS.APPROVAL_REQUESTS,
  icon: React.createElement(CheckCircle, { className: 'h-4 w-4' }),
};

export const SETTINGS_CRUMB: BreadcrumbItem = {
  label: 'Settings',
  path: PATHS.SETTINGS,
  icon: React.createElement(Settings, { className: 'h-4 w-4' }),
};

export const STUDENT_DASHBOARD_CRUMB: BreadcrumbItem = {
  label: 'Student Dashboard',
  path: PATHS.STUDENT_DASHBOARD,
  icon: React.createElement(Home, { className: 'h-4 w-4' }),
};

export const STUDENT_PROFILE_CRUMB: BreadcrumbItem = {
  label: 'Profile',
  path: PATHS.STUDENT_PROFILE,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

export const SUPPORTER_DASHBOARD_CRUMB: BreadcrumbItem = {
  label: 'Dashboard',
  path: PATHS.SUPPORTER_DASHBOARD,
  icon: React.createElement(Home, { className: 'h-4 w-4' }),
};

export const SUPPORTER_BROWSE_CRUMB: BreadcrumbItem = {
  label: 'Browse Students',
  path: PATHS.SUPPORTER_BROWSE,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

export const SUPPORTER_DONATIONS_CRUMB: BreadcrumbItem = {
  label: 'My Donations',
  path: PATHS.SUPPORTER_DONATIONS,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

export const DONATION_DETAILS_CRUMB: BreadcrumbItem = {
  label: 'Donation Details',
  path: PATHS.DONATION_DETAILS,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

export const ACTIVITIES_CRUMB: BreadcrumbItem = {
  label: 'Activities',
  path: PATHS.ACTIVITIES,
  icon: React.createElement(FileText, { className: 'h-4 w-4' }),
};

// Helper function to get breadcrumb items for a specific page
export const getBreadcrumbItems = (
  page: string,
  additionalInfo?: {
    campaignTitle?: string;
    campaignId?: string | number;
    settingsTab?: string;
    approvalId?: string | number;
    approvalTitle?: string;
    donationId?: string | number;
    studentName?: string;
    amount?: string | number;
    source?: string;
  }
): BreadcrumbItem[] => {
  switch (page) {
    case 'dashboard':
      return [HOME_CRUMB, DASHBOARD_CRUMB];

    case 'campaigns':
      return [HOME_CRUMB, DASHBOARD_CRUMB, CAMPAIGNS_CRUMB];

    case 'approvalRequests':
      return [HOME_CRUMB, DASHBOARD_CRUMB, APPROVAL_REQUESTS_CRUMB];

    case 'approvalRequestDetails':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        APPROVAL_REQUESTS_CRUMB,
        {
          label: additionalInfo?.approvalTitle || 'Approval Request Details',
          path: additionalInfo?.approvalId ? `${PATHS.APPROVAL_REQUEST_DETAILS}/${additionalInfo.approvalId}` : undefined
        }
      ];

    case 'settings':
      const items = [HOME_CRUMB, DASHBOARD_CRUMB, SETTINGS_CRUMB];
      if (additionalInfo?.settingsTab) {
        items.push({
          label: additionalInfo.settingsTab.charAt(0).toUpperCase() + additionalInfo.settingsTab.slice(1),
        });
      }
      return items;

    case 'create-campaign':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        CAMPAIGNS_CRUMB,
        { label: 'Create Campaign', icon: React.createElement(FileCheck2, { className: 'h-4 w-4' }) }
      ];

    case 'campaign-details':
      const source = additionalInfo?.source;
      if (source === 'approvals') {
        return [
          HOME_CRUMB,
          DASHBOARD_CRUMB,
          APPROVAL_REQUESTS_CRUMB,
          {
            label: additionalInfo?.campaignTitle || 'Campaign Details',
            path: additionalInfo?.campaignId ? `/institution/campaign/${additionalInfo.campaignId}` : undefined
          }
        ];
      } else {
        return [
          HOME_CRUMB,
          DASHBOARD_CRUMB,
          CAMPAIGNS_CRUMB,
          {
            label: additionalInfo?.campaignTitle || 'Campaign Details',
            path: additionalInfo?.campaignId ? `/institution/campaign/${additionalInfo.campaignId}` : undefined
          }
        ];
      }

    case 'add-user':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        SETTINGS_CRUMB,
        { label: 'Add User', icon: React.createElement(UserPlus, { className: 'h-4 w-4' }) }
      ];
      
    case 'assign-role':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        SETTINGS_CRUMB,
        { label: 'Assign Role', icon: React.createElement(UserCog, { className: 'h-4 w-4' }) }
      ];
      
    case 'revoke-role':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        SETTINGS_CRUMB,
        { label: 'Revoke Role', icon: React.createElement(UserCog, { className: 'h-4 w-4' }) }
      ];
      
    case 'delete-user':
      return [
        HOME_CRUMB,
        DASHBOARD_CRUMB,
        SETTINGS_CRUMB,
        { label: 'Delete User', icon: React.createElement(UserCog, { className: 'h-4 w-4' }) }
      ];

    case 'student-dashboard':
      return [HOME_CRUMB, STUDENT_DASHBOARD_CRUMB];

    case 'student-profile':
      return [HOME_CRUMB, STUDENT_DASHBOARD_CRUMB, STUDENT_PROFILE_CRUMB];

    case 'student-campaign-details':
      return [
        HOME_CRUMB,
        STUDENT_DASHBOARD_CRUMB,
        {
          label: additionalInfo?.campaignTitle || 'Campaign Details',
          path: additionalInfo?.campaignId ? `/student/campaign/${additionalInfo.campaignId}` : undefined
        }
      ];

    case 'supporter-dashboard':
      return [HOME_CRUMB, SUPPORTER_DASHBOARD_CRUMB];

    case 'supporter-browse':
      return [HOME_CRUMB, SUPPORTER_DASHBOARD_CRUMB, SUPPORTER_BROWSE_CRUMB];

    case 'supporter-donations':
      return [HOME_CRUMB, SUPPORTER_DASHBOARD_CRUMB, SUPPORTER_DONATIONS_CRUMB];

    case 'supporter-campaign-details':
      return [
        HOME_CRUMB,
        SUPPORTER_DASHBOARD_CRUMB,
        SUPPORTER_BROWSE_CRUMB,
        {
          label: additionalInfo?.campaignTitle || 'Campaign Details',
          path: additionalInfo?.campaignId ? `/campaign/${additionalInfo.campaignId}` : undefined
        }
      ];

    case 'donation-details':
      return [
        HOME_CRUMB,
        SUPPORTER_DASHBOARD_CRUMB,
        SUPPORTER_DONATIONS_CRUMB,
        {
          label: `Donation to ${additionalInfo?.studentName || 'Student'}`,
          path: additionalInfo?.donationId ? `/donation-details/${additionalInfo.donationId}` : undefined
        }
      ];

    case 'activities':
      return [HOME_CRUMB, ACTIVITIES_CRUMB];

    case 'activity-details':
      return [HOME_CRUMB, DASHBOARD_CRUMB, { label: 'Activity Details' }];

    default:
      return [HOME_CRUMB];
  }
};
