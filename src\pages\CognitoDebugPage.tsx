import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { COGNITO_DOMAIN, COGNITO_CLIENT_ID, COGNITO_REDIRECT_URI, COGNITO_SIGNOUT_URI, COGNITO_RESPONSE_TYPE, COGNITO_SCOPE } from '../config/env';
import cognitoService from '../services/cognitoService';
import { useAuth } from '../context/AuthContext';

/**
 * CognitoDebugPage is a diagnostic page to verify and debug Cognito configuration
 * It displays the current Cognito configuration and provides buttons to test login/logout
 */
export function CognitoDebugPage() {
  const navigate = useNavigate();
  const { loginWithCognito } = useAuth();

  // Direct environment variables
  const [envVars, setEnvVars] = useState({
    VITE_COGNITO_DOMAIN: import.meta.env.VITE_COGNITO_DOMAIN || 'Not set',
    VITE_COGNITO_CLIENT_ID: import.meta.env.VITE_COGNITO_CLIENT_ID || 'Not set',
    VITE_COGNITO_REDIRECT_URI: import.meta.env.VITE_COGNITO_REDIRECT_URI || 'Not set',
    VITE_COGNITO_SIGNOUT_URI: import.meta.env.VITE_COGNITO_SIGNOUT_URI || 'Not set',
    VITE_COGNITO_RESPONSE_TYPE: import.meta.env.VITE_COGNITO_RESPONSE_TYPE || 'Not set',
    VITE_COGNITO_SCOPE: import.meta.env.VITE_COGNITO_SCOPE || 'Not set',
  });

  // Config variables from env.ts
  const [configVars, setConfigVars] = useState({
    COGNITO_DOMAIN,
    COGNITO_CLIENT_ID,
    COGNITO_REDIRECT_URI,
    COGNITO_SIGNOUT_URI,
    COGNITO_RESPONSE_TYPE,
    COGNITO_SCOPE
  });

  // Generated URLs
  const [generatedUrls, setGeneratedUrls] = useState({
    authUrl: '',
    logoutUrl: ''
  });

  useEffect(() => {
    // Generate the URLs when the component mounts
    try {
      const authUrl = cognitoService.buildAuthorizationUrl();
      const logoutUrl = cognitoService.buildLogoutUrl();
      setGeneratedUrls({ authUrl, logoutUrl });
    } catch (error) {
      console.error('Error generating URLs:', error);
    }
  }, []);

  const handleTestLogin = () => {
    loginWithCognito(false);
  };

  const handleTestLogout = () => {
    cognitoService.redirectToLogout();
  };

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Cognito Configuration Debug</h1>
          <button 
            onClick={handleBack}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
          >
            Back
          </button>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Environment Variables (from .env)</h2>
          <div className="bg-gray-100 p-4 rounded overflow-auto">
            <pre>{JSON.stringify(envVars, null, 2)}</pre>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Config Variables (from env.ts)</h2>
          <div className="bg-gray-100 p-4 rounded overflow-auto">
            <pre>{JSON.stringify(configVars, null, 2)}</pre>
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Generated URLs</h2>
          <div className="bg-gray-100 p-4 rounded overflow-auto">
            <h3 className="font-medium mb-2">Authorization URL:</h3>
            <div className="mb-4 break-all">{generatedUrls.authUrl || 'Error generating URL'}</div>
            
            <h3 className="font-medium mb-2">Logout URL:</h3>
            <div className="break-all">{generatedUrls.logoutUrl || 'Error generating URL'}</div>
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={handleTestLogin}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Login
          </button>

          <button
            onClick={handleTestLogout}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Test Logout
          </button>
        </div>
      </div>
    </div>
  );
}

export default CognitoDebugPage;
