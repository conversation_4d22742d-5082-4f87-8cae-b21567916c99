import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Menu, X, BarChart3, FileText, Settings, LogOut } from 'lucide-react';

export type InstitutionTab = 'dashboard' | 'approvalRequests' | 'campaigns' | 'settings' | 'reports';

interface InstitutionSidebarProps {
  sidebarOpen: boolean;
  toggleSidebar: () => void;
  activeTab?: InstitutionTab;
  onSignOut: () => void;
}

export function InstitutionSidebar({ sidebarOpen, toggleSidebar, activeTab = 'dashboard', onSignOut }: InstitutionSidebarProps) {
  const navigate = useNavigate();

  const updateTab = (tab: InstitutionTab) => {
    navigate(`/institution-dashboard?tab=${tab}`);
  };

  return (
    <>
      {/* Hamburger menu for mobile */}
      <button
        onClick={toggleSidebar}
        className="md:hidden fixed top-4 left-4 z-40 p-2 rounded-md bg-white shadow-md"
        aria-label="Toggle sidebar"
      >
        <Menu className="h-6 w-6 text-gray-600" />
      </button>

      {/* Side Navigation with conditional classes */}
      <div
        className={`${sidebarOpen ? 'w-full md:w-64' : 'w-0 -ml-64'} bg-white border-r border-gray-200 p-4 shadow-sm transition-all duration-300 z-30 ${sidebarOpen ? 'fixed md:relative h-[100dvh] overflow-y-auto' : 'hidden md:block md:w-0 md:-ml-64 md:h-screen'}`}
      >
        {/* Close button for mobile */}
        <button
          onClick={toggleSidebar}
          className="md:hidden absolute top-4 right-4 p-1 rounded-full bg-gray-100 hover:bg-gray-200"
          aria-label="Close sidebar"
        >
          <X className="h-5 w-5 text-gray-600" />
        </button>
        <div className="space-y-2 mt-16 md:mt-0">
          <button
            onClick={() => updateTab('dashboard')}
            className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg ${activeTab === 'dashboard' ? 'bg-blue-900 text-white' : 'hover:bg-blue-400'}`}
          >
            <BarChart3 className="h-5 w-5" />
            <span>Dashboard</span>
          </button>
          <button
            onClick={() => updateTab('approvalRequests')}
            className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg ${activeTab === 'approvalRequests' ? 'bg-blue-900 text-white' : 'hover:bg-blue-400'}`}
          >
            <FileText className="h-5 w-5" />
            <span>Approval Requests</span>
          </button>
          <button
            onClick={() => updateTab('campaigns')}
            className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg ${activeTab === 'campaigns' ? 'bg-blue-900 text-white' : 'hover:bg-blue-400'}`}
          >
            <FileText className="h-5 w-5" />
            <span>Campaigns</span>
          </button>
          <button
            onClick={() => updateTab('settings')}
            className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg ${activeTab === 'settings' ? 'bg-blue-900 text-white' : 'hover:bg-blue-400'}`}
          >
            <Settings className="h-5 w-5" />
            <span>Settings</span>
          </button>
          <button
            onClick={() => updateTab('reports')}
            className={`w-full flex items-center space-x-2 px-4 py-2 rounded-lg ${activeTab === 'reports' ? 'bg-blue-900 text-white' : 'hover:bg-blue-400'}`}
          >
            <BarChart3 className="h-5 w-5" />
            <span>Reports</span>
          </button>

          {/* Sign Out Button */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <button
              onClick={onSignOut}
              className="w-full flex items-center space-x-2 px-4 py-2 rounded-lg text-red-600 hover:bg-red-100"
            >
              <LogOut className="h-5 w-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default InstitutionSidebar;