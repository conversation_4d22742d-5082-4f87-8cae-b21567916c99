import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import StatusPage from './StatusPage';
import { ChevronRight, ChevronLeft, Upload, X, Plus, Building2, Mail, Phone, Globe, FileText, User, MapPin } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface FormData {
  // Institution Details
  institutionName: string;
  registrationNumber: string;
  email: string;
  phone: string;
  website: string;

  // Address
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;

  // Admin Details
  adminName: string;
  adminEmail: string;
  adminPhone: string;
  adminDesignation: string;

  // Documents
  registrationCertificate: FileList;
  taxDocument: FileList;
  authorizationLetter: FileList;
}

export function InstitutionRegistration() {
  const navigate = useNavigate();
  const { loginWithCognito } = useAuth();
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      institutionName: '',
      registrationNumber: '',
      email: '',
      phone: '',
      website: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      adminName: '',
      adminEmail: '',
      adminPhone: '',
      adminDesignation: '',
    }
  });

  // We're watching documents to trigger re-renders when they change
  watch('registrationCertificate');
  watch('taxDocument');
  watch('authorizationLetter');

  const steps = [
    { number: 1, title: 'Institution Information' },
    { number: 2, title: 'Address Details' },
    { number: 3, title: 'Admin Details' },
    { number: 4, title: 'Documents' }
  ];

  const nextStep = async () => {
    if (step < steps.length) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('Form Data:', data);

      toast.success("Registration submitted successfully!");

      // Short delay before navigation to show success message
      setTimeout(() => {
        navigate('/institution-login', {
          state: {
            registrationSuccess: true,
            institutionName: data.institutionName
          }
        });
      }, 1500);
    } catch (error) {
      console.error('Registration error:', error);
      const apiMessage = (error as any)?.response?.data?.message || (error as Error)?.message || 'Registration failed. Please try again.';
      setErrorDetails({ message: apiMessage });
      setStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((s, i) => (
          <React.Fragment key={s.number}>
            <div className="flex flex-col items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  step >= s.number
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : 'border-gray-300 text-gray-500'
                }`}
              >
                {step > s.number ? (
                  <ChevronRight className="h-6 w-6" />
                ) : (
                  s.number
                )}
              </div>
              <span
                className={`mt-2 text-xs ${
                  step >= s.number ? 'text-blue-600 font-medium' : 'text-gray-500'
                }`}
              >
                {s.title}
              </span>
            </div>
            {i < steps.length - 1 && (
              <div
                className={`flex-1 h-0.5 mx-2 ${
                  step > i + 1 ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              ></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );

  const renderInstitutionInfo = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Institution Name</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Building2 className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('institutionName', { required: 'Institution name is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter institution name"
          />
        </div>
        {errors.institutionName && (
          <p className="mt-1 text-sm text-red-600">{errors.institutionName.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Registration Number</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FileText className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('registrationNumber', { required: 'Registration number is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter registration number"
          />
        </div>
        {errors.registrationNumber && (
          <p className="mt-1 text-sm text-red-600">{errors.registrationNumber.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Mail className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              }
            })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Phone</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Phone className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('phone', { required: 'Phone number is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter phone number"
          />
        </div>
        {errors.phone && (
          <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Website</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Globe className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('website')}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="https://www.example.com"
          />
        </div>
      </div>
    </div>
  );

  const renderAddressDetails = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Address</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MapPin className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('address', { required: 'Address is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter street address"
          />
        </div>
        {errors.address && (
          <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">City</label>
          <input
            {...register('city', { required: 'City is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter city"
          />
          {errors.city && (
            <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">State/Province</label>
          <input
            {...register('state', { required: 'State is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter state"
          />
          {errors.state && (
            <p className="mt-1 text-sm text-red-600">{errors.state.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Postal Code</label>
          <input
            {...register('postalCode', { required: 'Postal code is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter postal code"
          />
          {errors.postalCode && (
            <p className="mt-1 text-sm text-red-600">{errors.postalCode.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Country</label>
          <input
            {...register('country', { required: 'Country is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter country"
          />
          {errors.country && (
            <p className="mt-1 text-sm text-red-600">{errors.country.message}</p>
          )}
        </div>
      </div>
    </div>
  );

  const renderAdminDetails = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Admin Name</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <User className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('adminName', { required: 'Admin name is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter admin name"
          />
        </div>
        {errors.adminName && (
          <p className="mt-1 text-sm text-red-600">{errors.adminName.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Admin Email</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Mail className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('adminEmail', {
              required: 'Admin email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              }
            })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        {errors.adminEmail && (
          <p className="mt-1 text-sm text-red-600">{errors.adminEmail.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Admin Phone</label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Phone className="h-5 w-5 text-gray-400" />
          </div>
          <input
            {...register('adminPhone', { required: 'Admin phone is required' })}
            className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter admin phone"
          />
        </div>
        {errors.adminPhone && (
          <p className="mt-1 text-sm text-red-600">{errors.adminPhone.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Admin Designation</label>
        <input
          {...register('adminDesignation', { required: 'Admin designation is required' })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          placeholder="e.g. Principal, Director, etc."
        />
        {errors.adminDesignation && (
          <p className="mt-1 text-sm text-red-600">{errors.adminDesignation.message}</p>
        )}
      </div>
    </div>
  );

  const renderDocuments = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Registration Certificate</label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="registrationCertificate"
                className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
              >
                <span>Upload a file</span>
                <input
                  id="registrationCertificate"
                  type="file"
                  className="sr-only"
                  {...register('registrationCertificate', { required: 'Registration certificate is required' })}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">PDF, PNG, JPG up to 10MB</p>
          </div>
        </div>
        {errors.registrationCertificate && (
          <p className="mt-1 text-sm text-red-600">{errors.registrationCertificate.message}</p>
        )}
      </div>


      <div>
        <label className="block text-sm font-medium text-gray-700">Authorization Letter</label>
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="authorizationLetter"
                className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
              >
                <span>Upload a file</span>
                <input
                  id="authorizationLetter"
                  type="file"
                  className="sr-only"
                  {...register('authorizationLetter', { required: 'Authorization letter is required' })}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">PDF, PNG, JPG up to 10MB</p>
          </div>
        </div>
        {errors.authorizationLetter && (
          <p className="mt-1 text-sm text-red-600">{errors.authorizationLetter.message}</p>
        )}
      </div>
    </div>
  );

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return renderInstitutionInfo();
      case 2:
        return renderAddressDetails();
      case 3:
        return renderAdminDetails();
      case 4:
        return renderDocuments();
      default:
        return null;
    }
  };

  const renderStepButtons = () => (
    <div className="flex justify-between mt-8">
      {step > 1 ? (
        <button
          type="button"
          onClick={prevStep}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          Previous
        </button>
      ) : (
        <button
          type="button"
          onClick={() => navigate('/auth')}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          Back to Sign In
        </button>
      )}

      {step < steps.length ? (
        <button
          type="button"
          onClick={nextStep}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Next
          <ChevronRight className="h-5 w-5 ml-1" />
        </button>
      ) : (
        <button
          type="submit"
          disabled={isSubmitting}
          className={`flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ${
            isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Registration'}
        </button>
      )}
    </div>
  );

  // Handle Cognito registration
  const handleCognitoRegistration = () => {
    toast.info("Redirecting to AWS Cognito for secure registration...");
    // Short delay before redirecting to show the toast
    setTimeout(() => {
      loginWithCognito(true);
    }, 1500);
  };

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Registration Failed"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => { setStatus('idle'); setErrorDetails(null); }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white px-6 py-8 shadow-lg rounded-xl sm:px-10">
          <div className="mb-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              Institution Registration
            </h2>
            <p className="mt-2 text-gray-600">
              Register your institution to start supporting students
            </p>
          </div>

        


          {renderStepIndicator()}

          <form onSubmit={handleSubmit(onSubmit)}>
            {renderStepContent()}
            {renderStepButtons()}
          </form>
        </div>
      </div>
    </div>
  );
}

export default InstitutionRegistration;
