import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ChevronLeft, User, Mail } from 'lucide-react';
import StatusPage from './StatusPage';
import supporterService from '../services/supporterService';


interface GoogleUserData {
  email: string;
  name: string;
  picture: string;
}

export function SupporterRegistrationForm() {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userData, setUserData] = useState<GoogleUserData | null>(null);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(
    null
  );
  const [errorBackUrl, setErrorBackUrl] = useState<string>();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: ''
  });

  useEffect(() => {
    // Get prefilled data from localStorage (set during Google login)
    const prefillData = localStorage.getItem('supporter_registration_prefill');
    if (prefillData) {
      try {
        const data = JSON.parse(prefillData);
        setUserData(data);
        
        // Split name into first and last name
        const nameParts = data.name.split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        
        setFormData({
          firstName,
          lastName
        });
      } catch (error) {
        console.error('Error parsing prefill data:', error);
        localStorage.removeItem('supporter_registration_prefill');
        setErrorDetails({
          message: 'Error loading registration data. Please try again.'
        });
        setErrorBackUrl('/supporter-login');
        setStatus('error');
      }
    } else {
      // No prefill data, redirect to login
      localStorage.removeItem('supporter_registration_prefill');
      setErrorDetails({ message: 'Please sign in with Google first.' });
      setErrorBackUrl('/supporter-login');
      setStatus('error');
    }
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    

    if (!formData.firstName.trim()) {
      setErrorDetails({ message: 'First name is required' });
      setErrorBackUrl(undefined);
      setStatus('error');
      return;
    }

    if (!formData.lastName.trim()) {
      setErrorDetails({ message: 'Last name is required' });
      setErrorBackUrl(undefined);
      setStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      // Register the supporter
      await supporterService.registerSupporter({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim()
      });

      // Clean up localStorage
      localStorage.removeItem('supporter_registration_prefill');

      // Log in the user
   toast.success('Registration completed successfully!');
setTimeout(() => {
  navigate('/supporters', {
    state: {
      registrationSuccess: true,
      supporterName: `${formData.firstName} ${formData.lastName}`
    }
  });
}, 1500);
   } catch (error: any) {
  console.error('Registration error:', error);

  const apiMessage = error.response?.data?.message || '';
  const apiData = error.response?.data?.data || {};
  let errorMessage = apiMessage || 'Registration failed. Please try again.';
  if (Object.keys(apiData).length > 0) {
    const validationErrors = Object.entries(apiData)
      .map(([field, err]) => `• ${field}: ${err}`)
      .join('\n');
    errorMessage = apiMessage + '\n\n' + validationErrors;
  }
  setErrorDetails({ message: errorMessage });
  setStatus('error');
  setIsSubmitting(false);
}

  };

  const handleBack = () => {
    // Clean up and go back to login
    localStorage.removeItem('supporter_registration_prefill');
    navigate('/supporter-login');
  };

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Registration Failed"
        message={errorDetails?.message || ''}
        actionText={errorBackUrl ? 'Back to Login' : 'Try Again'}
        backUrl={errorBackUrl}
        onAction={
          errorBackUrl
            ? undefined
            : () => {
                setStatus('idle');
                setErrorDetails(null);
                setErrorBackUrl(undefined);
              }
        }
      />
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading registration form...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">

      {/* Header */}
      <div className="max-w-md mx-auto">
        <button
          onClick={handleBack}
          className="flex items-center text-blue-600 hover:text-blue-800 mb-8"
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          Back to Login
        </button>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <User className="h-8 w-8 text-blue-600" />
            </div>
            <h2 className="text-3xl font-bold mb-2">Complete Your Registration</h2>
            <p className="text-gray-600">
              Please confirm your details to create your supporter account
            </p>
          </div>

          {/* Google Account Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
             
              <div>
                <p className="font-medium text-gray-900">{userData.name}</p>
                <p className="text-sm text-gray-600 flex items-center">
                  <Mail className="h-4 w-4 mr-1" />
                  {userData.email}
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Name
              </label>
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
              
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your first name"
              />
              <p className="text-xs text-gray-500 mt-1">Pre-filled from your Google account</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Name
              </label>
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
              
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your last name"
              />
              <p className="text-xs text-gray-500 mt-1">Pre-filled from your Google account</p>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Registering...
                </>
              ) : (
                'Complete Registration'
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              By registering, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
