import axios from 'axios';

// Create an axios instance for external API calls
const externalApi = axios.create({
  baseURL: 'https://ashburn-rzs.icamxperts.com/edu-fund-services',
  headers: {
    'Content-Type': 'application/json',
    'accept': '*/*',
  },
  // Add timeout to prevent hanging requests
  timeout: 15000, // 15 seconds
});

// Add a request interceptor to include the auth token in requests if available
externalApi.interceptors.request.use(
  (config) => {
    // Get token from AWS Amplify session
    const token = localStorage.getItem('aws-amplify-auth-token') || sessionStorage.getItem('aws-amplify-auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.warn('No auth token found for API request');
    }

    // Log API requests for debugging
    console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
externalApi.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle CORS errors
    if (error.message && error.message.includes('Network Error')) {
      console.error('CORS or Network Error:', error.config.url);
      // Log the error but don't create mock responses
      console.error('API request failed:', error.config.method, error.config.url);
    }

    return Promise.reject(error);
  }
);

export default externalApi;
