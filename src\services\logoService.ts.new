import googleApi from './googleApi';
import api from './api';
import { getGoogleToken } from './googleApi';

interface LogoResponse {
  logoUrl: string;
}

/**
 * Service for fetching institution logo
 */
const logoService = {
  /**
   * Get the institution logo URL for student
   * @returns Promise with logo URL
   */
  getLogo: async (): Promise<string> => {
    try {
      const response = await googleApi.get<LogoResponse>('/api/student/v1/logo');
      return response.data.logoUrl;
    } catch (error) {
      console.error('Error fetching logo:', error);
      return ''; // Return empty string if logo fetch fails
    }
  },
  
  /**
   * Get the institution logo URL for supporter
   * @returns Promise with logo URL
   */
  getSupporterLogo: async (): Promise<string> => {
    try {
      const token = getGoogleToken();
      
      if (!token) {
        console.warn('No Google token available for logo fetch');
        return '';
      }
      
      const response = await api.get<LogoResponse>('/api/supporter/v1/institution/logo', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return response.data.logoUrl;
    } catch (error) {
      console.error('Error fetching supporter logo:', error);
      return ''; // Return empty string if logo fetch fails
    }
  }
};

export default logoService;