import externalApi from './externalApi';
import { v4 as uuidv4 } from 'uuid';

export interface ExternalUserCreateRequest {
  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  password?: string;
}

export interface ExternalUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service for managing users in the external EduFund API
 */
const externalUserService = {
  /**
   * Create a new user in the external API
   * @param userData User data
   * @returns Promise with created user
   */
  createUser: async (userData: ExternalUserCreateRequest): Promise<ExternalUser> => {
    try {
      console.log('Creating new user in external API with data:', userData);
      const response = await externalApi.post('/users', userData);
      console.log('Create user response from external API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating user in external API:', error);
      throw error;
    }
  },

  /**
   * Check if a user exists by email
   * @param email User email
   * @returns Promise with boolean indicating if user exists
   */
  checkUserExists: async (email: string): Promise<boolean> => {
    try {
      console.log(`Checking if user with email ${email} exists`);
      // This is a simplified implementation - in a real app, you would call an API endpoint
      // that can check if a user exists by email
      const response = await externalApi.get(`/users/email/${email}`);
      return !!response.data;
    } catch (error) {
      // If we get a 404, the user doesn't exist
      if (error.response && error.response.status === 404) {
        return false;
      }
      // For other errors, log and return false
      console.error('Error checking if user exists:', error);
      return false;
    }
  },

  /**
   * Get user by ID
   * @param id User ID
   * @returns Promise with user details
   */
  getUserById: async (id: string): Promise<ExternalUser> => {
    try {
      console.log(`Fetching user with ID: ${id}`);
      const response = await externalApi.get(`/users/${id}`);
      console.log('User response from external API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  },

  /**
   * Generate a unique user ID
   * @returns A UUID string
   */
  generateUserId: (): string => {
    return uuidv4();
  }
};

export default externalUserService;
