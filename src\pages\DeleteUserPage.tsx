import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { toast } from 'react-toastify';
import { X, AlertTriangle, Trash2 } from 'lucide-react';
import institutionService from '../services/institutionService';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import StatusPage from '../components/StatusPage';
import type { User } from '../services';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';
import cognitoService from '../services/cognitoService';

const DeleteUserPage = () => {
  const navigate = useNavigate();
  const auth = useAuth();
  const { userId } = useParams<{ userId: string }>();
  
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);

  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');
    toast.success('Successfully signed out');
    cognitoService.redirectToLogout();
  };

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!userId) {
        setErrorDetails({ message: 'User ID is required' });
        setStatus('error');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const users = await institutionService.getInstitutionUsers(auth);
        const user = users.find(u => u.id === userId);
        
        if (!user) {
          setErrorDetails({ message: 'User not found' });
          setStatus('error');
          return;
        }
        
        setSelectedUser(user);
      } catch (error) {
        console.error('Error fetching user details:', error);
        setErrorDetails({ message: 'Failed to load user details' });
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [userId, auth, navigate]);

  const handleDeleteUser = async () => {
    if (!selectedUser) {
      setErrorDetails({ message: 'User information is missing' });
      setStatus('error');
      return;
    }
    
    // Use the userId from URL params directly instead of roleId
    if (!userId) {
      setErrorDetails({ message: 'User ID is required' });
      setStatus('error');
      return;
    }
    
    try {
      setDeleting(true);
      await institutionService.deleteInstitutionUser(userId, auth);
      setStatus('success');
    } catch (error: any) {
      console.error('Error deleting user:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    } finally {
      setDeleting(false);
    }
  };

  // Show status page after operation
  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="User Deleted Successfully"
        message={`${selectedUser?.name || 'User'} has been permanently removed from your institution.`}
        actionText="Back to Settings"
        backUrl="/institution-dashboard?tab=settings"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="settings"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-100 p-4 md:p-8">
        {/* Breadcrumb */}
        <Breadcrumb items={getBreadcrumbItems('delete-user')} />

      <div className="max-w-2xl mx-auto mt-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800">Delete User</h1>
            <button
              onClick={() => navigate('/institution-dashboard?tab=settings')}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
              <span className="ml-2 text-gray-600">Loading user details...</span>
            </div>
          ) : selectedUser ? (
            <>
              <div className="mb-6">
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mx-auto mb-4">
                  <Trash2 className="h-8 w-8 text-red-600" />
                </div>
                <h2 className="text-xl font-semibold text-center mb-2">
                  Delete User Account
                </h2>
                <p className="text-gray-600 text-center">
                  Are you sure you want to delete <span className="font-medium">{selectedUser.name}</span>?
                </p>
              </div>

              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">User Information</h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">{selectedUser.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{selectedUser.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <p className="font-medium">
                        <span className={`px-2 py-1 text-xs rounded-full text-white ${
                          selectedUser.status?.toLowerCase() === 'active' ? 'bg-green-600' : 'bg-gray-500'
                        }`}>
                          {selectedUser.status}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Roles</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Array.isArray(selectedUser.roles) ? (
                          selectedUser.roles.map(role => (
                            <div key={role} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {role}
                            </div>
                          ))
                        ) : Array.isArray(selectedUser.role) ? (
                          selectedUser.role.map(role => (
                            <div key={role} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {role}
                            </div>
                          ))
                        ) : (
                          <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {selectedUser.role}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-red-800">Warning: Permanent Action</h3>
                      <p className="text-sm text-red-700 mt-1">
                        This action cannot be undone. The user will lose all access to the system and any associated data.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-8">
                <button
                  onClick={() => navigate('/institution-dashboard?tab=settings')}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteUser}
                  disabled={deleting}
                  className={`px-4 py-2 ${
                    deleting
                      ? 'bg-red-400 cursor-not-allowed'
                      : 'bg-red-600 hover:bg-red-700'
                  } text-white rounded flex items-center`}
                >
                  {deleting && (
                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                  )}
                  Delete User
                </button>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-red-600">User not found</p>
              <button
                onClick={() => navigate('/institution-dashboard?tab=settings')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Back to Settings
              </button>
            </div>
          )}
        </div>
        </div>
      </div>
      </div>
      
     
    </div>
  );
};

export default DeleteUserPage;