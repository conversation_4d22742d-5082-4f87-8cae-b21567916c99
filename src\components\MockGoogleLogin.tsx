import React from 'react';
import { FcGoogle } from 'react-icons/fc';
import { authConfig } from '../config/auth.config';

interface MockGoogleLoginProps {
  buttonText?: string;
  onSuccess: (response: any) => void;
  onError: () => void;
  customClass?: string;
  iconClass?: string;
  textClass?: string;
}

export function MockGoogleLogin({
  buttonText = "Continue with Google",
  onSuccess,
  onError,
  customClass,
  iconClass,
  textClass
}: MockGoogleLoginProps) {
  const handleClick = () => {
    // Get the current user type from the URL
    const isStudentLogin = window.location.pathname.includes('student');

    // Simulate API delay
    setTimeout(() => {
      // Use a consistent user based on the login type
      const userIndex = isStudentLogin ? 0 : 1; // 0 for student, 1 for supporter
      const user = authConfig.mockGoogleUsers[userIndex];

      console.log('Selected mock user:', user);

      // Mock Google OAuth response
      const mockResponse = {
        credential: btoa(JSON.stringify({
          email: user.email,
          name: user.name,
          picture: user.picture,
          sub: 'mock-google-id-' + user.email, // Use email to make this consistent
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
        })),
      };

      onSuccess(mockResponse);
    }, 1000);
  };

  // Use custom classes if provided, otherwise use default classes
  const buttonClassName = customClass || "w-full flex items-center justify-center gap-2 bg-white text-gray-700 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500";
  const googleIconClassName = iconClass || "w-5 h-5";
  const textClassName = textClass || "";

  return (
    <button
      onClick={handleClick}
      className={buttonClassName}
    >
      <FcGoogle className={googleIconClassName} />
      <span className={textClassName}>{buttonText}</span>
    </button>
  );
}