import React, { createContext, useContext, useState, useEffect } from 'react';
import authService, { LoginRequest, LoginResponse } from '../services/authService';

interface User {
  id: number | string;
  name: string;
  email: string;
  userType: string;
  profileId?: number | string;
}

interface SupporterAuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (credentials: LoginRequest, rememberMe?: boolean) => Promise<LoginResponse>;
  logout: (redirectPath?: string) => void;
  loading: boolean;
}

const SupporterAuthContext = createContext<SupporterAuthContextType>({
  isAuthenticated: false,
  user: null,
  login: async () => ({ token: '', id: '', name: '', email: '', userType: '', profileId: '' }),
  logout: () => {},
  loading: false,
});

export function SupporterAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from localStorage or sessionStorage on component mount
  useEffect(() => {
    const checkAuthState = () => {
      // Check for regular authentication
      const savedUser = authService.getCurrentUser();
      if (savedUser && savedUser.userType.toLowerCase() === 'supporter') {
        setUser(savedUser);
        return true;
      }

      // Check for Google auth data if no regular user found
      const supporterEmail = localStorage.getItem('supporter_email');
      const googleName = localStorage.getItem('google_auth_name');
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      
      if (token && supporterEmail && googleName) {
        console.log('Token and Google auth data found, creating user from Google data');
        const googleUser = {
          id: supporterEmail,
          name: googleName,
          email: supporterEmail,
          userType: 'SUPPORTER',
          profileId: supporterEmail
        };
        setUser(googleUser);
        // Save the user to storage to prevent this issue in the future
        authService.saveUser(googleUser, true);
        return true;
      }

      return false;
    };

    checkAuthState();
    setLoading(false);
  }, []);

  // Consider authenticated if we have a user object, token in storage, or Google auth
  const hasToken = !!(localStorage.getItem('token') || sessionStorage.getItem('token'));
  const hasGoogleAuth = Object.keys(localStorage).some(key =>
    key.startsWith('supporter_registered_') ||
    key === 'supporter_email' ||
    (key.includes('google') && key.includes('auth'))
  );
  const isAuthenticated = (user !== null && user.userType.toLowerCase() === 'supporter') || hasToken || hasGoogleAuth;

  // Regular login with email/password
  const login = async (credentials: LoginRequest, rememberMe = false): Promise<LoginResponse> => {
    try {
      setLoading(true);
      const response = await authService.login(credentials);

      // Save token and user data based on rememberMe preference
      authService.saveToken(response.token, rememberMe);

      const userData = {
        id: response.id,
        name: response.name,
        email: response.email,
        userType: response.userType,
        profileId: response.profileId
      };

      authService.saveUser(userData, rememberMe);

      // Update state
      setUser(userData);

      return response;
    } catch (error) {
      console.error('Supporter login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout
  const logout = async (redirectPath?: string) => {
    // Store the redirect path if provided
    if (redirectPath) {
      sessionStorage.setItem('logout_redirect', redirectPath);
    }

    console.log('Logging out supporter user');

    // Clear secure Google token from memory
    try {
      const { clearGoogleToken } = await import('../services/googleApi');
      clearGoogleToken();
    } catch (error) {
      console.error('Error clearing Google token:', error);
    }

    // Regular logout - this clears localStorage and sessionStorage
    authService.logout();

    // Double-check to ensure all auth-related items are cleared
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');

    // Clear Google auth data
    const supporterEmail = localStorage.getItem('supporter_email');
    if (supporterEmail) {
      localStorage.removeItem(`supporter_registered_${supporterEmail}`);
    }

    localStorage.removeItem('supporter_email');
    localStorage.removeItem('google_auth_picture');
    localStorage.removeItem('google_auth_name');

    // Clear any other Google auth-related items
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('supporter_registered_') ||
          key.includes('google') ||
          key.includes('oauth') ||
          key.includes('supporter')) {
        console.log('Removing localStorage item:', key);
        localStorage.removeItem(key);
      }
    });

    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('supporter_registered_') ||
          key.includes('google') ||
          key.includes('oauth') ||
          key.includes('supporter')) {
        console.log('Removing sessionStorage item:', key);
        sessionStorage.removeItem(key);
      }
    });

    // Handle Google sign out if Google's OAuth client is available
    if (window.google && window.google.accounts && window.google.accounts.id) {
      console.log('Google OAuth client found, revoking and signing out');
      // Revoke Google authentication
      window.google.accounts.id.disableAutoSelect();

      // If we have the email, we can revoke access
      if (supporterEmail) {
        window.google.accounts.id.revoke(supporterEmail, () => {
          console.log('Google OAuth token revoked for', supporterEmail);
        });
      }

      // Sign out from Google
      const googleLogoutUrl = 'https://accounts.google.com/logout';

      // Open Google logout in a hidden iframe to sign out from Google
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = googleLogoutUrl;
      document.body.appendChild(iframe);

      // Remove the iframe after a short delay
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 1000);
    }

    // Clear local state
    setUser(null);

    console.log('Supporter logout complete, all storage cleared');

    // Redirect out of the app once cleanup is done
    if (redirectPath) {
      setTimeout(() => {
        window.location.href = redirectPath;
      }, 100);
    }
  };

  return (
    <SupporterAuthContext.Provider value={{
      isAuthenticated,
      user,
      login,
      logout,
      loading
    }}>
      {children}
    </SupporterAuthContext.Provider>
  );
}

export const useSupporterAuth = () => useContext(SupporterAuthContext);
