import externalApi from './externalApi';
import { v4 as uuidv4 } from 'uuid';
import externalUserService, { ExternalUserCreateRequest } from './externalUserService';
import api from './api';
import googleApi, { getGoogleToken } from './googleApi';

export interface ExternalStudentCreateRequest {
  userId: string;
  institutionId: string;
  studentRegId: string;
  course: string;
  department: string;
  year: string;
}

export interface ExternalStudent {
  id: string;
  userId: string;
  institutionId: string;
  studentRegId: string;
  course: string;
  department: string;
  year: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service for managing students in the external EduFund API
 */
const externalStudentService = {
  /**
   * Check if a student exists by email in the institution's student list
   * @param institutionId Institution ID
   * @param email Student email
   * @returns Promise with boolean indicating if student exists
   */
  checkStudentExists: async (email: string): Promise<boolean> => {
    try {
      console.log(`Checking if student with email ${email} exists`);
      // Use the hardcoded institution ID
      const institutionId = 'ddb0d2a9-f150-4de6-8608-765b91b153e1';

      // Get all students for the institution
      const response = await externalApi.get(
        `/students/institution/${institutionId}?page=0&size=20&sort=firstName,asc`
      );

      // Check if any student has the given email
      const students = response.data.content || [];
      const exists = students.some((student: any) => student.email === email);

      console.log(`Student with email ${email} exists: ${exists}`);
      return exists;
    } catch (error) {
      console.error('Error checking if student exists:', error);
      return false;
    }
  },



checkLoggedInStudent: async (): Promise<{ exists: boolean; status: string | null }> => {
  try {
    console.log('🔍 Checking logged-in student status from backend...');
    const response = await googleApi.get('/api/student/v1/me/check');
    return {
      exists: response.data.exists,
      status: response.data.status,
    };
  } catch (error: any) {
    console.error('❌ checkLoggedInStudent error:', error);
    // On any error, assume student does not exist and status is unknown
    return { exists: false, status: null };
  }
},


  /**
   * Register a student in the backend with Google authentication (secured endpoint)
   */
  registerStudent: async (studentData: {
    studentRegId: string;
    course: string;
    department: string;
    year: string;
    // institutionId: string;
    name: string;
    phoneNumber: string;
    batch: string;
  }): Promise<{ success: boolean }> => {
    try {
      console.log('Registering student via /api/student/v1/register:', studentData);
      const response = await googleApi.post('/api/student/v1/register', studentData);
      console.log('Register API response:', response.data);
      return { success: true };
    } catch (error) {
      console.error('Error registering student with secure API:', error);
      throw error;
    }
  },

getStudentDashboard: async (): Promise<{
  totalRaisedAmount: number;
  activeCampaignCount: number;
}> => {
  try {
    const response = await googleApi.get('/api/student/v1/dashboard');
    return response.data;
  } catch (error) {
    console.error('Error fetching student dashboard:', error);
    throw error;
  }
},

getStudentCampaigns: async (
  page: number = 0,
  size: number = 10,
  status?: string,
  sort: string = 'desc'
): Promise<{
  content: any[]; // You can define a proper `Campaign` interface if needed
  totalPages: number;
  totalElements: number;
}> => {
  try {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('size', size.toString());
    params.append('sort', sort);
    if (status) params.append('status', status);

    const response = await googleApi.get(`/api/student/v1/campaigns?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching student campaigns:', error);
    throw error;
  }
},

  
  /**
   * Create a new student in the external API
   * @param studentData Student data
   * @returns Promise with created student
   */
  createStudent: async (studentData: ExternalStudentCreateRequest): Promise<ExternalStudent> => {
    try {
      console.log('Creating new student in external API with data:', studentData);
      const response = await externalApi.post('/students', studentData);
      console.log('Create student response from external API:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating student in external API:', error);
      throw error;
    }
  },

  /**
   * Generate a unique user ID for a new student
   * @returns A UUID string
   */
  generateUserId: (): string => {
    return uuidv4();
  },

  /**
   * Create a user and then a student in the external API
   * @param userData User data
   * @param studentData Student data (without userId)
   * @returns Promise with created student
   */
  createUserAndStudent: async (
    userData: {
      firstName: string;
      lastName: string;
      email: string;
      password?: string;
    },
    studentData: {
      institutionId: string;
      studentRegId: string;
      course: string;
      department: string;
      year: string;
    }
  ): Promise<ExternalStudent> => {
    try {
      console.log('Creating new user and student in external API');

      // First, create the user
      const userCreateRequest: ExternalUserCreateRequest = {
        ...userData,
        userType: 'STUDENT' // Set user type to STUDENT
      };

      console.log('Creating user with data:', userCreateRequest);
      const createdUser = await externalUserService.createUser(userCreateRequest);
      console.log('User created successfully:', createdUser);

      // Then, create the student with the user ID
      const studentCreateRequest: ExternalStudentCreateRequest = {
        ...studentData,
        userId: createdUser.id
      };

      console.log('Creating student with data:', studentCreateRequest);
      const createdStudent = await externalStudentService.createStudent(studentCreateRequest);
      console.log('Student created successfully:', createdStudent);

      return createdStudent;
    } catch (error) {
      console.error('Error creating user and student:', error);
      throw error;
    }
  },

  getStudentCampaignStatus: async (): Promise<{ hasActiveCampaign: boolean }> => {
  try {
    const response = await googleApi.get('/api/student/v1/me/campaign-status');
    return response.data;
  } catch (error) {
    console.error('Error fetching student campaign status:', error);
    throw error;
  }
},

getStudentProfile: async (): Promise<{
  academicRecord: string;
  institutionName: string;
  studentRegId: string;
  name: string;
  course: string;
  year: string;
  batch: string;
  phone: string;
}> => {
  try {
    const response = await googleApi.get('/api/student/v1/profile');
    return response.data;
  } catch (error) {
    console.error('Error fetching student profile:', error);
    throw error;
  }
},
updateStudentProfile: async (profileData: {
  name: string;
  course: string;
  year: string;
  batch: string;
  phone: string;
}): Promise<void> => {
  try {
    await googleApi.put('/api/student/v1/profile', profileData);
  } catch (error) {
    console.error('Error updating student profile:', error);
    throw error;
  }
},
  createStudentCampaign: async (campaignData: {
    title: string;
    description: string;
    goalAmount: number;
    startDate: string;
    endDate: string;
    validDateRange: boolean;
  }): Promise<void> => {
    try {
      await googleApi.post('/api/student/v1/campaigns', campaignData);
    } catch (error) {
      console.error('Error creating student campaign:', error);
      throw error;
    }
  },

  /**
   * Get details of a specific campaign for the student
   * @param campaignId Campaign identifier
   * @returns Promise with campaign details
   */
  getStudentCampaignDetails: async (campaignId: string): Promise<any> => {
    try {
      const response = await googleApi.get(`/api/student/v1/campaigns/${campaignId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching student campaign details:', error);
      throw error;
    }
  },

  /**
   * Terminate a campaign for the student
   * @param campaignId Campaign identifier
   * @param reason Reason for termination
   */
  terminateStudentCampaign: async (
    campaignId: string,
    reason: string
  ): Promise<void> => {
    try {
      await googleApi.put(`/api/student/v1/${campaignId}/terminate`, { reason });
    } catch (error) {
      console.error('Error terminating student campaign:', error);
      throw error;
    }
  },


};

export default externalStudentService;
