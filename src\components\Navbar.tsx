import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { GraduationCap, LogIn, LogOut, ChevronLeft, Menu, X } from 'lucide-react';
import { useStudentAuth } from '../context/StudentAuthContext';
import { useSupporterAuth } from '../context/SupporterAuthContext';

// Add type declaration for Google OAuth client
declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          disableAutoSelect: () => void;
          revoke: (email: string, callback: () => void) => void;
        };
      };
    };
  }
}

// Add a new prop to control whether to show the sign in button
interface NavbarProps {
  title?: string;
  displaySignOutButton?: boolean;
  hideAuthButtons?: boolean;
  transparent?: boolean;
  className?: string;
}

export function Navbar({
  title = 'EDU-FUND',
  displaySignOutButton = false,
  hideAuthButtons = false,
  transparent = false,
  className = '',
}: NavbarProps) {
  const navigate = useNavigate();
  const { isAuthenticated: studentAuthenticated, logout: studentLogout } = useStudentAuth();
  const { isAuthenticated: supporterAuthenticated, logout: supporterLogout } = useSupporterAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const currentUrl = window.location.href;
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const basePath = import.meta.env.VITE_BASE_PATH || '/';
  const currentOrigin = window.location.origin;

  const isExactBaseUrl =
    currentUrl === baseUrl ||
    currentUrl === baseUrl + '/' ||
    currentUrl === `${currentOrigin}${basePath}` ||
    currentUrl === `${currentOrigin}${basePath}/`;




  const hasToken = !!(localStorage.getItem('token') || sessionStorage.getItem('token'));
  let isAuthenticated = false;

  if (location.pathname.includes('/student')) {
    isAuthenticated = studentAuthenticated;
  } else if (location.pathname.includes('/supporter')) {
    isAuthenticated = supporterAuthenticated;
  } else {
    isAuthenticated = studentAuthenticated || supporterAuthenticated || hasToken;
  }

  const handleSignOut = () => {
    console.log('Sign Out button clicked');
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');

    Object.keys(localStorage).forEach((key) => {
      if (
        key.startsWith('supporter_registered_') ||
        key.includes('google') ||
        key.includes('oauth') ||
        key.includes('supporter')
      ) {
        localStorage.removeItem(key);
      }
    });

    Object.keys(sessionStorage).forEach((key) => {
      if (
        key.startsWith('supporter_registered_') ||
        key.includes('google') ||
        key.includes('oauth') ||
        key.includes('supporter')
      ) {
        sessionStorage.removeItem(key);
      }
    });

    let redirectPath = '/';
    let logoutFunction = studentLogout;
    const currentPath = location.pathname;

    if (currentPath.includes('/student')) {
      redirectPath = '/student-login';
      logoutFunction = studentLogout;
    } else if (currentPath.includes('/supporter')) {
      redirectPath = '/supporter-login';
      logoutFunction = supporterLogout;
    }

    logoutFunction(redirectPath);
    setTimeout(() => {
      if (currentPath.includes('/student')) {
        window.location.href = `${baseUrl}/student-login`;
      } else if (currentPath.includes('/supporter')) {
        localStorage.clear();
        sessionStorage.clear();

        const supporterEmail = localStorage.getItem('supporter_email');
        if (supporterEmail) {
          localStorage.removeItem(`supporter_registered_${supporterEmail}`);
        }

        if (window.google && window.google.accounts && window.google.accounts.id) {
          window.google.accounts.id.disableAutoSelect();
          const iframe = document.createElement('iframe');
          iframe.style.display = 'none';
          iframe.src = 'https://accounts.google.com/logout';
          document.body.appendChild(iframe);
          setTimeout(() => {
            document.body.removeChild(iframe);
            window.location.href = `${baseUrl}/supporter-login`;
          }, 1000);
        } else {
          window.location.href = `${baseUrl}/supporter-login`;
        }
      } else {
        window.location.href = `${baseUrl}/`;
      }
    }, 100);
  };

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <nav className={`${transparent ? 'bg-transparent' : 'bg-white shadow-sm'} ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            {isAuthenticated && (
              <button
                onClick={handleBack}
                className="mr-4 p-2 rounded-full hover:bg-gray-100 text-gray-600"
                aria-label="Go back"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
            )}
            <Link to="/" className="flex items-center">
              <span></span>
              <GraduationCap className={`h-8 w-8 ${transparent ? 'text-white' : 'text-blue-600'}`} />
              <span className={`ml-2 text-xl font-bold ${transparent ? 'text-white' : 'text-gray-900'}`}>
                {title}
              </span>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>

          {/* Desktop navigation */}
          {isExactBaseUrl && (
            <div className="hidden lg:flex items-center space-x-4 mr-6">
              <Link
                to="/auth"
                className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Sign In
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && isExactBaseUrl && (
        <div className="lg:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              to="/auth"
              className="flex items-center px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:bg-blue-50"
            >
              <LogIn className="h-5 w-5 mr-2" />
              Sign In
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
}
