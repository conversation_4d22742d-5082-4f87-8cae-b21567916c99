import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { toast } from 'react-toastify';
import { X, Check } from 'lucide-react';
import institutionService from '../services/institutionService';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import StatusPage from '../components/StatusPage';
import type { User } from '../services';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';
import cognitoService from '../services/cognitoService';

const AssignRolePage = () => {
  const navigate = useNavigate();
  const auth = useAuth();
  const { userId } = useParams<{ userId: string }>();
  
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [availableRoles] = useState<string[]>(['INSTITUTION_ADMIN', 'FINANCIAL_ADMIN', 'CLERK']);
  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);

  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');
    toast.success('Successfully signed out');
    cognitoService.redirectToLogout();
  };

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (!userId) {
        setErrorDetails({ message: 'User ID is required' });
        setStatus('error');
        return;
      }

      try {
        setLoading(true);
        const users = await institutionService.getInstitutionUsers(auth);
        const user = users.find(u => u.id === userId);
        
        if (!user) {
          setErrorDetails({ message: 'User not found' });
          setStatus('error');
          return;
        }
        
        setSelectedUser(user);
      } catch (error) {
        console.error('Error fetching user details:', error);
        setErrorDetails({ message: 'Failed to load user details' });
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [userId, auth, navigate]);

  const handleAssignRoles = async () => {
    if (!selectedUser || selectedRoles.length === 0) {
      toast.error('Please select at least one role to assign');
      return;
    }
    
    try {
      setAssigning(true);
      await institutionService.assignUserRoles(selectedUser.id, selectedRoles, auth);
      setStatus('success');
    } catch (error: any) {
      console.error('Error assigning roles:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    } finally {
      setAssigning(false);
    }
  };

  // Get user's current roles
  const getUserRoles = () => {
    if (!selectedUser) return [];
    
    if (Array.isArray(selectedUser.roles)) {
      return selectedUser.roles;
    } else if (Array.isArray(selectedUser.role)) {
      return selectedUser.role;
    } else if (selectedUser.role) {
      return [selectedUser.role];
    }
    
    return [];
  };

  // Filter out roles the user already has
  const filteredAvailableRoles = availableRoles.filter(role => {
    const userRoles = getUserRoles();
    return !userRoles.includes(role);
  });

  // Toggle role selection
  const toggleRoleSelection = (role: string) => {
    setSelectedRoles(prev => 
      prev.includes(role) 
        ? prev.filter(r => r !== role) 
        : [...prev, role]
    );
  };

  // Get role description
  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'INSTITUTION_ADMIN':
        return 'Institution Admin has full access to all features including user management, campaign management, approvals, and financial reports.';
      case 'FINANCIAL_ADMIN':
        return 'Financial Admin can manage campaigns, approvals, bank details, and download reports, but cannot manage users or institution profile.';
      case 'CLERK':
        return 'Clerk has limited access with view-only permissions for most features.';
      default:
        return '';
    }
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="Roles Assigned Successfully"
        message={`${selectedRoles.length > 1 ? 'Roles have' : 'Role has'} been assigned to ${selectedUser?.name || 'the user'}.`}
        actionText="Back to Settings"
        backUrl="/institution-dashboard?tab=settings"
        showHeader={false}
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
        showHeader={false}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="settings"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-100 p-4 md:p-8">
        {/* Breadcrumb */}
        <Breadcrumb items={getBreadcrumbItems('assign-role')} />

      <div className="max-w-2xl mx-auto mt-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-800">Assign Roles</h1>
            <button
              onClick={() => navigate('/institution-dashboard?tab=settings')}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading user details...</span>
            </div>
          ) : selectedUser ? (
            <>
              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">User Information</h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">{selectedUser.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{selectedUser.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <p className="font-medium">
                        <span className={`px-2 py-1 text-xs rounded-full text-white ${
                          selectedUser.status?.toLowerCase() === 'active' ? 'bg-green-600' : 'bg-gray-500'
                        }`}>
                          {selectedUser.status}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Current Roles</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {getUserRoles().length > 0 ? (
                          getUserRoles().map(role => (
                            <div key={role} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {role}
                            </div>
                          ))
                        ) : (
                          <p className="text-sm text-gray-500">No roles assigned</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h2 className="text-lg font-semibold mb-4">Assign New Roles</h2>
                
                {filteredAvailableRoles.length > 0 ? (
                  <div>
                    <p className="text-sm text-gray-600 mb-3">
                      Select one or more roles to assign to this user:
                    </p>
                    
                    <div className="space-y-3">
                      {filteredAvailableRoles.map(role => (
                        <div 
                          key={role} 
                          className={`border rounded-lg p-4 cursor-pointer ${
                            selectedRoles.includes(role) 
                              ? 'border-blue-500 bg-blue-50' 
                              : 'border-gray-200 hover:border-blue-300'
                          }`}
                          onClick={() => toggleRoleSelection(role)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className={`w-5 h-5 rounded border flex items-center justify-center ${
                                selectedRoles.includes(role) 
                                  ? 'bg-blue-600 border-blue-600' 
                                  : 'border-gray-400'
                              }`}>
                                {selectedRoles.includes(role) && (
                                  <Check className="h-3 w-3 text-white" />
                                )}
                              </div>
                              <span className="ml-2 font-medium">{role}</span>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 mt-2 pl-7">
                            {getRoleDescription(role)}
                          </p>
                        </div>
                      ))}
                    </div>
                    
                    {selectedRoles.length > 0 && (
                      <div className="mt-4 p-4 bg-blue-50 border border-blue-100 rounded-md">
                        <h3 className="font-medium text-blue-800 mb-2">Selected Roles ({selectedRoles.length})</h3>
                        <div className="flex flex-wrap gap-2">
                          {selectedRoles.map(role => (
                            <div key={role} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full flex items-center">
                              {role}
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleRoleSelection(role);
                                }}
                                className="ml-2 text-blue-600 hover:text-blue-800"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="p-4 bg-yellow-50 border border-yellow-100 rounded-md">
                    <p className="text-yellow-700">
                      This user already has all available roles assigned.
                    </p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-8">
                <button
                  onClick={() => navigate('/institution-dashboard?tab=settings')}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAssignRoles}
                  disabled={selectedRoles.length === 0 || assigning || filteredAvailableRoles.length === 0}
                  className={`px-4 py-2 ${
                    selectedRoles.length === 0 || assigning || filteredAvailableRoles.length === 0
                      ? 'bg-blue-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white rounded flex items-center`}
                >
                  {assigning && (
                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                  )}
                  Assign {selectedRoles.length > 1 ? 'Roles' : 'Role'}
                </button>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-red-600">User not found</p>
              <button
                onClick={() => navigate('/institution-dashboard?tab=settings')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Back to Settings
              </button>
            </div>
          )}
        </div>
        </div>
      </div>
      </div>
      
    
    </div>
  );
};

export default AssignRolePage;