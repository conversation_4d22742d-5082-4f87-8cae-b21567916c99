import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { activityService, type Activity } from '../services';
import { Pagination } from '@mui/material';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from '../components/InstitutionHeader';
import { InstitutionFooter } from '../components/InstitutionFooter';
import { toast } from 'react-toastify';
import cognitoService from '../services/cognitoService';

export function ActivitiesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activities, setActivities] = useState<Activity[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const navigate = useNavigate();

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');
    toast.success('Successfully signed out');
    cognitoService.redirectToLogout();
  };

  const fetchActivities = async (pageNumber: number) => {
    setLoading(true);
    try {
      const response = await activityService.adminSearchActivities({
        entityId: '',
        entityType: 'institution',
        userId: '',
        action: searchTerm,
        page: pageNumber - 1,
        size: 10,
        sortBy: 'timestamp',
        sortDir: 'desc',
      });
      setActivities(response.content);
      setTotalPages(response.totalPages || 0);
      setPage(pageNumber);
    } catch (err) {
      console.error('Failed to search activities', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    fetchActivities(1);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="dashboard"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-50 p-4">
          <h1 className="text-xl font-semibold mb-4">Activities</h1>
      <div className="flex mb-4">
        <input
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search activities..."
          className="flex-grow border border-gray-300 rounded-md px-3 py-2"
        />
        <button
          onClick={handleSearch}
          className="ml-2 px-4 py-2 bg-blue-600 text-white rounded-md"
        >
          Search
        </button>
      </div>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left">Action</th>
                <th className="px-4 py-2 text-left">Description</th>
                <th className="px-4 py-2 text-left">Time</th>
              </tr>
            </thead>
            <tbody>
              {activities.map((a) => (
                <tr
                  key={a.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => navigate(`/activities/${a.id}`)}
                >
                  <td className="px-4 py-2">{a.action || a.type}</td>
                  <td className="px-4 py-2">{a.description}</td>
                  <td className="px-4 py-2">{new Date(a.timestamp).toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {totalPages > 1 && (
        <div className="mt-4">
          <Pagination
            count={totalPages}
            page={page}
            onChange={(_, val) => fetchActivities(val)}
          />
          </div>
        )}
        </div>
      </div>
      
    
    </div>
  );
}

export default ActivitiesPage;


