import React, { useState } from 'react';
import { X, <PERSON>ertTriangle, Check } from 'lucide-react';
import { toast } from 'react-toastify';
import StatusPage from './StatusPage';

interface TerminateCampaignModalProps {
  campaignId: number;
  campaignTitle: string;
  onClose: () => void;
  onTerminate: (reason: string) => Promise<void>;
}

export function TerminateCampaignModal({
  campaignId,
  campaignTitle,
  onClose,
  onTerminate
}: TerminateCampaignModalProps) {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'reason' | 'confirm'>('reason');
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!reason.trim()) {
      setErrorMessage('Please provide a reason for termination');
      setStatus('error');
      return;
    }

    if (step === 'reason') {
      setStep('confirm');
      return;
    }

    try {
      setIsSubmitting(true);
      await onTerminate(reason);
      toast.success('Campaign terminated successfully');
      onClose();
    } catch (error: any) {
      console.error('Error terminating campaign:', error);
      const apiMessage = error?.response?.data?.message;
      const message = apiMessage || error?.message || 'Failed to terminate campaign';
      setErrorMessage(message);
      setStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorMessage || ''}
        actionText="Retry"
        onAction={() => {
          setStatus('idle');
          setErrorMessage(null);
        }}
        secondaryActionText="Cancel"
        onSecondaryAction={onClose}
      />
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="flex justify-between items-center border-b p-4">
          <h2 className="text-xl font-semibold text-gray-800">
            {step === 'reason' ? 'Terminate Campaign' : 'Confirm Termination'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          {step === 'reason' ? (
            <>
              <div className="mb-6">
                <p className="text-gray-600 mb-2">
                  You are about to terminate the campaign:
                </p>
                <p className="font-medium text-gray-800">{campaignTitle}</p>
              </div>

              <div className="mb-6">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for Termination <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="reason"
                  rows={4}
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Please provide a detailed reason for terminating this campaign..."
                />
                <p className="mt-1 text-sm text-gray-500">
                  This reason will be communicated to the student and included in the audit log.
                </p>
              </div>
            </>
          ) : (
            <div className="mb-6">
              <div className="flex items-start mb-4">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-amber-500" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">Confirm Termination</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This action cannot be undone. The campaign will be permanently terminated and all stakeholders will be notified.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
                <h4 className="font-medium text-gray-800 mb-2">Termination Reason:</h4>
                <p className="text-gray-600">{reason}</p>
              </div>

              <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
                <h4 className="font-medium text-amber-800 mb-2">What happens next?</h4>
                <ul className="list-disc list-inside text-sm text-amber-700 space-y-1">
                  <li>The campaign status will be changed to "Terminated"</li>
                  <li>The student will be notified via email</li>
                  <li>No further donations will be accepted</li>
                  <li>The termination will be recorded in the audit log</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 border-t p-4 bg-gray-50 rounded-b-lg">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className={`flex items-center px-4 py-2 rounded-md text-white ${
              step === 'reason' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'
            }`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : step === 'reason' ? (
              'Continue'
            ) : (
              <>
                <Check className="h-4 w-4 mr-1" />
                Confirm Termination
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

export default TerminateCampaignModal;
