import axios from 'axios';
import { backendUrl } from '../config/api';

// Create base axios instance with auth token support
const api = axios.create({
  baseURL: backendUrl,
  responseType: 'json',
});

// Add request interceptor to add auth token to all requests
api.interceptors.request.use(
  async (config) => {
    // Get token from localStorage
    const token = localStorage.getItem('id_token');
    
    // Debug token presence
    console.log('API Request:', {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      tokenFirstChars: token ? token.substring(0, 10) + '...' : 'none'
    });
    
    if (token) {
      // Ensure headers object exists
      config.headers = config.headers || {};
      // Set Authorization header with Bearer token
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for handling 401s
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data
    });
    
    if (error.response?.status === 401) {
      // Clear token from localStorage
      localStorage.removeItem('id_token');

      // Determine which login route to use based on the current URL
      const basePath = window.location.pathname.includes('${import.meta.env.VITE_BASE_PATH}') ? '${import.meta.env.VITE_BASE_PATH}/' : '/';
      const currentPath = window.location.pathname.toLowerCase();

      let loginRoute = 'institution-login';
      if (currentPath.includes('student')) {
        loginRoute = 'student-login';
      } else if (currentPath.includes('supporter')) {
        loginRoute = 'supporter-login';
      }

      // Redirect the user to the appropriate login page
      window.location.href = basePath + loginRoute;
    }
    return Promise.reject(error);
  }
);

// Export the base instance for all requests (now with auth)
export default api;


