import { InstitutionCampaignDetailsPage } from './InstitutionCampaignDetailsPage';

// This is a wrapper component that prevents the original component from redirecting
export function DebugInstitutionCampaignDetailsPage() {
  // Override window.location.href to prevent redirects
  const originalWindowLocation = window.location;
  Object.defineProperty(window, 'location', {
    writable: true,
    value: {
      ...originalWindowLocation,
      href: originalWindowLocation.href,
      replace: (url: string) => {
        console.log(`Prevented redirect to: ${url}`);
        return;
      },
      assign: (url: string) => {
        console.log(`Prevented redirect to: ${url}`);
        return;
      }
    }
  });

  // Mock authentication data
  localStorage.setItem('id_token', 'debug-token');
  localStorage.setItem('institution_role', 'INSTITUTION_ADMIN');
  
  return <InstitutionCampaignDetailsPage />;
}