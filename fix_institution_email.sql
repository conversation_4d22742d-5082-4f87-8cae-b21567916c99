-- SQL script to fix the institution profile email issue

-- 1. First, let's check the current structure of the institutions table
-- This helps us understand the column names and constraints
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'institutions';

-- 2. Check if there's a trigger or constraint that might be changing the email value
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers
WHERE event_object_table = 'institutions';

-- 3. Update the PATCH endpoint handler in the backend code
-- Since we can't directly modify the Java code, we'll create a function that can be called
-- from the application to properly handle the email field

CREATE OR REPLACE FUNCTION update_institution_profile(
    p_institution_id INTEGER,
    p_name VARCHAR,
    p_website_url VARCHAR,
    p_email VARCHAR,
    p_contact_phone VARCHAR,
    p_address VARCHAR,
    p_city VARCHAR,
    p_state VARCHAR,
    p_postal_code VARCHAR,
    p_country VARCHAR
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB;
BEGIN
    -- Update the institution record
    UPDATE institutions
    SET 
        name = COALESCE(p_name, name),
        website_url = COALESCE(p_website_url, website_url),
        email = COALESCE(p_email, email), -- This is the key fix - ensure email is updated properly
        contact_phone = COALESCE(p_contact_phone, contact_phone),
        address = COALESCE(p_address, address),
        city = COALESCE(p_city, city),
        state = COALESCE(p_state, state),
        postal_code = COALESCE(p_postal_code, postal_code),
        country = COALESCE(p_country, country),
        updated_at = NOW()
    WHERE id = p_institution_id
    RETURNING jsonb_build_object(
        'id', id,
        'name', name,
        'websiteUrl', website_url,
        'email', email, -- Make sure email is included in the response
        'contactPhone', contact_phone,
        'address', address,
        'city', city,
        'state', state,
        'postalCode', postal_code,
        'country', country
    ) INTO v_result;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- 4. Create a view that can be used by the application to ensure email is properly handled
CREATE OR REPLACE VIEW institution_profile_view AS
SELECT 
    id,
    name,
    website_url AS "websiteUrl",
    email,
    contact_phone AS "contactPhone",
    address,
    city,
    state,
    postal_code AS "postalCode",
    country
FROM institutions;

-- 5. Check if there's a hardcoded email value in the institutions table
-- This will help identify if there's a default value being used
SELECT id, email FROM institutions WHERE email = '<EMAIL>';

-- 6. Update any hardcoded email values if found
UPDATE institutions 
SET email = '<EMAIL>' 
WHERE email = '<EMAIL>';

-- 7. Add a comment to explain the fix
COMMENT ON FUNCTION update_institution_profile IS 
'This function properly handles institution profile updates including the email field.
It was created to fix an issue where the email field was being ignored or overwritten during updates.';
