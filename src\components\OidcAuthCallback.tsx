import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { toast } from 'react-toastify';
import { clearOidcState, isStateMismatchError, handleStateMismatchError } from '../utils/authUtils';

export function OidcAuthCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('Processing OIDC callback...');
        console.log('Current URL:', window.location.href);
        console.log('Auth state:', {
          isLoading: auth.isLoading,
          isAuthenticated: auth.isAuthenticated,
          hasUser: !!auth.user,
          error: auth.error
        });

        // Check if there's an error in the URL
        const params = new URLSearchParams(location.search);
        const urlError = params.get('error');
        const errorDescription = params.get('error_description');

        if (urlError) {
          console.error('Authentication error from URL:', urlError, errorDescription);
          setError(errorDescription || 'Authentication failed');
          setTimeout(() => navigate('/institution-login'), 3000);
          return;
        }

        // Handle OIDC authentication errors
        if (auth.error) {
          console.error('OIDC authentication error:', auth.error);
          
          // Handle specific state mismatch error
          if (isStateMismatchError(auth.error)) {
            console.log('State mismatch detected, handling gracefully');
            handleStateMismatchError();
            return;
          }
          
          setError(auth.error.message || 'Authentication failed');
          setTimeout(() => navigate('/institution-login'), 3000);
          return;
        }

        // Wait for OIDC to process the callback
        if (auth.isLoading) {
          console.log('OIDC is still processing...');
          return;
        }

        if (auth.isAuthenticated && auth.user) {
          console.log('OIDC authentication successful!');
          console.log('User:', auth.user);
          console.log('Redirecting to dashboard...');
          navigate('/institution-dashboard');
        } else {
          console.log('OIDC authentication not yet complete, waiting...');
        }

      } catch (error) {
        console.error('Error processing callback:', error);
        setError('Failed to process authentication response');
        setTimeout(() => navigate('/institution-login'), 3000);
      } finally {
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [auth.isLoading, auth.isAuthenticated, auth.user, auth.error, location, navigate]);



  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">
            {error ? 'Authentication Error' : 'Signing you in...'}
          </h2>

          {isProcessing && !error && (
            <div className="flex justify-center my-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          )}

          {error && (
            <div className="text-red-500 mb-4">
              {error}
            </div>
          )}

          <p className="text-gray-600">
            {error
              ? 'Redirecting you back to the login page...'
              : 'Please wait while we complete the authentication process.'}
          </p>
          
          {error && error.includes('session expired') && (
            <button
              onClick={() => navigate('/institution-login')}
              className="mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

export default OidcAuthCallback;
