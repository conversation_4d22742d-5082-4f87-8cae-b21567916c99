import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Save, Info, Shield } from 'lucide-react';
import { toast } from 'react-toastify';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material';
import { Theme, useTheme } from '@mui/material/styles';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import institutionService from '../services/institutionService';
import { useAuth } from 'react-oidc-context';
import StatusPage from '../components/StatusPage';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';

interface UserFormData {
  name: string;
  email: string;
  role: string[];
  department: string;
}

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

function getStyles(name: string, selectedItems: readonly string[], theme: Theme) {
  return {
    fontWeight:
      selectedItems.indexOf(name) === -1
        ? theme.typography.fontWeightRegular
        : theme.typography.fontWeightMedium,
  };
}

const roles = [
  { value: 'INSTITUTION_ADMIN', label: 'Institution Admin' },
  { value: 'FINANCIAL_ADMIN', label: 'Financial Admin' },
  { value: 'CLERK', label: 'Clerk' },
];

export function AddUserPage() {
  const navigate = useNavigate();
  const theme = useTheme();
  const auth = useAuth();

  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    role: [],
    department: '',
  });
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
const [sidebarOpen, setSidebarOpen] = useState(true);


  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = async () => {
    try {
      console.log("Starting institution sign out...");

      

      // Clear any OIDC state from storage
      Object.keys(sessionStorage).forEach((key) => {
        if (key.startsWith("oidc.")) {
          sessionStorage.removeItem(key);
        }
      });

      // Clear any other auth-related storage
      localStorage.removeItem("id_token");
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("id_token");
      sessionStorage.removeItem("token");
      sessionStorage.removeItem("user");

     

      console.log("Local state and storage cleared");

      // Use custom Cognito logout instead of OIDC signout
      try {
        // Build the correct Cognito logout URL
        const cognitoDomain = import.meta.env.VITE_COGNITO_DOMAIN;
        const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
        const logoutUri = `${window.location.origin}${import.meta.env.VITE_BASE_PATH}/`;

        const logoutUrl = `${cognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(
          logoutUri
        )}`;

        console.log("Redirecting to Cognito logout URL:", logoutUrl);
        toast.success("Successfully signed out");

        // Redirect to Cognito logout
        window.location.href = logoutUrl;
      } catch (logoutError) {
        console.warn(
          "Custom logout failed, using direct redirect:",
          logoutError
        );

        // Fallback: Direct redirect to home page
        toast.success("Successfully signed out");
        window.location.href = "${import.meta.env.VITE_BASE_PATH}/";
      }
    } catch (error) {
      console.error("Error during sign out process:", error);
        setStatus('error');
        setErrorDetails({ message: 'Sign out completed with warnings' });

        // Always redirect to home page as fallback
        window.location.href = "${import.meta.env.VITE_BASE_PATH}/";
      }
    };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    if (name === 'role') return;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleChange = (event: SelectChangeEvent<typeof formData.role>) => {
    const { value } = event.target;
    const selectedRoles = typeof value === 'string' ? value.split(',') : value;
    setFormData((prev) => ({
      ...prev,
      role: selectedRoles,
    }));
  };

  const handleDeleteRole = (roleToDelete: string) => {
    setFormData((prev) => ({
      ...prev,
      role: prev.role.filter((role) => role !== roleToDelete),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (
        !formData.name ||
        !formData.email ||
        formData.role.length === 0 ||
        !formData.department
      ) {
        setErrorDetails({ message: 'Please fill in all required fields' });
        setStatus('error');
        return;
      }

      const nameParts = formData.name.trim().split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(' ') || '-';

      await institutionService.inviteInstitutionUser(
        {
          email: formData.email,
          firstName,
          lastName,
          roles: formData.role,
        },
        auth
      );

      setStatus('success');
    } catch (error: any) {
      console.error('Invite error:', error);
      const apiMessage = error.response?.data?.message || '';
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join('\n');
        errorMessage = apiMessage + '\n\n' + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      setStatus('error');
    }
  };

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title="User Invited Successfully"
        message={`Invitation email has been sent to ${formData.email}. They will receive instructions to set up their account.`}
        actionText="Back to Settings"
        backUrl="/institution-dashboard?tab=settings"
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Retry"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="settings"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-50 py-8">
          <div className="max-w-3xl mx-auto px-4">
          <div className="mb-6">
            <button
              onClick={() => navigate('/institution-dashboard?tab=settings')}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Settings
            </button>
          </div>

          <Breadcrumb items={getBreadcrumbItems('add-user')} />

          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold mb-6">Add New User</h1>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter user's full name"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter user's email address"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <FormControl required fullWidth sx={{ mb: 1 }}>
                    <InputLabel id="role-label">Role</InputLabel>
                    <Select
                      labelId="role-label"
                      id="role"
                      multiple
                      value={formData.role}
                      onChange={handleRoleChange}
                      input={<OutlinedInput id="select-multiple-chip" label="Role" />}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => {
                            const role = roles.find((r) => r.value === value);
                            return (
                              <Chip
                                key={value}
                                label={role ? role.label : value}
                                onDelete={(event) => {
                                  event.stopPropagation();
                                  handleDeleteRole(value);
                                }}
                                onMouseDown={(event) => event.stopPropagation()}
                              />
                            );
                          })}
                        </Box>
                      )}
                      MenuProps={MenuProps}
                    >
                      {roles.map((role) => (
                        <MenuItem
                          key={role.value}
                          value={role.value}
                          style={getStyles(role.value, formData.role, theme)}
                        >
                          {role.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>

                <div>
                  <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                    Department <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="department"
                    name="department"
                    value={formData.department}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g. Finance, Admissions, etc."
                  />
                </div>
              </div>

              <div className="border-t border-b py-4 my-4">
                <div className="flex items-center mb-4">
                  <Shield className="h-5 w-5 text-blue-600 mr-2" />
                  <h3 className="text-lg font-medium">Permissions</h3>
                </div>

                <div className="bg-blue-50 p-4 rounded-md mb-4 text-sm">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-800">Permission Levels</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                        {[
                          { name: 'Manage Campaigns', granted: formData.role.includes('INSTITUTION_ADMIN') || formData.role.includes('FINANCIAL_ADMIN') },
                          { name: 'Manage Approvals', granted: formData.role.includes('INSTITUTION_ADMIN') || formData.role.includes('FINANCIAL_ADMIN') },
                          { name: 'Manage Institution Profile', granted: formData.role.includes('INSTITUTION_ADMIN') },
                          { name: 'Manage Bank Details', granted: formData.role.includes('INSTITUTION_ADMIN') || formData.role.includes('FINANCIAL_ADMIN') },
                          { name: 'Manage Users', granted: formData.role.includes('INSTITUTION_ADMIN') },
                          { name: 'Download Reports', granted: formData.role.includes('INSTITUTION_ADMIN') || formData.role.includes('FINANCIAL_ADMIN') },
                        ].map((permission, index) => (
                          <div key={index} className="flex items-center">
                            <div className={`w-4 h-4 rounded-full mr-2 ${permission.granted ? 'bg-green-500' : 'bg-gray-300'}`} />
                            <span className={permission.granted ? 'text-blue-800' : 'text-gray-500'}>
                              {permission.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-2 text-sm text-gray-600">
                  <p>Permissions are automatically assigned based on the selected role(s). Select multiple roles to combine permissions.</p>
                </div>
              </div>

              <div className="border-b pb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Account Setup</h3>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Info className="h-5 w-5 text-blue-600 mr-2" />
                    <p className="text-sm text-gray-700">
                      An invitation email will be sent to the user with instructions to set up their account
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
                  onClick={() => navigate('/institution-dashboard?tab=settings')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Add User
                </button>
              </div>
            </form>
          </div>
          </div>
        </div>
      </div>
      
     
    </div>
  );
}

export default AddUserPage;
