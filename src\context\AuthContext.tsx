import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { signOut, getCurrentUser, fetchAuthSession } from 'aws-amplify/auth';
import authService, { LoginRequest, LoginResponse } from '../services/authService';
import cognitoService from '../services/cognitoService';

export type UserType = 'institution' | 'student' | 'supporter' | 'admin' | null;

interface User {
  id: any;
  name: string;
  email: string;
  userType: string;
  profileId?: number | string; // Updated to support UUID-style profile IDs
  isCognitoUser?: boolean;
}

interface AuthContextType {
  userType: UserType;
  isAuthenticated: boolean;
  user: User | null;
  login: (type: UserType, credentials: LoginRequest, rememberMe?: boolean) => Promise<LoginResponse>;
  loginWithCognito: (rememberMe?: boolean) => void;
  setUserFromCognito: (cognitoUser: any) => void;
  logout: (redirectPath?: string) => void;
  loading: boolean;
  idToken: string | null; // ✅ Add this line
}


const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [userType, setUserType] = useState<UserType>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [idToken, setIdToken] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const currentUser = await getCurrentUser();
      const session = await fetchAuthSession();
      const token = session.tokens?.idToken?.toString();
      setIdToken(token || null); // ✅ This is fine

if (token) {
  localStorage.setItem('id_token', token); // ✅ Optional: avoid storing if already in memory
}
      const userData: User = {
        id: currentUser.userId,
        name: currentUser.username,
        email: currentUser.signInDetails?.loginId || '',
        userType: 'institution'
      };
      
      setUser(userData);
      setUserType('institution');
      setLoading(false);
      setIdToken(token || null);
      if (token) {
        localStorage.setItem('id_token', token);
      }
    } catch (error) {
      setUser(null);
      setUserType(null);
      setLoading(false);
      setIdToken(null);
      localStorage.removeItem('id_token');
    }
  };

  // Determine authentication status from multiple sources
  const isCognitoAuthenticated = cognitoService.isAuthenticated();
  const hasCognitoTokens = !!(localStorage.getItem('id_token') || sessionStorage.getItem('id_token'));
  const hasAccessToken = !!(localStorage.getItem('access_token') || sessionStorage.getItem('access_token'));
  const justAuthenticated = sessionStorage.getItem('just_authenticated') === 'true';

  // Consider authenticated if we have a user object or Cognito tokens
  // Also consider authenticated if we just completed the authentication process
  const isAuthenticated = user !== null || isCognitoAuthenticated || hasCognitoTokens || hasAccessToken || justAuthenticated;

  // Log authentication state for debugging
  useEffect(() => {
    console.log('AuthContext state:', {
      userType,
      hasUser: user !== null,
      isAuthenticated,
      isCognitoAuthenticated,
      hasCognitoTokens,
      hasAccessToken,
      justAuthenticated,
      hasIdToken: !!(localStorage.getItem('id_token') || sessionStorage.getItem('id_token')),
      idTokenInLocalStorage: localStorage.getItem('id_token') ? 'present' : 'missing',
      idTokenInSessionStorage: sessionStorage.getItem('id_token') ? 'present' : 'missing'
    });

    // Clear the just_authenticated flag after a short delay
    if (justAuthenticated) {
      const timer = setTimeout(() => {
        console.log('Clearing just_authenticated flag');
        sessionStorage.removeItem('just_authenticated');
      }, 5000); // Clear after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [user, userType, isAuthenticated, isCognitoAuthenticated, hasCognitoTokens, hasAccessToken, justAuthenticated]);

  // Regular login with email/password
  const login = async (type: UserType, credentials: LoginRequest, rememberMe = false): Promise<LoginResponse> => {
    try {
      setLoading(true);

      // Use the appropriate login endpoint based on user type
      let response;
      if (type === 'institution') {
        // Try to use institution-specific endpoint if available
        try {
          response = await authService.loginInstitution(credentials);
        } catch (error) {
          // Fall back to generic login if institution-specific endpoint fails
          console.warn('Institution-specific login failed, falling back to generic login');
          response = await authService.login(credentials);
        }
      } else {
        response = await authService.login(credentials);
      }

      // Save token and user data based on rememberMe preference
      authService.saveToken(response.token, rememberMe);

      const userData = {
        id: response.id,
        name: response.name,
        email: response.email,
        userType: response.userType,
        profileId: response.profileId
      };

      authService.saveUser(userData, rememberMe);

      // Update state
      setUser(userData);
      setUserType(type);

      return response;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Login with Cognito (redirects to Cognito hosted UI)
  const loginWithCognito = (rememberMe = true) => {
    // Generate a state parameter for CSRF protection
    const state = rememberMe ? 'remember-me' : '';
    cognitoService.redirectToLogin(state);
  };

  // Set user from Cognito token data
 const setUserFromCognito = (cognitoUser: any) => {
  if (!cognitoUser) return;

  const userData = {
    id: cognitoUser.sub,
    name: cognitoUser.name || `${cognitoUser.given_name || ''} ${cognitoUser.family_name || ''}`.trim(),
    email: cognitoUser.email,
    userType: 'institution',
    isCognitoUser: true
  };

  setUser(userData);
  setUserType('institution');

  // Optional: you may also pass token separately and store here
  const token = localStorage.getItem('id_token') || sessionStorage.getItem('id_token');
  if (token) setIdToken(token);
};


  // Logout - handles both regular and Cognito logout
  // The redirectPath parameter allows specifying where to redirect after logout
  const logout = async (redirectPath = '/') => {
    try {
      await signOut();
      setUser(null);
      setUserType(null);
      setIdToken(null);
      localStorage.removeItem('id_token');
      navigate(redirectPath);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      userType,
      isAuthenticated,
      user,
      login,
      loginWithCognito,
      setUserFromCognito,
      logout,
      loading,
      idToken
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}





