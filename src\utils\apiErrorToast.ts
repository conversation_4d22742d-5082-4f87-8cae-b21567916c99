import axios, { AxiosError } from 'axios';
import { toast } from 'react-toastify';

/**
 * Display a formatted error message using toast.error.
 * Handles Axios errors and falls back to a generic message.
 */
export default function apiErrorToast(error: unknown, fallback = 'An unexpected error occurred') {
  let message = fallback;

  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<any>;
    if (axiosError.response?.data) {
      const data = axiosError.response.data;
      if (typeof data === 'string') {
        message = data;
      } else if (typeof data.message === 'string') {
        message = data.message;
      } else if (typeof data.error === 'string') {
        message = data.error;
      }
    } else if (axiosError.message) {
      message = axiosError.message;
    }
  } else if (error instanceof Error) {
    message = error.message;
  }

  toast.error(message);
}
