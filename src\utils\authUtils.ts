/**
 * Authentication utilities for handling OAuth2 state management
 * and password reset flows
 */

/**
 * Clears all OIDC-related state from storage
 */
export const clearOidcState = (): void => {
  console.log('Clearing all OIDC state...');
  
  // Clear from both sessionStorage and localStorage
  [sessionStorage, localStorage].forEach(storage => {
    Object.keys(storage).forEach(key => {
      if (key.startsWith('oidc.')) {
        storage.removeItem(key);
      }
    });
  });
  
  console.log('OIDC state cleared successfully');
};

/**
 * Generates a secure state parameter for OAuth2 flows
 */
export const generateSecureState = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Builds the Cognito forgot password URL for reinvited users
 */
export const buildForgotPasswordUrl = (email?: string): string => {
  const domain = import.meta.env.VITE_COGNITO_DOMAIN;
  const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
  const redirectUri = import.meta.env.VITE_COGNITO_REDIRECT_URI;
  
  const params = new URLSearchParams({
    client_id: clientId,
    response_type: 'code',
    redirect_uri: redirectUri,
  });
  
  if (email) {
    params.append('username', email);
  }
  
  return `${domain}/forgotPassword?${params.toString()}`;
};

/**
 * Handles post-password-reset redirect to ensure clean state
 */
export const handlePasswordResetComplete = (): void => {
  console.log('Handling password reset completion...');
  
  // Clear any existing OIDC state
  clearOidcState();
  
  // Set a flag to indicate password reset was completed
  sessionStorage.setItem('password_reset_completed', 'true');
  
  // Redirect to login page with success message
  const loginUrl = '${import.meta.env.VITE_BASE_PATH}/institution-login?password_reset_complete=true';
  window.location.href = loginUrl;
};

/**
 * Checks if the current error is a state mismatch error
 */
export const isStateMismatchError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message || error.toString();
  return errorMessage.includes('No matching state found') || 
         errorMessage.includes('state mismatch') ||
         errorMessage.includes('Invalid state');
};

/**
 * Handles state mismatch errors by clearing state and redirecting
 */
export const handleStateMismatchError = (): void => {
  console.log('Handling state mismatch error...');
  
  clearOidcState();
  
  // Redirect to login with error message
  const loginUrl = '${import.meta.env.VITE_BASE_PATH}/institution-login?error=session_expired';
  window.location.href = loginUrl;
};

/**
 * Initializes a fresh OAuth2 login flow
 */
export const initiateFreshLogin = async (authContext: any): Promise<void> => {
  console.log('Initiating fresh login flow...');
  
  try {
    // Clear any existing state
    clearOidcState();
    
    // Wait a moment for state to clear
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Start new login flow
    await authContext.signinRedirect();
  } catch (error) {
    console.error('Failed to initiate fresh login:', error);
    throw error;
  }
};