# Edu Fund UI

This is the frontend application for the Edu Fund platform, which connects students, educational institutions, and supporters for educational fundraising campaigns.

## Project Structure

```
project/
├── src/
│   ├── components/       # Reusable UI components
│   ├── config/           # Configuration files
│   ├── context/          # React context providers
│   ├── hooks/            # Custom React hooks
│   ├── pages/            # Page components
│   ├── services/         # API services
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── App.tsx           # Main application component
│   ├── index.ts          # Central export file
│   ├── index.css         # Global styles
│   └── main.tsx          # Application entry point
├── .env                  # Environment variables
├── index.html            # HTML entry point
└── vite.config.ts        # Vite configuration
```

## Using the Index File

The project now includes a central `index.ts` file that exports all major components, contexts, pages, services, and utilities. This makes importing components throughout the application much easier.

### Example Usage

Instead of:
```typescript
import { Navbar } from './components/Navbar';
import { Footer } from './components/Footer';
import { HomePage } from './pages/HomePage';
import { authService } from './services/authService';
```

You can now use:
```typescript
import { Navbar, Footer, HomePage, authService } from './index';
```

## Authentication

The application supports multiple authentication methods:
- Institution users: Cognito authentication
- Students: Custom authentication
- Supporters: Custom authentication
- Social login: Google OAuth

## Routes

- `/`: Homepage
- `/auth`: Authentication page
- `/institution-dashboard`: Institution dashboard (protected)
- `/students`: Student dashboard (protected)
- `/supporters`: Supporter dashboard (protected)
- `/campaign/:id`: Campaign details page
- `/student/campaign/:id`: Student campaign details page (protected)
- `/institution/campaign/:id`: Institution campaign details page (protected)

## Development

1. Install dependencies:
```
npm install
```

2. Run the development server:
```
npm run dev
```

3. Build for production:
```
npm run build
```