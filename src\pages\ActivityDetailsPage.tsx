import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { activityService, type Activity } from '../services';
import StatusPage from '../components/StatusPage';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from '../components/InstitutionHeader';
import { InstitutionFooter } from '../components/InstitutionFooter';
import cognitoService from '../services/cognitoService';
import { toast } from 'react-toastify';

export function ActivityDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const auth = useAuth();
  const [activity, setActivity] = useState<{
    title: string;
    performedBy: string;
    actionType: string;
    timestamp: string;
    detailFields: Array<{ key: string; value: string }>;
    linkToEntity?: string;
    sensitive: boolean;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');
    toast.success('Successfully signed out');
    cognitoService.redirectToLogout();
  };

  useEffect(() => {
    const load = async () => {
      if (!id) {
        setError('No activity id provided');
        setLoading(false);
        return;
      }
      try {
        const data = await activityService.getActivityDetail(id, auth);
        setActivity(data);
      } catch (err) {
        console.error('Failed to load activity detail', err);
        setError('Failed to load activity');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [id]);

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  if (error) {
    return <StatusPage type="error" title="Error" message={error} />;
  }

  if (!activity) {
    return <div className="p-4">No activity found</div>;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="dashboard"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-50">

        <Breadcrumb items={getBreadcrumbItems('activity-details')} />

        <div className="bg-white rounded-lg shadow p-4 md:p-8">
          

          <h1 className="text-2xl font-bold mb-6">{activity.title}</h1>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <span className="font-semibold text-gray-600">Performed By:</span>
              <p className="text-gray-800">{activity.performedBy}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Action Type:</span>
              <p className="text-gray-800">{activity.actionType}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Timestamp:</span>
              <p className="text-gray-800">{new Date(activity.timestamp).toLocaleString()}</p>
            </div>
           
          </div>

          {activity.detailFields && activity.detailFields.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Details</h2>
              <div className="bg-gray-50 border rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  {activity.detailFields.map((field, index) => (
    <div key={index}>
      <span className="font-medium text-gray-600">
        {field.key === 'Entity Type' ? 'Detail Type' : field.key}:
      </span>
      <p className="text-gray-800">{field.value}</p>
    </div>
  ))}
</div>

              </div>
            </div>
          )}

          {activity.sensitive && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm font-medium">⚠️ This activity contains sensitive information</p>
            </div>
          )}

        </div>
        </div>
      </div>
      
      <InstitutionFooter />
    </div>
  );
}

export default ActivityDetailsPage;


