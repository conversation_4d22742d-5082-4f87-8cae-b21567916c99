Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF5810, 0007FFFF4710) msys-2.0.dll+0x1FEBA
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210285FF9, 0007FFFF56C8, 0007FFFF5810, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF5810  0002100690B4 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF5AF0  00021006A49D (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCDAD10000 ntdll.dll
7FFCDAAD0000 KERNEL32.DLL
7FFCD8750000 KERNELBASE.dll
7FFCD8D30000 USER32.dll
7FFCD84C0000 win32u.dll
7FFCD9E70000 GDI32.dll
7FFCD83A0000 gdi32full.dll
7FFCD8680000 msvcp_win.dll
7FFCD84F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCDA040000 advapi32.dll
7FFCD9790000 msvcrt.dll
7FFCD8EF0000 sechost.dll
7FFCDA920000 RPCRT4.dll
7FFCD8720000 bcrypt.dll
7FFCD75A0000 CRYPTBASE.DLL
7FFCD85F0000 bcryptPrimitives.dll
7FFCDA8F0000 IMM32.DLL
