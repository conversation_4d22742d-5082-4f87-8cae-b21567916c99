{"name": "edu-fund-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest run"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.808.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@react-oauth/google": "^0.12.2", "@supabase/supabase-js": "^2.39.7", "@types/react-tabs": "^5.0.5", "@types/uuid": "^10.0.0", "amazon-cognito-identity-js": "^6.3.15", "aws-amplify": "^6.14.4", "axios": "^1.8.4", "emailjs-com": "^3.2.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "oidc-client-ts": "^3.2.1", "papaparse": "^5.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-oidc-context": "^3.3.0", "react-qr-code": "^2.0.12", "react-router-dom": "^6.22.2", "react-tabs": "^6.1.0", "react-toastify": "^11.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-toastify": "^4.1.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.0", "@testing-library/react": "^14.0.0"}}