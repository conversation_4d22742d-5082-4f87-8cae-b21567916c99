import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { ChevronLeft, X } from 'lucide-react';
import { toast } from 'react-toastify';
import { useAuth as useOidcAuth } from 'react-oidc-context';
import StatusPage from '../components/StatusPage';
import institutionService from '../services/institutionService';
import approvalService from '../services/approvalService';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import InstitutionSidebar from '../components/InstitutionSidebar';
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';

export function CampaignRejectionPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const auth = useOidcAuth();
  const [campaign, setCampaign] = useState<any>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);
    const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
      const handleSignOut = async () => {
        try {
          console.log("Starting institution sign out...");
    
          
    
          // Clear any OIDC state from storage
          Object.keys(sessionStorage).forEach((key) => {
            if (key.startsWith("oidc.")) {
              sessionStorage.removeItem(key);
            }
          });
    
          // Clear any other auth-related storage
          localStorage.removeItem("id_token");
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          sessionStorage.removeItem("id_token");
          sessionStorage.removeItem("token");
          sessionStorage.removeItem("user");
    
         
    
          console.log("Local state and storage cleared");
    
          // Use custom Cognito logout instead of OIDC signout
          try {
            // Build the correct Cognito logout URL
            const cognitoDomain = import.meta.env.VITE_COGNITO_DOMAIN;
            const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
            const logoutUri = `${window.location.origin}${import.meta.env.VITE_BASE_PATH}/`;
    
            const logoutUrl = `${cognitoDomain}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(
              logoutUri
            )}`;
    
            console.log("Redirecting to Cognito logout URL:", logoutUrl);
            toast.success("Successfully signed out");
    
            // Redirect to Cognito logout
            window.location.href = logoutUrl;
          } catch (logoutError) {
            console.warn(
              "Custom logout failed, using direct redirect:",
              logoutError
            );
    
            // Fallback: Direct redirect to home page
            toast.success("Successfully signed out");
            window.location.href = "${import.meta.env.VITE_BASE_PATH}/";
          }
          } catch (error) {
            console.error("Error during sign out process:", error);
            setErrorDetails({ message: "Failed to sign out. Please try again." });
            setStatus("error");
          }
      };
      

  useEffect(() => {
    const fetchCampaign = async () => {
      if (!id) return;
      try {
        const campaignData = await institutionService.getCampaignById(id, auth);
        setCampaign(campaignData);
      } catch (error) {
        console.error('Error fetching campaign:', error);
        setErrorDetails({ message: 'Failed to load campaign details' });
        setStatus('error');
      }
    };
    fetchCampaign();
  }, [id, auth, navigate]);

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    if (!id || !campaign) return;

    try {
      setIsLoading(true);
      
      await approvalService.updateApprovalRequest(id, {
        id: id,
        status: 'REJECTED',
        reason: rejectionReason.trim()
      }, auth);

      toast.success('Campaign rejected successfully');
      navigate(`/institution/campaign/${id}`);
      
    } catch (error) {
      console.error('Error rejecting campaign:', error);
      setErrorDetails({ message: 'Failed to reject campaign. Please try again.' });
      setStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

 return (
  <div className="min-h-screen flex flex-col">
    <InstitutionHeader />
    
    <div className="flex flex-1">
      <InstitutionSidebar
        sidebarOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        activeTab="approvalRequests"
        onSignOut={handleSignOut}
      />
      
      <div className="flex-1 bg-gray-50 py-8">
        <div className="max-w-2xl mx-auto px-4">
        <div className="mb-6">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ChevronLeft className="h-5 w-5 mr-1" />
            Back
          </button>
          <Breadcrumb
            items={getBreadcrumbItems('campaign-rejection', {
              campaignTitle: campaign?.title || 'Campaign',
              campaignId: id,
              source: 'approvals'
            })}
          />
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
              <X className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Confirm Rejection</h1>
            <p className="text-gray-600">
              Are you sure you want to reject the request from{' '}
              <span className="font-semibold">{campaign?.student?.firstName} {campaign?.student?.lastName}</span>?
            </p>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Rejection
            </label>
            <textarea
              className="w-full border rounded-lg px-3 py-2"
              placeholder="Enter reason for rejection..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
            />
            {rejectionReason.trim() === '' && (
              <p className="text-sm text-red-500 mt-1">
                Please provide a reason for rejection
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleReject}
              disabled={isLoading || rejectionReason.trim() === ''}
              className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {isLoading ? 'Rejecting...' : 'Reject Campaign'}
            </button>
          </div>
        </div>
        </div>
      </div>
    </div>
    
   
  </div>
);

}

export default CampaignRejectionPage;