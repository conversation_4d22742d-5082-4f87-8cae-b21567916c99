import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronLeft, Plus, Save, X } from "lucide-react";
import { toast } from "react-toastify";
import api from "../services/api";
import { useAuth } from "react-oidc-context";
import cognitoService from "../services/cognitoService";
import Breadcrumb from "../components/Breadcrumb";
import { getBreadcrumbItems } from "../utils/breadcrumbUtils";
import campaignService from "../services/campaignService";
import StatusPage from "../components/StatusPage";
import InstitutionSidebar from "../components/InstitutionSidebar";
import InstitutionHeader from "../components/InstitutionHeader";
import { InstitutionFooter } from '../components/InstitutionFooter';

type CampaignCategory =
  | "Tuition"
  | "Books"
  | "Accommodation"
  | "Research"
  | "Other";

interface CreateCampaignFormData {
  studentRegId: string;
  campaignTitle: string;
  description: string;
  targetAmount: string;
  startDate: string;
  endDate: string;
  category: CampaignCategory;
  documents: File[];
  milestones: {
    title: string;
    dueDate: string;
  }[];
  // Student details fields
  studentFirstName: string;
  studentLastName: string;

  studentEmail: string;
  studentPhone: string;
  studentInstitute: string;
  studentCourse: string;
  studentYear: string;
  studentBatch: string;
  studentAcademicRecord: string;
  studentDepartment: string;
  studentStory: string;
  // Privacy settings
  showSupportersToOthers: boolean;
}

export function CreateCampaignPage() {
  const navigate = useNavigate();
  const auth = useAuth();
  const { user } = useAuth();

  // Add this effect to check authentication before rendering
  useEffect(() => {
    // Check if user is authenticated
    if (!auth.isAuthenticated) {
      toast.error("Please log in to create a campaign");
      navigate("/institution-login");
    }

    // Check if token exists in localStorage
    const token = localStorage.getItem("id_token");
    if (!token) {
      console.warn("No ID token found in localStorage");
      // If user is authenticated but no token, try to get it from auth
      if (auth.isAuthenticated && auth.user?.id_token) {
        localStorage.setItem("id_token", auth.user.id_token);
        console.log("ID token restored from auth context");
      } else {
        // toast.error('Authentication error. Please log in again.');
        navigate("/institution-login");
      }
    }
  }, [auth.isAuthenticated, navigate]);

  const [emailError, setEmailError] = useState<string | null>(null);
  const [status, setStatus] = useState<"idle" | "success" | "error">("idle");
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(
    null
  );

  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleSignOut = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("id_token");
    sessionStorage.removeItem("token");
    sessionStorage.removeItem("user");
    sessionStorage.removeItem("id_token");
    toast.success("Successfully signed out");
    cognitoService.redirectToLogout();
  };

  const [formData, setFormData] = useState<CreateCampaignFormData>({
    studentRegId: "",
    campaignTitle: "",
    description: "",
    targetAmount: "",
    startDate: "",
    endDate: "",
    category: "Tuition",
    documents: [],
    milestones: [{ title: "", dueDate: "" }],
    // Initialize student details fields
    studentFirstName: "",
    studentLastName: "",

    studentEmail: "",
    studentPhone: "",
    studentInstitute: "",
    studentCourse: "",
    studentYear: "",
    studentBatch: "",
    studentAcademicRecord: "",
    studentDepartment: "",
    studentStory: "",
    // Privacy settings
    showSupportersToOthers: true,
  });

  useEffect(() => {
    // Using hardcoded institution ID as requested
    // No need to fetch from API since we're using a hardcoded value
    // This ensures we always have an institution ID available
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
    field: keyof CreateCampaignFormData
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted");

    const loadingToast = toast.loading("Creating campaign...");

    if (!formData.studentEmail.endsWith("@gmail.com")) {
      toast.update(loadingToast, {
        render: "Only Gmail addresses are allowed.",
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
      return;
    }

    try {
      const startDate = new Date(formData.startDate);
      const endDateObj = new Date(formData.endDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      let endDateToUse = endDateObj;
      if (endDateObj <= today) {
        endDateToUse = new Date();
        endDateToUse.setDate(today.getDate() + 1);
        console.log(
          "End date adjusted to future:",
          endDateToUse.toISOString().split("T")[0]
        );
      }

      // Validate required fields
      if (!formData.studentRegId || !formData.studentEmail) {
        toast.update(loadingToast, {
          render: "Missing student ID or email",
          type: "error",
          isLoading: false,
          autoClose: 3000,
        });
        return;
      }

      const firstName = formData.studentFirstName.trim();
      const lastName = formData.studentLastName.trim();

      const campaignPayload = {
        studentFirstName: firstName,
        studentLastName: lastName,
        studentName: `${firstName} ${lastName}`.trim(),

        studentPhoneNumber: formData.studentPhone,
        studentBatch: formData.studentBatch,
        studentCourse: formData.studentCourse,
        studentDepartment: formData.studentDepartment,
        studentYear: formData.studentYear,
        studentRegId: formData.studentRegId,
        studentEmail: formData.studentEmail,
        title: formData.campaignTitle,
        description: formData.description,
        goalAmount: parseFloat(formData.targetAmount),
        startDate: startDate.toISOString().split("T")[0],
        endDate: endDateToUse.toISOString().split("T")[0],
        showSupportersToOthers: formData.showSupportersToOthers,
      };

      // Use the authenticated API instance directly
      const response = await api.post(
        "/api/institution/v1/campaigns",
        campaignPayload,
        {
          headers: {
            "Content-Type": "application/json",
            // The Authorization header will be added by the interceptor
          },
        }
      );

      console.log("Campaign creation response:", response);

      if (formData.documents.length > 0) {
        console.log("Files to be uploaded:", formData.documents);
        // implement file upload if needed
      }

      toast.dismiss(loadingToast);
      setStatus("success");
    } catch (error: any) {
      console.error("Error creating campaign:", error);
      const apiMessage = error.response?.data?.message || "";
      const apiData = error.response?.data?.data || {};
      let errorMessage = apiMessage;
      if (Object.keys(apiData).length > 0) {
        const validationErrors = Object.entries(apiData)
          .map(([field, error]) => `• ${field}: ${error}`)
          .join("\n");
        errorMessage = apiMessage + "\n\n" + validationErrors;
      }
      setErrorDetails({ message: errorMessage });
      toast.dismiss(loadingToast);
      setStatus("error");
    }
  };

  // Add a debug function to log form submission
  const debugSubmit = (e: React.FormEvent) => {
    console.log("Debug: Form submit button clicked");
    handleSubmit(e);
  };

  const formatWithCommas = (value: number | string) => {
    if (!value) return "";
    return Number(value).toLocaleString("en-IN"); // You can also use 'en-US' if needed
  };

  const handleFormattedChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: string
  ) => {
    const rawValue = e.target.value.replace(/,/g, "");
    const numericValue = Number(rawValue);

    if (isNaN(numericValue) || numericValue < 0) return;

    setFormData({ ...formData, [field]: numericValue });
  };

  if (status === "success") {
    return (
      <StatusPage
        type="success"
        title="Campaign Created Successfully"
        message={`Your campaign "${formData.campaignTitle}" has been created successfully.`}
        actionText="View Campaigns"
        backUrl="/institution-dashboard?tab=campaigns"
      />
    );
  }

  if (status === "error") {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus("idle");
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <InstitutionHeader />
      
      <div className="flex flex-1">
        <InstitutionSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="campaigns"
          onSignOut={handleSignOut}
        />
        
        <div className="flex-1 bg-gray-50 py-8">
          <div className="max-w-3xl mx-auto px-4">
          <div className="mb-6">
            <button
              onClick={() => navigate("/institution-dashboard?tab=campaigns")}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              Back to Campaigns
            </button>
          </div>

          {/* Breadcrumb */}
          <Breadcrumb items={getBreadcrumbItems("create-campaign")} />

          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-2xl font-bold mb-6">Create New Campaign</h1>

            <form onSubmit={debugSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Student Reg Id
                </label>
                <input
                  type="text"
                  value={formData.studentRegId}
                  onChange={(e) => handleChange(e, "studentRegId")}
                  className="w-full border rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign Title
                </label>
                <input
                  type="text"
                  value={formData.campaignTitle}
                  onChange={(e) => handleChange(e, "campaignTitle")}
                  className="w-full border rounded-lg px-3 py-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleChange(e, "description")}
                  className="w-full border rounded-lg px-3 py-2 h-32"
                  required
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Amount (₹)
                  </label>
                  <input
                    type="text"
                    inputMode="numeric"
                    value={formatWithCommas(formData.targetAmount)}
                    onChange={(e) => handleFormattedChange(e, "targetAmount")}
                    className="w-full border rounded-lg px-3 py-2"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleChange(e, "startDate")}
                    className="w-full border rounded-lg px-3 py-2"
                    min={new Date().toISOString().split("T")[0]} // Only allow today or future dates
                    max={formData.endDate || undefined} // Restrict start date not after end
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleChange(e, "endDate")}
                    className="w-full border rounded-lg px-3 py-2"
                    min={formData.startDate || undefined} // 👈 restrict end date not before start
                    required
                  />
                </div>
              </div>

              {/* Student Details Section */}
              <div className="mt-6 border-t pt-6">
                <h2 className="text-lg font-semibold mb-4">Student Details</h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <input
                        type="text"
                        value={formData.studentFirstName}
                        onChange={(e) => handleChange(e, "studentFirstName")}
                        className="w-full border rounded-lg px-3 py-2"
                        placeholder="Enter first name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <input
                        type="text"
                        value={formData.studentLastName}
                        onChange={(e) => handleChange(e, "studentLastName")}
                        className="w-full border rounded-lg px-3 py-2"
                        placeholder="Enter last name"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        value={formData.studentEmail}
                        onChange={(e) => {
                          handleChange(e, "studentEmail");
                          setEmailError(null); // clear error when typing
                        }}
                        onBlur={() => {
                          if (!formData.studentEmail.endsWith("@gmail.com")) {
                            setEmailError("Only Gmail addresses are allowed.");
                          }
                        }}
                        className="w-full border rounded-lg px-3 py-2"
                        placeholder="<EMAIL>"
                      />

                      {emailError ? (
                        <p className="text-sm text-red-600 mt-1">
                          {emailError}
                        </p>
                      ) : (
                        <p className="mt-1 text-xs text-blue-600">
                          Only Google email addresses are allowed
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="text"
                        value={formData.studentPhone}
                        onChange={(e) => handleChange(e, "studentPhone")}
                        className="w-full border rounded-lg px-3 py-2"
                        placeholder="e.g. 1234567890"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Batch Year Range
                    </label>
                    <div className="flex gap-3">
                      <select
                        value={formData.studentBatch?.split("-")[0] || ""}
                        onChange={(e) => {
                          const end =
                            formData.studentBatch?.split("-")[1] || "";
                          const newValue = `${e.target.value}-${end}`;
                          setFormData((prev) => ({
                            ...prev,
                            studentBatch: newValue,
                          }));
                        }}
                        className="w-full border rounded-lg px-3 py-2"
                      >
                        <option value="">Start Year</option>
                        {Array.from({ length: 10 }, (_, i) => {
                          const year = 2020 + i;
                          return (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          );
                        })}
                      </select>

                      <select
                        value={formData.studentBatch?.split("-")[1] || ""}
                        onChange={(e) => {
                          const start =
                            formData.studentBatch?.split("-")[0] || "";
                          const newValue = `${start}-${e.target.value}`;
                          setFormData((prev) => ({
                            ...prev,
                            studentBatch: newValue,
                          }));
                        }}
                        className="w-full border rounded-lg px-3 py-2"
                      >
                        <option value="">End Year</option>
                        {Array.from({ length: 10 }, (_, i) => {
                          const year = 2020 + i;
                          return (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Course
                      </label>
                      <input
                        list="course-options"
                        type="text"
                        value={formData.studentCourse}
                        onChange={(e) => handleChange(e, "studentCourse")}
                        className="w-full border rounded-lg px-3 py-2"
                        placeholder="Enter course"
                      />
                      <datalist id="course-options">
                        <option value="Computer Science" />
                        <option value="Engineering" />
                        <option value="Business" />
                      </datalist>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Year
                      </label>
                      <select
                        value={formData.studentYear}
                        onChange={(e) => handleChange(e, "studentYear")}
                        className="w-full border rounded-lg px-3 py-2"
                      >
                        <option value="">Select year</option>
                        <option value="1">First Year</option>
                        <option value="2">Second Year</option>
                        <option value="3">Third Year</option>
                        <option value="4">Fourth Year</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Department
                    </label>
                    <input
                      type="text"
                      value={formData.studentDepartment}
                      onChange={(e) => handleChange(e, "studentDepartment")}
                      className="w-full border rounded-lg px-3 py-2"
                      placeholder="e.g. Engineering"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Supporting Documents
                </label>
                <div className="border-dashed border-2 border-gray-300 rounded-lg p-4 text-center">
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    id="document-upload"
                    onChange={(e) => {
                      if (e.target.files) {
                        setFormData((prev) => ({
                          ...prev,
                          documents: Array.from(e.target.files || []),
                        }));
                      }
                    }}
                  />
                  <label
                    htmlFor="document-upload"
                    className="cursor-pointer text-blue-600 hover:text-blue-800"
                  >
                    Click to upload documents
                  </label>
                  <p className="text-sm text-gray-500 mt-1">
                    (Student records, fee receipts, etc.)
                  </p>
                </div>
                {formData.documents.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">
                      {formData.documents.length} file(s) selected
                    </p>
                  </div>
                )}
              </div>
              {/*
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Milestones
              </label>
              {formData.milestones.map((milestone, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    placeholder="Milestone title"
                    value={milestone.title}
                    onChange={(e) => handleMilestoneChange(index, 'title', e.target.value)}
                    className="flex-1 border rounded-lg px-3 py-2"
                    required
                  />
                  <input
                    type="date"
                    value={milestone.dueDate}
                    onChange={(e) => handleMilestoneChange(index, 'dueDate', e.target.value)}
                    className="w-40 border rounded-lg px-3 py-2"
                    required
                  />
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => handleRemoveMilestone(index)}
                      className="text-red-600 p-2"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={handleAddMilestone}
                className="text-blue-600 text-sm mt-2 flex items-center"
              >
                <Plus className="h-4 w-4 mr-1" /> Add Milestone
              </button>
            </div> */}

              {/* Privacy Settings */}
              <div className="mt-6 border-t pt-6">
                {/* <h2 className="text-lg font-semibold mb-4">Privacy Settings</h2> */}
                <div className="space-y-4">
                  {/*
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id="showSupporters"
                      checked={formData.showSupportersToOthers}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          showSupportersToOthers: e.target.checked,
                        }))
                      }
                      className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <label
                        htmlFor="showSupporters"
                        className="text-sm font-medium text-gray-700"
                      >
                        Show supporters list to other supporters
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        When enabled, supporters can see who else has
                        contributed to this campaign. When disabled, supporter
                        information remains private.
                      </p>
                    </div>
                  </div>
                  */}
                </div>
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  type="button"
                  onClick={() =>
                    navigate("/institution-dashboard?tab=campaigns")
                  }
                  className="px-4 py-2 border rounded-lg text-gray-600 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  onClick={(e) => {
                    console.log("Submit button clicked directly");
                    if (!e.defaultPrevented) {
                      debugSubmit(e);
                    }
                  }}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Create Campaign
                </button>
              </div>
            </form>
          </div>
          </div>
        </div>
      </div>
      
      
    </div>
  );
}

export default CreateCampaignPage;
