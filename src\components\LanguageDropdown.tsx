import React from 'react';
import { Globe } from 'lucide-react';
import { SupportedLanguage } from '../services/translationService';

interface LanguageDropdownProps {
  selectedLanguage: SupportedLanguage;
  onLanguageChange: (language: SupportedLanguage) => void;
}

const languages = [
  { code: 'en' as SupportedLanguage, name: 'English', flag: '' },
  { code: 'ta' as SupportedLanguage, name: '(Tamil)', flag: 'தமிழ்' },
  { code: 'ur' as SupportedLanguage, name: '(Urdu)', flag: 'اردو' }
];

const LanguageDropdown: React.FC<LanguageDropdownProps> = ({
  selectedLanguage,
  onLanguageChange
}) => {
  return (
    <div className="relative inline-block">
    Language:
      <select
        value={selectedLanguage}
        onChange={(e) => onLanguageChange(e.target.value as SupportedLanguage)}
        className="appearance-none ml-2 bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        {languages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {lang.flag} {lang.name}
          </option>
        ))}
      </select>
      <Globe className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
    </div>
  );
};

export default LanguageDropdown;