import React, { useEffect, useState } from 'react';
import { Eye } from 'lucide-react';

interface ViewCounterProps {
  campaignId: number;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showLabel?: boolean;
  className?: string;
  incrementOnMount?: boolean;
}

/**
 * ViewCounter component displays the number of views for a campaign
 * and can optionally increment the view count when mounted.
 */
export function ViewCounter({
  campaignId,
  size = 'md',
  showIcon = true,
  showLabel = true,
  className = '',
  incrementOnMount = false
}: ViewCounterProps) {
  const [viewCount, setViewCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Get font size based on size prop
  const getFontSize = () => {
    switch (size) {
      case 'sm': return 'text-xs';
      case 'md': return 'text-sm';
      case 'lg': return 'text-base';
      default: return 'text-sm';
    }
  };

  // Get icon size based on size prop
  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'h-3 w-3';
      case 'md': return 'h-4 w-4';
      case 'lg': return 'h-5 w-5';
      default: return 'h-4 w-4';
    }
  };

  // Format view count (e.g., 1000 -> 1K)
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    } else {
      return count.toString();
    }
  };

  // Load view count from localStorage
  useEffect(() => {
    const loadViewCount = () => {
      try {
        // Get campaign views from localStorage
        const campaignViews = localStorage.getItem('campaignViews');
        const viewsData = campaignViews ? JSON.parse(campaignViews) : {};
        
        // Get view count for this campaign
        const count = viewsData[campaignId] || 0;
        setViewCount(count);
        
        // If incrementOnMount is true, increment the view count
        if (incrementOnMount) {
          const newCount = count + 1;
          viewsData[campaignId] = newCount;
          localStorage.setItem('campaignViews', JSON.stringify(viewsData));
          setViewCount(newCount);
          
          // Also update the total views count
          updateTotalViews();
        }
      } catch (error) {
        console.error('Error loading view count:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Update total views in localStorage
    const updateTotalViews = () => {
      try {
        // Get total views from localStorage
        const totalViewsStr = localStorage.getItem('totalCampaignViews');
        const totalViews = totalViewsStr ? parseInt(totalViewsStr, 10) : 0;
        
        // Increment total views
        localStorage.setItem('totalCampaignViews', (totalViews + 1).toString());
        
        // Record view timestamp for analytics
        const viewTimestamps = localStorage.getItem('viewTimestamps');
        const timestamps = viewTimestamps ? JSON.parse(viewTimestamps) : [];
        timestamps.push({
          campaignId,
          timestamp: new Date().toISOString()
        });
        localStorage.setItem('viewTimestamps', JSON.stringify(timestamps));
      } catch (error) {
        console.error('Error updating total views:', error);
      }
    };
    
    // Small delay to simulate API call
    const timer = setTimeout(() => {
      loadViewCount();
    }, 300);
    
    return () => clearTimeout(timer);
  }, [campaignId, incrementOnMount]);

  if (isLoading) {
    return (
      <div className={`flex items-center ${getFontSize()} text-gray-400 ${className}`}>
        {showIcon && <div className={`${getIconSize()} mr-1 animate-pulse bg-gray-200 rounded-full`}></div>}
        <div className="w-8 h-3 animate-pulse bg-gray-200 rounded"></div>
        {showLabel && <div className="ml-1 w-12 h-3 animate-pulse bg-gray-200 rounded"></div>}
      </div>
    );
  }

  return (
    <div className={`flex items-center ${getFontSize()} text-gray-500 ${className}`}>
      {showIcon && <Eye className={`${getIconSize()} mr-1`} />}
      <span>{formatViewCount(viewCount)}</span>
      {showLabel && <span className="ml-1">{viewCount === 1 ? 'view' : 'views'}</span>}
    </div>
  );
}

export default ViewCounter;
