import googleApi from './googleApi';

interface LogoResponse {
  logoUrl: string;
}

/**
 * Service for fetching institution logo
 */
const logoService = {
  /**
   * Get the institution logo URL
   * @returns Promise with logo URL
   */
  getLogo: async (): Promise<string> => {
    try {
      const response = await googleApi.get<LogoResponse>('/api/student/v1/logo');
      return response.data.logoUrl;
    } catch (error) {
      console.error('Error fetching logo:', error);
      return ''; // Return empty string if logo fetch fails
    }
  }
};

export default logoService;