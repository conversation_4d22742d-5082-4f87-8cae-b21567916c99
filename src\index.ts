// Components exports
export * from './components/AuthCallback';
export * from './components/Breadcrumb';
export * from './components/CampaignActivityLog';
export * from './components/CampaignClosureInfo';
export * from './components/CampaignViewTracker';
export * from './components/CognitoCodeHandler';
export * from './components/CognitoProtectedRoute';
export * from './components/Footer';
export * from './components/InstitutionFooter';
export * from './components/StudentFooter';
export * from './components/SupporterFooter';
export * from './components/GoogleLogin';
export * from './components/InstitutionLogin';
export * from './components/InstitutionRegistration';
export * from './components/LoadingSpinner';
export * from './components/Login';
export * from './components/Navbar';
export * from './components/OidcAuthCallback';
export * from './components/Pagination';
export * from './components/StudentLogin';
export * from './components/StudentProtectedRoute';
export * from './components/StudentRegistration';
export * from './components/SupporterLogin';
export * from './components/SupporterProtectedRoute';
export * from './components/SupporterRegistration';
export * from './components/SupporterRegistrationForm';
export * from './components/InstitutionHeader';
export * from './components/StudentHeader';
export * from './components/SupporterHeader';

// Context exports
export * from './context/AuthContext';
export * from './context/StudentAuthContext';
export * from './context/SupporterAuthContext';

// Pages exports
export * from './pages/HomePage';
export * from './pages/AuthPage';
export * from './pages/StudentPage';
export * from './pages/SupporterPage';
export * from './pages/CampaignDetailsPage';
export * from './pages/StudentCampaignDetailsPage';
export * from './pages/InstitutionCampaignDetailsPage';
export * from './pages/DonationDetailsPage';

// Services exports
export * from './services';
export { default as translationService } from './services/translationService';
export type { SupportedLanguage } from './services/translationService';

// Utils exports
export * from './utils/apiErrorToast';
export * from './utils/breadcrumbUtils';
export * from './utils/lazyLoad';
// Components exports
export * from './components/StudentSidebar';
export { default as LanguageDropdown } from './components/LanguageDropdown';

// Utils exports
export * from './utils/getInitials';


// App export
export * from './App';