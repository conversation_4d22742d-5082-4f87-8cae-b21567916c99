import React, { useEffect } from 'react';

interface CampaignViewTrackerProps {
  campaignId: number;
  userId?: string | null;
}

/**
 * CampaignViewTracker is an invisible component that tracks campaign views
 * It should be included in any page that displays campaign details
 */
export function CampaignViewTracker({ campaignId, userId = null }: CampaignViewTrackerProps) {
  useEffect(() => {
    // Track the view when the component mounts
    const trackView = () => {
      try {
        // Get campaign views from localStorage
        const campaignViews = localStorage.getItem('campaignViews');
        const viewsData = campaignViews ? JSON.parse(campaignViews) : {};
        
        // Increment view count for this campaign
        viewsData[campaignId] = (viewsData[campaignId] || 0) + 1;
        localStorage.setItem('campaignViews', JSON.stringify(viewsData));
        
        // Update total views count
        const totalViewsStr = localStorage.getItem('totalCampaignViews');
        const totalViews = totalViewsStr ? parseInt(totalViewsStr, 10) : 0;
        localStorage.setItem('totalCampaignViews', (totalViews + 1).toString());
        
        // Record view timestamp for analytics
        const viewTimestamps = localStorage.getItem('viewTimestamps');
        const timestamps = viewTimestamps ? JSON.parse(viewTimestamps) : [];
        
        // Add new timestamp with additional metadata
        timestamps.push({
          campaignId,
          userId: userId || 'anonymous',
          timestamp: new Date().toISOString(),
          device: getDeviceType(),
          referrer: document.referrer || 'direct',
          userAgent: navigator.userAgent
        });
        
        // Keep only the last 1000 timestamps to avoid localStorage size limits
        if (timestamps.length > 1000) {
          timestamps.splice(0, timestamps.length - 1000);
        }
        
        localStorage.setItem('viewTimestamps', JSON.stringify(timestamps));
        
        // Record unique visitors
        trackUniqueVisitor(campaignId);
        
        console.log(`View tracked for campaign ${campaignId}`);
      } catch (error) {
        console.error('Error tracking view:', error);
      }
    };
    
    // Track unique visitors
    const trackUniqueVisitor = (campaignId: number) => {
      try {
        // Generate a visitor ID if not exists
        let visitorId = localStorage.getItem('visitorId');
        if (!visitorId) {
          visitorId = `visitor_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
          localStorage.setItem('visitorId', visitorId);
        }
        
        // Get unique visitors for this campaign
        const uniqueVisitorsStr = localStorage.getItem('uniqueVisitors');
        const uniqueVisitors = uniqueVisitorsStr ? JSON.parse(uniqueVisitorsStr) : {};
        
        // If this visitor hasn't been recorded for this campaign, add them
        if (!uniqueVisitors[campaignId] || !uniqueVisitors[campaignId].includes(visitorId)) {
          uniqueVisitors[campaignId] = uniqueVisitors[campaignId] || [];
          uniqueVisitors[campaignId].push(visitorId);
          localStorage.setItem('uniqueVisitors', JSON.stringify(uniqueVisitors));
        }
      } catch (error) {
        console.error('Error tracking unique visitor:', error);
      }
    };
    
    // Detect device type
    const getDeviceType = (): string => {
      const userAgent = navigator.userAgent;
      if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(userAgent)) {
        return 'tablet';
      }
      if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(userAgent)) {
        return 'mobile';
      }
      return 'desktop';
    };
    
    // Only track the view if this is a real campaign ID
    if (campaignId && campaignId > 0) {
      // Add a small delay to ensure the page has loaded
      const timer = setTimeout(() => {
        trackView();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [campaignId, userId]);

  // This component doesn't render anything
  return null;
}

export default CampaignViewTracker;
