/**
 * Cognito Authentication Service
 *
 * This service provides methods to interact with AWS Cognito for authentication
 * using the Cognito Hosted UI approach.
 */

/**
 * Builds the Cognito authorization URL for login
 * @param state Optional state parameter for CSRF protection
 * @returns The complete authorization URL
 */
const buildAuthorizationUrl = (state?: string): string => {
  // Get values directly from environment variables with more detailed logging
  const domain = import.meta.env.VITE_COGNITO_DOMAIN;
  const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
  const redirectUri = import.meta.env.VITE_COGNITO_REDIRECT_URI;
  const responseType = import.meta.env.VITE_COGNITO_RESPONSE_TYPE;
  const scope = import.meta.env.VITE_COGNITO_SCOPE;

  // console.log('Environment variables for Cognito:', {
  //   'import.meta.env.VITE_COGNITO_DOMAIN': import.meta.env.VITE_COGNITO_DOMAIN,
  //   'COGNITO_DOMAIN fallback': COGNITO_DOMAIN,
  //   'Used domain': domain
  // });

  console.log('Building authorization URL with configuration:', {
    domain,
    clientId,
    redirectUri,
    responseType,
    scope
  });

  // Ensure domain is not a placeholder value
  if (!domain || domain.includes('your-cognito-domain')) {
    console.error('Invalid Cognito domain:', domain);
    alert('Cognito configuration error: Invalid domain. Check console for details.');
    return '';
  }

  const params = new URLSearchParams({
    client_id: clientId,
    response_type: responseType,
    scope: scope,
    redirect_uri: redirectUri,
  });

  if (state) {
    params.append('state', state);
  }

  const authUrl = `${domain}/login?${params.toString()}`;
  console.log('Generated authorization URL:', authUrl);
  return authUrl;
};

/**
 * Builds the Cognito logout URL
 * @returns The complete logout URL
 */
const buildLogoutUrl = (): string => {
  // Get values directly from environment variables
  let domain = import.meta.env.VITE_COGNITO_DOMAIN;
  const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
  // Use the root path directly instead of going through /auth
  const signoutUri = import.meta.env.VITE_COGNITO_SIGNOUT_URI || 'http://localhost:5173${import.meta.env.VITE_BASE_PATH}/';

  // Fix domain if it's not in the correct format
  // The correct format should be: https://your-domain-prefix.auth.region.amazoncognito.com
  if (domain && !domain.includes('.auth.')) {
    console.warn('Domain URL may be incorrect, attempting to fix:', domain);
    // Try to convert from https://domain.amazoncognito.com to https://domain.auth.region.amazoncognito.com
    domain = domain.replace('.amazoncognito.com', '.auth.us-east-1.amazoncognito.com');
    console.log('Fixed domain URL:', domain);
  }

  console.log('Building logout URL with configuration:', {
    domain,
    clientId,
    signoutUri
  });

  // Ensure domain is not a placeholder value
  if (!domain || domain.includes('your-cognito-domain')) {
    console.error('Invalid Cognito domain for logout:', domain);
    alert('Cognito configuration error: Invalid domain for logout. Check console for details.');
    return '';
  }

  // Construct the logout URL correctly for AWS Cognito
  // Format: https://domain/logout?client_id=app-client-id&logout_uri=logout-url
  // Make sure to properly encode the logout_uri parameter
  // Ensure we're using the /logout endpoint, not /login
  const endpoint = '/logout';
  const logoutUrl = `${domain}${endpoint}?client_id=${clientId}&logout_uri=${encodeURIComponent(signoutUri)}`;

  // Log the domain and client ID for debugging
  console.log('Domain used for logout:', domain);
  console.log('Client ID used for logout:', clientId);
  console.log('Signout URI used for logout:', signoutUri);
  console.log('Generated logout URL:', logoutUrl);
  return logoutUrl;
};

/**
 * Redirects the user to the Cognito hosted UI for login
 * @param state Optional state parameter for CSRF protection
 */
const redirectToLogin = (state?: string): void => {
  const authUrl = buildAuthorizationUrl(state);
  if (authUrl) {
    console.log('Redirecting to Cognito login URL:', authUrl);
    window.location.href = authUrl;
  } else {
    console.error('Cannot redirect to Cognito: Invalid authorization URL');
    alert('Cannot redirect to Cognito login. Please check your Cognito configuration in the .env file.');
  }
};

/**
 * Redirects the user to the Cognito logout endpoint
 */
const redirectToLogout = (): void => {
  const logoutUrl = buildLogoutUrl();
  if (logoutUrl) {
    console.log('Redirecting to Cognito logout URL:', logoutUrl);
    window.location.href = logoutUrl;
  } else {
    console.error('Cannot redirect to Cognito logout: Invalid logout URL');
    alert('Cannot redirect to Cognito logout. Please check your Cognito configuration in the .env file.');
  }
};

/**
 * Exchanges an authorization code for tokens
 * @param code The authorization code from Cognito
 * @returns Promise with the token response
 */
const exchangeCodeForTokens = async (code: string) => {
  try {
    // Get values directly from environment variables
    const domain = import.meta.env.VITE_COGNITO_DOMAIN;
    const clientId = import.meta.env.VITE_COGNITO_CLIENT_ID;
    const redirectUri = import.meta.env.VITE_COGNITO_REDIRECT_URI;
    const clientSecret = import.meta.env.VITE_COGNITO_CLIENT_SECRET;

    console.log('Exchanging code for tokens with configuration:', {
      domain,
      clientId,
      redirectUri,
      hasClientSecret: !!clientSecret,
      codeLength: code?.length
    });

    // In a real implementation, this would be a server-side call
    // For security reasons, client_secret should not be exposed in frontend code
    // This is a simplified example for demonstration purposes
    const tokenUrl = `${domain}/oauth2/token`;
    console.log('Token URL:', tokenUrl);

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: clientId,
      code,
      redirect_uri: redirectUri,
    });

    // Add client_secret if available (should be used in server-side code only)
    if (clientSecret) {
      params.append('client_secret', clientSecret);
    }

    console.log('Token request params (without client_secret):',
      params.toString().replace(/client_secret=[^&]*/, 'client_secret=REDACTED'));

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token exchange failed:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`Failed to exchange code for tokens: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const tokenData = await response.json();
    console.log('Token exchange successful, received token data:', tokenData);

    // Log the token data to help debug
    if (tokenData && tokenData.id_token) {
      console.log('ID token received, length:', tokenData.id_token.length);
    } else {
      console.warn('No ID token in response!', tokenData);
    }

    return tokenData;
  } catch (error) {
    console.error('Error exchanging code for tokens:', error);
    throw error;
  }
};

/**
 * Parses the JWT token to extract user information
 * @param token The JWT token
 * @returns Decoded token payload
 */
const parseJwt = (token: string) => {
  try {
    return JSON.parse(atob(token.split('.')[1]));
  } catch (e) {
    console.error('Error parsing JWT token:', e);
    return null;
  }
};

/**
 * Saves the authentication tokens to storage
 * @param tokens The tokens object from Cognito
 * @param rememberMe Whether to use localStorage (true) or sessionStorage (false)
 */
const saveTokens = (tokens: any, rememberMe = false) => {
  console.log('Saving tokens with rememberMe =', rememberMe);
  console.log('Token keys available:', Object.keys(tokens));

  if (!tokens || !tokens.id_token) {
    console.error('Invalid tokens object or missing id_token!', tokens);
    return;
  }

  const storage = rememberMe ? localStorage : sessionStorage;

  try {
    storage.setItem('id_token', tokens.id_token);
    storage.setItem('access_token', tokens.access_token);
    storage.setItem('refresh_token', tokens.refresh_token);
    storage.setItem('token_type', tokens.token_type);
    storage.setItem('expires_in', String(tokens.expires_in));
    storage.setItem('token_expiry', String(Date.now() + tokens.expires_in * 1000));

    // Verify tokens were saved
    const savedIdToken = storage.getItem('id_token');
    console.log('Tokens saved successfully. ID token in storage:',
      savedIdToken ? `${savedIdToken.substring(0, 10)}...` : 'null');
  } catch (error) {
    console.error('Error saving tokens to storage:', error);
  }
};

/**
 * Gets the current user information from the stored tokens
 * @returns User information or null if not authenticated
 */
const getCurrentUser = () => {
  // Check both localStorage and sessionStorage for tokens
  const idToken = localStorage.getItem('id_token') || sessionStorage.getItem('id_token');

  if (!idToken) {
    return null;
  }

  // Parse the JWT token to get user info
  const decodedToken = parseJwt(idToken);

  if (!decodedToken) {
    return null;
  }

  return {
    id: decodedToken.sub,
    email: decodedToken.email,
    name: decodedToken.name || `${decodedToken.given_name || ''} ${decodedToken.family_name || ''}`.trim(),
    userType: 'institution', // For Cognito users, we're assuming they're all institutions
  };
};

/**
 * Clears all authentication tokens from storage
 */
const clearTokens = () => {
  // Clear from both localStorage and sessionStorage
  localStorage.removeItem('id_token');
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('token_type');
  localStorage.removeItem('expires_in');
  localStorage.removeItem('token_expiry');

  sessionStorage.removeItem('id_token');
  sessionStorage.removeItem('access_token');
  sessionStorage.removeItem('refresh_token');
  sessionStorage.removeItem('token_type');
  sessionStorage.removeItem('expires_in');
  sessionStorage.removeItem('token_expiry');
};

/**
 * IMPORTANT: DO NOT USE THIS FUNCTION IN REACT COMPONENTS!
 *
 * This is only a fallback for non-React code that needs to check authentication.
 * React components should ALWAYS use the useAuth() hook instead:
 *
 * ```
 * const { isAuthenticated } = useAuth();
 * if (isAuthenticated) {
 *   // User is authenticated
 * }
 * ```
 *
 * @returns True if authenticated based on token storage (fallback method only)
 */
const isAuthenticated = () => {
  console.warn(
    'cognitoService.isAuthenticated() called - this is a fallback method only. ' +
    'React components should use the useAuth() hook instead.'
  );

  // This is only for non-React code that can't use hooks
  const idToken = localStorage.getItem('id_token') || sessionStorage.getItem('id_token');
  const tokenExpiry = localStorage.getItem('token_expiry') || sessionStorage.getItem('token_expiry');

  if (!idToken || !tokenExpiry) {
    return false;
  }

  // Check if token is expired
  const expiryTime = parseInt(tokenExpiry, 10);
  return Date.now() < expiryTime;
};

const cognitoService = {
  redirectToLogin,
  redirectToLogout,
  exchangeCodeForTokens,
  saveTokens,
  getCurrentUser,
  clearTokens,
  isAuthenticated,
  parseJwt,
  buildAuthorizationUrl,
  buildLogoutUrl,
};

export default cognitoService;



