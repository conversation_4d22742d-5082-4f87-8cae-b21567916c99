import api from './api';
import googleApi from './googleApi';

export interface Donation {
  id: number | string;
  campaignId?: number | string;
  userId?: number | string;
  amount: number;
  status?: 'PENDING' | 'COMPLETED' | 'FAILED';
  transactionId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserDonation {
  id: string;
  campaignTitle: string;
  studentFullName: string;
  donationDate: string;
  transactionId: string;
  amount: number;
  progressPercentage: number;
  donationStatus: string;
}

const donationService = {
  /**
   * Get all donations
   * @returns Promise with array of donations
   */
  getAllDonations: async (): Promise<Donation[]> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.get('/api/supporter/v1/donations');
    return response.data;
  },

  /**
   * Get donation by ID
   * @param id Donation ID
   * @returns Promise with donation details response
   */
  getDonationById: async (id: number | string): Promise<{success: boolean, message: string | null, data: any}> => {
    const response = await googleApi.get(`/api/supporter/v1/donation/${id}`);
    return response.data;
  },

  /**
   * Create a new donation
   * @param donationData Donation data including campaignId, userId, and amount
   * @returns Promise with created donation
   */
  createDonation: async (donationData: Partial<Donation>): Promise<Donation> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.post('/api/supporter/v1/donation', donationData);
    return response.data;
  },

  /**
   * Get donations by campaign ID
   * @param campaignId Campaign ID
   * @returns Promise with array of donations for the campaign
   */
  getDonationsByCampaign: async (campaignId: number | string): Promise<Donation[]> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.get(`/api/supporter/v1/donations/campaign/${campaignId}`);
    return response.data;
  },

  /**
   * Get donations by user ID
   * @param userId User ID
   * @returns Promise with array of donations made by the user
   */
  getDonationsByUser: async (userId: number | string): Promise<Donation[]> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.get(`/api/supporter/v1/donations/user/${userId}`);
    return response.data;
  },

  /**
   * Update donation status
   * @param id Donation ID
   * @param status New status (PENDING, COMPLETED, FAILED)
   * @returns Promise with updated donation
   */
  updateDonationStatus: async (id: number | string, status: 'PENDING' | 'COMPLETED' | 'FAILED'): Promise<Donation> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.patch(`/api/supporter/v1/donation/${id}/status`, { status });
    return response.data;
  },

  /**
   * Process payment for donation
   * @param donationId Donation ID
   * @param paymentDetails Payment details including payment method and transaction ID
   * @returns Promise with processed payment result
   */
  processPayment: async (donationId: number | string, paymentDetails: {
    paymentMethod: string;
    transactionId: string;
  }): Promise<any> => {
    // Use the API endpoint pattern from the curl example
    const response = await api.post(`/api/supporter/v1/donation/${donationId}/payment`, paymentDetails);
    return response.data;
  },

  /**
   * Get current user's donations
   * @returns Promise with array of user donations
   */
  getMyDonations: async (): Promise<UserDonation[]> => {
    const response = await googleApi.get('/api/supporter/v1/me/donations');
    return response.data.data;
  }
};

export default donationService;
