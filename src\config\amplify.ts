import { Amplify } from 'aws-amplify';
import { COGNITO_DOMAIN, COGNITO_CLIENT_ID } from './env';

// Configure Amplify
Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: 'us-east-1_xr0yffogk', // Extract from domain
      userPoolClientId: COGNITO_CLIENT_ID,
      signUpVerificationMethod: 'code',
      loginWith: {
        oauth: {
          domain: COGNITO_DOMAIN,
          scopes: ['email', 'openid', 'phone', 'profile'],
          responseType: 'code',
          redirectSignIn: [window.location.origin + '/auth/callback'],
          redirectSignOut: [window.location.origin + '/auth']
        }
      }
    }
  }
});

export default Amplify; 