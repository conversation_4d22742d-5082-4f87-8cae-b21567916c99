/**
 * Environment Configuration
 *
 * This file centralizes all environment variable access to ensure consistent
 * fallback values and easier debugging of environment-related issues.
 */

// AWS Cognito Configuration
export const COGNITO_DOMAIN = import.meta.env.VITE_COGNITO_DOMAIN || '';
export const COGNITO_CLIENT_ID = import.meta.env.VITE_COGNITO_CLIENT_ID || '';

if (!COGNITO_DOMAIN || !COGNITO_CLIENT_ID) {
  console.error('Missing required Cognito configuration. Please check your environment variables.');
}

export const COGNITO_REDIRECT_URI = import.meta.env.VITE_COGNITO_REDIRECT_URI || 'http://localhost:5173${import.meta.env.VITE_BASE_PATH}/auth/callback';
export const COGNITO_SIGNOUT_URI = import.meta.env.VITE_COGNITO_SIGNOUT_URI || 'http://localhost:5173${import.meta.env.VITE_BASE_PATH}/';
export const COGNITO_PASSWORD_RESET_REDIRECT_URI = import.meta.env.VITE_COGNITO_PASSWORD_RESET_REDIRECT_URI || 'http://localhost:5173${import.meta.env.VITE_BASE_PATH}/auth/password-reset-complete';
export const COGNITO_RESPONSE_TYPE = import.meta.env.VITE_COGNITO_RESPONSE_TYPE || 'code';
export const COGNITO_SCOPE = import.meta.env.VITE_COGNITO_SCOPE || 'email openid phone profile';
export const COGNITO_AUTHORITY = import.meta.env.VITE_COGNITO_AUTHORITY || 'https://cognito-idp.us-east-1.amazonaws.com/us-east-1_xr0yffogk';


// Log Cognito configuration for debugging
console.log('Cognito Configuration from env.ts:', {
  COGNITO_DOMAIN,
  COGNITO_CLIENT_ID,
  COGNITO_REDIRECT_URI,
  COGNITO_SIGNOUT_URI,
  COGNITO_PASSWORD_RESET_REDIRECT_URI,
  COGNITO_RESPONSE_TYPE,
  COGNITO_SCOPE,
  COGNITO_AUTHORITY,
  'import.meta.env.VITE_COGNITO_DOMAIN': import.meta.env.VITE_COGNITO_DOMAIN,
});

// Log environment configuration during development
if (import.meta.env.DEV) {
  console.log('Environment Configuration:', {
    COGNITO_DOMAIN,
    COGNITO_CLIENT_ID,
    COGNITO_REDIRECT_URI,
    COGNITO_SIGNOUT_URI,
    COGNITO_PASSWORD_RESET_REDIRECT_URI,
    COGNITO_RESPONSE_TYPE,
    COGNITO_SCOPE,
    COGNITO_AUTHORITY,
    NODE_ENV: import.meta.env.MODE,
    BASE_URL: import.meta.env.BASE_URL,
  });
}
