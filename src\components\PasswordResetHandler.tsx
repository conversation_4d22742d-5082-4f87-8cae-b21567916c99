import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { clearOidcState } from '../utils/authUtils';

/**
 * Component to handle password reset completion
 * This should be used as a redirect target after Cognito password reset
 */
export function PasswordResetHandler() {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.log('Password reset handler activated');
    
    // Clear any existing OIDC state to ensure fresh login
    clearOidcState();
    
    // Show success message
    toast.success('Password reset successful! Please sign in with your new password.');
    
    // Redirect to login page with completion flag
    navigate('/institution-login?password_reset_complete=true', { replace: true });
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Password Reset Complete</h2>
          <div className="flex justify-center my-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
          </div>
          <p className="text-gray-600">
            Redirecting you to the login page...
          </p>
        </div>
      </div>
    </div>
  );
}

export default PasswordResetHandler;