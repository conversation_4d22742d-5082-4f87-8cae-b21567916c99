import api from './api';
import { getIdToken } from './institutionService';

export interface FeedbackMessage {
  message: string;
  replies: any;
  email: string;
  name: string;
  id: string;
  authorName: string;
  authorRole: string;
  authorEmail: string;
  content: string;
  parentId?: string;
  createdAt: string;
}

export interface PostFeedbackPayload {
  content: string;
  parentId?: string | null;
}


const feedbackService = {
  // GET /api/campaigns/{campaignId}/feedback
  async getFeedback(
    campaignId: string,
    tokenOrAuth?: string | any
  ): Promise<FeedbackMessage[]> {
    let headers: any = {};
    let token: string | null = null;
    
    try {
      token = typeof tokenOrAuth === 'string'
        ? tokenOrAuth
        : getIdToken(tokenOrAuth);
      
      if (!token) {
        console.warn('getFeedback: No auth token available');
        return []; // Return empty array if no token is available
      }
      
      headers.Authorization = `Bearer ${token}`;
    } catch (err) {
      console.warn('getFeedback: No auth token available');
      return []; // Return empty array if token extraction fails
    }

    const response = await api.get(
      `/api/campaigns/${campaignId}/feedback`,
      { headers }
    );
    return response.data?.data ?? response.data;
  },

 // POST /api/campaigns/{campaignId}/feedback
 async postFeedback(
    campaignId: string,
    payload: PostFeedbackPayload,
    tokenOrAuth?: string | any
  ): Promise<FeedbackMessage> {
    const token =
      typeof tokenOrAuth === 'string'
        ? tokenOrAuth
        : getIdToken(tokenOrAuth);
    const response = await api.post(
      `/api/campaigns/${campaignId}/feedback`,
      payload,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return response.data?.data ?? response.data;
  },

  // DELETE /api/feedback/{feedbackId}
  async deleteFeedback(
    feedbackId: string,
    tokenOrAuth?: string | any
  ): Promise<void> {
    const token =
      typeof tokenOrAuth === 'string'
        ? tokenOrAuth
        : getIdToken(tokenOrAuth);
    await api.delete(`/api/feedback/${feedbackId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  }
};

export default feedbackService;
