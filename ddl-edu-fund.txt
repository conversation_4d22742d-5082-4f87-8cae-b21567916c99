import React from 'react';
import { GoogleLogin as ReactGoogleLogin } from '@react-oauth/google';
import { FcGoogle } from 'react-icons/fc';

interface GoogleLoginProps {
  buttonText?: string;
  onSuccess: (response: any) => void;
  onError: () => void;
  customClass?: string;
  iconClass?: string;
  textClass?: string;
}

export function GoogleLogin({
  buttonText = "Continue with Google",
  onSuccess,
  onError,
  customClass,
  iconClass,
  textClass
}: GoogleLoginProps) {
  // Use custom classes if provided, otherwise use default classes
  const buttonClassName = customClass || "w-full flex items-center justify-center gap-2 bg-white text-gray-700 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500";
  const googleIconClassName = iconClass || "w-5 h-5";
  const textClassName = textClass || "";

  return (
    <ReactGoogleLogin
      onSuccess={onSuccess}
      onError={onError}
      useOneTap
      theme="filled_blue"
      text="continue_with"
      shape="rectangular"
      width="100%"
    />
  );
ALTER TABLE edufund.user_roles ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES edufund.users(user_id);
