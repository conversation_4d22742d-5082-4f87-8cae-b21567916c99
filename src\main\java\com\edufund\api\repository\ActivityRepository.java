package com.edufund.api.repository;

import com.edufund.api.model.Activity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityRepository extends JpaRepository<Activity, Long> {
    
    List<Activity> findByInstitutionIdOrderByTimestampDesc(Long institutionId);
    
    List<Activity> findByEntityIdAndEntityTypeOrderByTimestampDesc(Long entityId, String entityType);
}
