import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactNode;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  return (
    <nav className={`flex items-center text-sm bg-white p-3 rounded-lg shadow-sm mb-4 ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center flex-wrap w-full">
        {items.map((item, index) => {
          const isLast = index === items.length - 1;

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 mx-2 text-gray-400 flex-shrink-0" />
              )}

              {isLast ? (
                <span className="font-medium text-gray-800 flex items-center">
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  <span className="truncate max-w-[150px] md:max-w-none">{item.label}</span>
                </span>
              ) : (
                <Link
                  to={item.path || '#'}
                  className="text-blue-600 hover:text-blue-800 flex items-center"
                >
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  <span className="truncate max-w-[100px] md:max-w-none">{item.label}</span>
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

export default Breadcrumb;
