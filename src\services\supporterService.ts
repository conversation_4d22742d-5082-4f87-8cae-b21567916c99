import googleApi from './googleApi';
import publicApi from './publicApi';
import { UserDonation } from './donationService';

export interface SupporterCreateRequest {
  firstName: string;
  lastName: string;
}

export interface Supporter {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service for managing supporters in the EduFund application
 */
const supporterService = {
  /**
   * Check if the logged-in supporter exists and is active
   * @returns Promise with existence and status
   */
  checkLoggedInSupporter: async (): Promise<{ exists: boolean; status: string }> => {
    try {
      console.log('Checking logged-in supporter existence...');
      const response = await googleApi.get('/api/supporter/v1/me/check');
      console.log('Supporter check response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error checking logged-in supporter:', error);
      throw error;
    }
  },

  /**
   * Register a new supporter
   * @param supporterData Supporter registration data
   * @returns Promise with registration result
   */
  registerSupporter: async (supporterData: SupporterCreateRequest): Promise<{ success: boolean }> => {
    try {
      console.log('Registering new supporter with data:', supporterData);
      const response = await googleApi.post('/api/supporter/v1/register', supporterData);
      console.log('Register supporter response:', response.data);
      return { success: true };
    } catch (error) {
      console.error('Error registering supporter:', error);
      throw error;
    }
  },

  /**
   * Fetch campaigns for supporters
   * @param query Search keyword
   * @param page Page number
   * @param size Page size
   * @returns Promise with campaign list
   */
  getSupporterCampaigns: async (
    query: string,
    page: number = 0,
    size: number = 12,
    sortBy: string = 'mostRecent'
  ) => {
    try {
      const params: Record<string, string | number> = {
        sortBy,
        page,
        size
      };
      if (query) {
        params.q = query;
      }

      const response = await publicApi.get('/api/public/v1/campaigns', {
        params
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching supporter campaigns:', error);
      throw error;
    }
  },

  /**
   * Get donations for the currently logged in supporter
   * @param page Page number
   * @param size Page size
   * @param sort Sort parameter (e.g. "donationDate,desc")
   * @returns Promise with paginated donation list
   */
 getMyDonations: async (
  page: number = 0,
  size: number = 10,
  sort: string = 'donatedAt,desc'
): Promise<{ 
  success: boolean; 
  message: string | null; 
  data: { 
    content: any[]; 
    totalPages: number; 
    totalElements: number;
    first: boolean;
    last: boolean;
    empty: boolean;
  } 
}> => {
  try {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('size', size.toString());
    params.append('sort', sort);

    const response = await googleApi.get(
      `/api/supporter/v1/me/donations?${params.toString()}`
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching supporter donations:', error);
    throw error;
  }
},


  /**
   * Get campaign details by ID for supporters
   * @param id Campaign ID
   * @returns Promise with campaign detailssupp
   */
  getCampaignById: async (id: string) => {
    try {
      const response = await publicApi.get(`/api/public/v1/campaigns/${id}`);
      
      // Extract data from the API response
      const apiData = response.data.data;
      
      // Map the API response to the expected format
      if (apiData) {
        return {
          id: apiData.id,
          studentId: apiData.id,
          name: apiData.student?.studentFullName || "Student",
          institution: apiData.student?.institutionName || "",
          course: apiData.student?.course || "",
          year: "",
          email: apiData.student?.email || "",
          phone: apiData.student?.phone || "",
          imageUrl: "",
          campaign: {
            name: apiData.title || "",
            story: apiData.description || "",
            targetAmount: apiData.target || 0,
            raisedAmount: apiData.raised || 0,
            startDate: apiData.duration?.split(" - ")[0] || "",
            endDate: apiData.duration?.split(" - ")[1] || "",
            status: apiData.status?.toLowerCase() === "active" ? "active" : 
                   apiData.status?.toLowerCase() === "completed" ? "completed" : "upcoming",
            supporters: apiData.supporters || 0,
            documents: [],
            updates: [],
            milestones: []
          }
        };
      }
      
      throw new Error("Invalid campaign data format");
    } catch (error) {
      console.error('Error fetching campaign details:', error);
      throw error;
    }
  },

  /**
   * Download supporter donation statement in PDF format
   * @param from Start date in YYYY-MM-DD format
   * @param to End date in YYYY-MM-DD format
   * @returns Promise resolving to a Blob containing PDF data
   */
  downloadDonationStatement: async (from: string, to: string): Promise<Blob> => {
    try {
      // Prepare parameters
      const params: Record<string, string> = {};
      if (from) params.from = from;
      if (to) params.to = to;
      
      console.log('Downloading donation statement with date range:', { from, to });
      
      const response = await googleApi.get('/api/supporter/v1/statement', {
        params,
        responseType: 'blob',
        headers: {
          'Accept': 'application/pdf'
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error downloading donation statement:', error);
      throw error;
    }
  },

  /**
   * Download a specific donation receipt
   * @param donationId Donation identifier
   * @returns Promise resolving to a Blob containing the receipt PDF
   */
  downloadReceipt: async (donationId: string): Promise<Blob> => {
    try {
      const response = await googleApi.get(`/api/supporter/v1/${donationId}/receipt`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error downloading receipt:', error);
      throw error;
    }
  }
};

export default supporterService;