// import React, { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { ChevronLeft, Save, Mail, FileText, Edit, Trash2, Copy, Eye, Plus, AlertTriangle, Info, Check, X } from 'lucide-react';
// import { toast } from 'react-toastify';
// import { useAuth } from '../context/AuthContext';

// interface EmailTemplate {
//   id: string;
//   name: string;
//   subject: string;
//   body: string;
//   description: string;
//   type: 'approval' | 'rejection' | 'notification' | 'reminder' | 'custom';
//   lastModified: string;
//   variables: string[];
// }

// export function EmailTemplatesPage() {
//   const navigate = useNavigate();
//   const { user } = useAuth();
//   const [isLoading, setIsLoading] = useState(true);
//   const [isSaving, setIsSaving] = useState(false);
//   const [templates, setTemplates] = useState<EmailTemplate[]>([]);
//   const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
//   const [editedTemplate, setEditedTemplate] = useState<EmailTemplate | null>(null);
//   const [showPreview, setShowPreview] = useState(false);
//   const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
//   const [showCreateModal, setShowCreateModal] = useState(false);
//   const [newTemplate, setNewTemplate] = useState<Partial<EmailTemplate>>({
//     name: '',
//     subject: '',
//     body: '',
//     description: '',
//     type: 'custom',
//     variables: []
//   });
  
//   // Sample variables for templates
//   const variablesByType = {
//     approval: [
//       '{student_name}', '{campaign_title}', '{amount}', '{institution_name}', 
//       '{approver_name}', '{approval_date}', '{approval_link}'
//     ],
//     rejection: [
//       '{student_name}', '{campaign_title}', '{amount}', '{institution_name}', 
//       '{rejector_name}', '{rejection_date}', '{rejection_reason}'
//     ],
//     notification: [
//       '{recipient_name}', '{campaign_title}', '{status}', '{date}', 
//       '{institution_name}', '{details_link}'
//     ],
//     reminder: [
//       '{approver_name}', '{pending_count}', '{oldest_request_date}', 
//       '{institution_name}', '{approvals_link}'
//     ],
//     custom: [
//       '{recipient_name}', '{institution_name}'
//     ]
//   };
  
//   // Fetch email templates
//   useEffect(() => {
//     const fetchTemplates = async () => {
//       try {
//         setIsLoading(true);
        
//         // In a real implementation, this would fetch from the API
//         // For now, we'll simulate a delay and use mock data
//         await new Promise(resolve => setTimeout(resolve, 1000));
        
//         // Mock data
//         const mockTemplates: EmailTemplate[] = [
//           {
//             id: 'template-1',
//             name: 'Campaign Approval Notification',
//             subject: 'Your Campaign Has Been Approved',
//             body: `<p>Dear {student_name},</p>
// <p>We are pleased to inform you that your campaign "{campaign_title}" has been approved by {institution_name}.</p>
// <p>The approved amount is ₹{amount}.</p>
// <p><strong>Next steps:</strong></p>
// <ul>
//   <li>Review the attached approval document</li>
//   <li>Complete the fund utilization form</li>
//   <li>Submit required documentation within 7 days</li>
// </ul>
// <p>If you have any questions, please contact our support team.</p>
// <p>Best regards,<br>{approver_name}<br>{institution_name}</p>`,
//             description: 'Sent to students when their campaign is approved',
//             type: 'approval',
//             lastModified: '2023-05-15T10:30:00Z',
//             variables: variablesByType.approval
//           },
//           {
//             id: 'template-2',
//             name: 'Campaign Rejection Notification',
//             subject: 'Campaign Funding Request Rejected',
//             body: `<p>Dear {student_name},</p>
// <p>We regret to inform you that your campaign funding request for "{campaign_title}" has been rejected.</p>
// <p><strong>Reason for rejection:</strong> {rejection_reason}</p>
// <p>If you would like to discuss this decision or submit a revised campaign, please contact our support team.</p>
// <p>Best regards,<br>{rejector_name}<br>{institution_name}</p>`,
//             description: 'Sent to students when their campaign is rejected',
//             type: 'rejection',
//             lastModified: '2023-05-16T14:45:00Z',
//             variables: variablesByType.rejection
//           },
//           {
//             id: 'template-3',
//             name: 'Approval Request Reminder',
//             subject: 'Reminder: Pending Campaign Approval Requests',
//             body: `<p>Dear {approver_name},</p>
// <p>This is a reminder that you have {pending_count} pending campaign approval requests that require your attention.</p>
// <p>The oldest request has been waiting since {oldest_request_date}.</p>
// <p>Please review these requests at your earliest convenience by clicking the link below:</p>
// <p><a href="{approvals_link}">Review Pending Approvals</a></p>
// <p>Thank you for your prompt attention to this matter.</p>
// <p>Best regards,<br>{institution_name} System</p>`,
//             description: 'Sent to approvers to remind them of pending requests',
//             type: 'reminder',
//             lastModified: '2023-06-01T09:15:00Z',
//             variables: variablesByType.reminder
//           }
//         ];
        
//         setTemplates(mockTemplates);

//         if (mockTemplates.length === 0) {
//           toast.info('No email templates found');
//         } else {
//           // Select the first template by default
//           setSelectedTemplate(mockTemplates[0]);
//           setEditedTemplate(mockTemplates[0]);
//         }
//       } catch (error) {
//         console.error('Error fetching email templates:', error);
//         toast.error('Failed to load email templates');
//       } finally {
//         setIsLoading(false);
//       }
//     };
    
//     fetchTemplates();
//   }, []);
  
//   // Handle template selection
//   const handleSelectTemplate = (template: EmailTemplate) => {
//     setSelectedTemplate(template);
//     setEditedTemplate(template);
//     setShowPreview(false);
//   };
  
//   // Handle template edit
//   const handleEditTemplate = (field: keyof EmailTemplate, value: string) => {
//     if (!editedTemplate) return;
    
//     setEditedTemplate({
//       ...editedTemplate,
//       [field]: value,
//       lastModified: new Date().toISOString()
//     });
//   };
  
//   // Save template changes
//   const handleSaveTemplate = async () => {
//     if (!editedTemplate) return;
    
//     try {
//       setIsSaving(true);
      
//       // In a real implementation, this would call the API
//       // For now, we'll simulate a delay
//       await new Promise(resolve => setTimeout(resolve, 1000));
      
//       // Update the templates list
//       const updatedTemplates = templates.map(template => 
//         template.id === editedTemplate.id ? editedTemplate : template
//       );
      
//       setTemplates(updatedTemplates);
//       setSelectedTemplate(editedTemplate);
      
//       toast.success('Email template saved successfully');
//     } catch (error) {
//       console.error('Error saving email template:', error);
//       toast.error('Failed to save email template');
//     } finally {
//       setIsSaving(false);
//     }
//   };
  
//   // Delete template
//   const handleDeleteTemplate = async () => {
//     if (!selectedTemplate) return;
    
//     try {
//       setIsSaving(true);
      
//       // In a real implementation, this would call the API
//       // For now, we'll simulate a delay
//       await new Promise(resolve => setTimeout(resolve, 1000));
      
//       // Remove the template from the list
//       const updatedTemplates = templates.filter(template => template.id !== selectedTemplate.id);
      
//       setTemplates(updatedTemplates);
//       setSelectedTemplate(updatedTemplates.length > 0 ? updatedTemplates[0] : null);
//       setEditedTemplate(updatedTemplates.length > 0 ? updatedTemplates[0] : null);
//       setShowDeleteConfirm(false);
      
//       toast.success('Email template deleted successfully');
//     } catch (error) {
//       console.error('Error deleting email template:', error);
//       toast.error('Failed to delete email template');
//     } finally {
//       setIsSaving(false);
//     }
//   };
  
//   // Create new template
//   const handleCreateTemplate = async () => {
//     try {
//       setIsSaving(true);
      
//       // In a real implementation, this would call the API
//       // For now, we'll simulate a delay
//       await new Promise(resolve => setTimeout(resolve, 1000));
      
//       // Create a new template object
//       const newTemplateObj: EmailTemplate = {
//         id: `template-${Date.now()}`,
//         name: newTemplate.name || 'New Template',
//         subject: newTemplate.subject || '',
//         body: newTemplate.body || '',
//         description: newTemplate.description || '',
//         type: newTemplate.type as EmailTemplate['type'] || 'custom',
//         lastModified: new Date().toISOString(),
//         variables: variablesByType[newTemplate.type as keyof typeof variablesByType] || []
//       };
      
//       // Add the new template to the list
//       const updatedTemplates = [...templates, newTemplateObj];
      
//       setTemplates(updatedTemplates);
//       setSelectedTemplate(newTemplateObj);
//       setEditedTemplate(newTemplateObj);
//       setShowCreateModal(false);
//       setNewTemplate({
//         name: '',
//         subject: '',
//         body: '',
//         description: '',
//         type: 'custom',
//         variables: []
//       });
      
//       toast.success('New email template created successfully');
//     } catch (error) {
//       console.error('Error creating email template:', error);
//       toast.error('Failed to create email template');
//     } finally {
//       setIsSaving(false);
//     }
//   };
  
//   // Duplicate template
//   const handleDuplicateTemplate = () => {
//     if (!selectedTemplate) return;
    
//     const duplicatedTemplate: EmailTemplate = {
//       ...selectedTemplate,
//       id: `template-${Date.now()}`,
//       name: `${selectedTemplate.name} (Copy)`,
//       lastModified: new Date().toISOString()
//     };
    
//     const updatedTemplates = [...templates, duplicatedTemplate];
    
//     setTemplates(updatedTemplates);
//     setSelectedTemplate(duplicatedTemplate);
//     setEditedTemplate(duplicatedTemplate);
    
//     toast.success('Email template duplicated successfully');
//   };
  
//   // Insert variable into template body
//   const handleInsertVariable = (variable: string) => {
//     if (!editedTemplate) return;
    
//     const textArea = document.getElementById('template-body') as HTMLTextAreaElement;
//     if (!textArea) return;
    
//     const start = textArea.selectionStart;
//     const end = textArea.selectionEnd;
//     const text = editedTemplate.body;
//     const before = text.substring(0, start);
//     const after = text.substring(end, text.length);
    
//     const newBody = before + variable + after;
    
//     setEditedTemplate({
//       ...editedTemplate,
//       body: newBody
//     });
    
//     // Set focus back to the textarea
//     setTimeout(() => {
//       textArea.focus();
//       textArea.setSelectionRange(start + variable.length, start + variable.length);
//     }, 0);
//   };
  
//   return (
//     <div className="min-h-screen bg-gray-50 py-8">
//       <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
//         <div className="mb-6">
//           <button
//             onClick={() => navigate('/institution-dashboard')}
//             className="flex items-center text-blue-600 hover:text-blue-800"
//           >
//             <ChevronLeft className="h-5 w-5 mr-1" />
//             Back to Dashboard
//           </button>
//         </div>
        
//         <div className="bg-white rounded-lg shadow-md">
//           <div className="flex justify-between items-center p-6 border-b">
//             <div>
//               <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
//               <p className="text-gray-600 mt-1">
//                 Manage email templates for campaign approvals and notifications
//               </p>
//             </div>
            
//             <button
//               onClick={() => setShowCreateModal(true)}
//               className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
//             >
//               <Plus className="h-4 w-4 mr-2" />
//               New Template
//             </button>
//           </div>
          
//           {isLoading ? (
//             <div className="flex justify-center items-center py-12">
//               <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
//               <span className="ml-3 text-gray-600">Loading templates...</span>
//             </div>
//           ) : (
//             <div className="flex flex-col md:flex-row">
//               {/* Template List */}
//               <div className="w-full md:w-1/3 border-r">
//                 <div className="p-4 border-b">
//                   <h2 className="font-semibold text-gray-700">Template Library</h2>
//                 </div>
//                 <div className="overflow-y-auto max-h-[calc(100vh-250px)]">
//                   {templates.length === 0 ? (
//                     <div className="p-4 text-center text-gray-500">
//                       No templates found
//                     </div>
//                   ) : (
//                     templates.map(template => (
//                       <div
//                         key={template.id}
//                         className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
//                           selectedTemplate?.id === template.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
//                         }`}
//                         onClick={() => handleSelectTemplate(template)}
//                     >
//                       <div className="flex items-start justify-between">
//                         <div>
//                           <h3 className="font-medium text-gray-900">{template.name}</h3>
//                           <p className="text-sm text-gray-500 mt-1">{template.description}</p>
//                           <div className="flex items-center mt-2">
//                             <span className={`px-2 py-0.5 text-xs rounded-full ${
//                               template.type === 'approval' ? 'bg-green-100 text-green-800' :
//                               template.type === 'rejection' ? 'bg-red-100 text-red-800' :
//                               template.type === 'reminder' ? 'bg-yellow-100 text-yellow-800' :
//                               template.type === 'notification' ? 'bg-blue-100 text-blue-800' :
//                               'bg-gray-100 text-gray-800'
//                             }`}>
//                               {template.type.charAt(0).toUpperCase() + template.type.slice(1)}
//                             </span>
//                             <span className="text-xs text-gray-500 ml-2">
//                               {new Date(template.lastModified).toLocaleDateString()}
//                             </span>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   ))}
//                   )}
//                 </div>
//               </div>
              
//               {/* Template Editor */}
//               {selectedTemplate && editedTemplate ? (
//                 <div className="w-full md:w-2/3 p-6">
//                   <div className="flex justify-between items-center mb-6">
//                     <div className="flex items-center space-x-4">
//                       <button
//                         onClick={() => setShowPreview(!showPreview)}
//                         className={`flex items-center px-3 py-1.5 rounded-md ${
//                           showPreview 
//                             ? 'bg-gray-200 text-gray-800' 
//                             : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
//                         }`}
//                       >
//                         <Eye className="h-4 w-4 mr-1" />
//                         {showPreview ? 'Edit' : 'Preview'}
//                       </button>
                      
//                       <button
//                         onClick={handleDuplicateTemplate}
//                         className="flex items-center px-3 py-1.5 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200"
//                       >
//                         <Copy className="h-4 w-4 mr-1" />
//                         Duplicate
//                       </button>
                      
//                       <button
//                         onClick={() => setShowDeleteConfirm(true)}
//                         className="flex items-center px-3 py-1.5 bg-gray-100 text-red-600 rounded-md hover:bg-red-50"
//                       >
//                         <Trash2 className="h-4 w-4 mr-1" />
//                         Delete
//                       </button>
//                     </div>
                    
//                     <button
//                       onClick={handleSaveTemplate}
//                       disabled={isSaving || JSON.stringify(selectedTemplate) === JSON.stringify(editedTemplate)}
//                       className={`flex items-center px-4 py-2 rounded-md ${
//                         isSaving || JSON.stringify(selectedTemplate) === JSON.stringify(editedTemplate)
//                           ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
//                           : 'bg-blue-600 text-white hover:bg-blue-700'
//                       }`}
//                     >
//                       {isSaving ? (
//                         <>
//                           <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
//                           Saving...
//                         </>
//                       ) : (
//                         <>
//                           <Save className="h-4 w-4 mr-2" />
//                           Save Changes
//                         </>
//                       )}
//                     </button>
//                   </div>
                  
//                   {showPreview ? (
//                     <div className="border rounded-lg p-6">
//                       <div className="bg-gray-50 p-4 rounded-md mb-4">
//                         <p className="text-sm text-gray-600">
//                           <strong>To:</strong> <EMAIL>
//                         </p>
//                         <p className="text-sm text-gray-600">
//                           <strong>Subject:</strong> {editedTemplate.subject}
//                         </p>
//                       </div>
                      
//                       <div className="bg-white p-4 rounded border">
//                         <div 
//                           dangerouslySetInnerHTML={{ __html: editedTemplate.body }}
//                           className="prose max-w-none"
//                         />
//                       </div>
//                     </div>
//                   ) : (
//                     <div className="space-y-6">
//                       <div>
//                         <label htmlFor="template-name" className="block text-sm font-medium text-gray-700 mb-1">
//                           Template Name
//                         </label>
//                         <input
//                           type="text"
//                           id="template-name"
//                           value={editedTemplate.name}
//                           onChange={(e) => handleEditTemplate('name', e.target.value)}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                         />
//                       </div>
                      
//                       <div>
//                         <label htmlFor="template-description" className="block text-sm font-medium text-gray-700 mb-1">
//                           Description
//                         </label>
//                         <input
//                           type="text"
//                           id="template-description"
//                           value={editedTemplate.description}
//                           onChange={(e) => handleEditTemplate('description', e.target.value)}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                         />
//                       </div>
                      
//                       <div>
//                         <label htmlFor="template-subject" className="block text-sm font-medium text-gray-700 mb-1">
//                           Email Subject
//                         </label>
//                         <input
//                           type="text"
//                           id="template-subject"
//                           value={editedTemplate.subject}
//                           onChange={(e) => handleEditTemplate('subject', e.target.value)}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                         />
//                       </div>
                      
//                       <div>
//                         <div className="flex justify-between items-center mb-1">
//                           <label htmlFor="template-body" className="block text-sm font-medium text-gray-700">
//                             Email Body
//                           </label>
//                           <div className="text-sm text-gray-500">
//                             HTML supported
//                           </div>
//                         </div>
//                         <textarea
//                           id="template-body"
//                           value={editedTemplate.body}
//                           onChange={(e) => handleEditTemplate('body', e.target.value)}
//                           rows={12}
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
//                         />
//                       </div>
                      
//                       <div>
//                         <label className="block text-sm font-medium text-gray-700 mb-2">
//                           Available Variables
//                         </label>
//                         <div className="flex flex-wrap gap-2">
//                           {editedTemplate.variables.map(variable => (
//                             <button
//                               key={variable}
//                               onClick={() => handleInsertVariable(variable)}
//                               className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded hover:bg-gray-200"
//                             >
//                               {variable}
//                             </button>
//                           ))}
//                         </div>
//                         <p className="text-xs text-gray-500 mt-2">
//                           Click on a variable to insert it at the cursor position in the email body
//                         </p>
//                       </div>
//                     </div>
//                   )}
//                 </div>
//               ) : (
//                 <div className="w-full md:w-2/3 p-6 flex items-center justify-center">
//                   <div className="text-center">
//                     <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
//                     <h3 className="text-lg font-medium text-gray-900">No template selected</h3>
//                     <p className="mt-1 text-gray-500">
//                       Select a template from the list or create a new one
//                     </p>
//                   </div>
//                 </div>
//               )}
//             </div>
//           )}
//         </div>
//       </div>
      
//       {/* Delete Confirmation Modal */}
//       {showDeleteConfirm && selectedTemplate && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
//           <div className="bg-white rounded-lg p-6 w-full max-w-md">
//             <div className="flex items-start mb-4">
//               <div className="flex-shrink-0">
//                 <AlertTriangle className="h-6 w-6 text-red-600" />
//               </div>
//               <div className="ml-3">
//                 <h3 className="text-lg font-medium text-gray-900">Delete Template</h3>
//                 <p className="mt-1 text-sm text-gray-500">
//                   Are you sure you want to delete the template "{selectedTemplate.name}"? This action cannot be undone.
//                 </p>
//               </div>
//             </div>
            
//             <div className="flex justify-end space-x-3">
//               <button
//                 onClick={() => setShowDeleteConfirm(false)}
//                 className="px-4 py-2 text-gray-700 border rounded-md hover:bg-gray-50"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleDeleteTemplate}
//                 className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
//               >
//                 {isSaving ? 'Deleting...' : 'Delete Template'}
//               </button>
//             </div>
//           </div>
//         </div>
//       )}
      
//       {/* Create Template Modal */}
//       {showCreateModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
//           <div className="bg-white rounded-lg p-6 w-full max-w-lg">
//             <div className="flex justify-between items-start mb-4">
//               <h3 className="text-lg font-medium text-gray-900">Create New Template</h3>
//               <button
//                 onClick={() => setShowCreateModal(false)}
//                 className="text-gray-400 hover:text-gray-500"
//               >
//                 <X className="h-5 w-5" />
//               </button>
//             </div>
            
//             <div className="space-y-4">
//               <div>
//                 <label htmlFor="new-template-name" className="block text-sm font-medium text-gray-700 mb-1">
//                   Template Name
//                 </label>
//                 <input
//                   type="text"
//                   id="new-template-name"
//                   value={newTemplate.name}
//                   onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                   placeholder="e.g., Campaign Approval Notification"
//                 />
//               </div>
              
//               <div>
//                 <label htmlFor="new-template-description" className="block text-sm font-medium text-gray-700 mb-1">
//                   Description
//                 </label>
//                 <input
//                   type="text"
//                   id="new-template-description"
//                   value={newTemplate.description}
//                   onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                   placeholder="e.g., Sent to students when their campaign is approved"
//                 />
//               </div>
              
//               <div>
//                 <label htmlFor="new-template-type" className="block text-sm font-medium text-gray-700 mb-1">
//                   Template Type
//                 </label>
//                 <select
//                   id="new-template-type"
//                   value={newTemplate.type}
//                   onChange={(e) => setNewTemplate({
//                     ...newTemplate, 
//                     type: e.target.value as EmailTemplate['type'],
//                     variables: variablesByType[e.target.value as keyof typeof variablesByType] || []
//                   })}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                 >
//                   <option value="approval">Approval Notification</option>
//                   <option value="rejection">Rejection Notification</option>
//                   <option value="notification">General Notification</option>
//                   <option value="reminder">Approval Reminder</option>
//                   <option value="custom">Custom Tempalate</option>
//                 </select>
//               </div>
              
//               <div>
//                 <label htmlFor="new-template-subject" className="block text-sm font-medium text-gray-700 mb-1">
//                   Email Subject
//                 </label>
//                 <input
//                   type="text"
//                   id="new-template-subject"
//                   value={newTemplate.subject}
//                   onChange={(e) => setNewTemplate({...newTemplate, subject: e.target.value})}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//                   placeholder="e.g., Your Campaign Has Been Approved"
//                 />
//               </div>
//             </div>
            
//             <div className="mt-6 flex justify-end space-x-3">
//               <button
//                 onClick={() => setShowCreateModal(false)}
//                 className="px-4 py-2 text-gray-700 border rounded-md hover:bg-gray-50"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleCreateTemplate}
//                 disabled={!newTemplate.name || !newTemplate.subject}
//                 className={`px-4 py-2 rounded-md ${
//                   !newTemplate.name || !newTemplate.subject
//                     ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
//                     : 'bg-blue-600 text-white hover:bg-blue-700'
//                 }`}
//               >
//                 {isSaving ? 'Creating...' : 'Create Template'}
//               </button>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }

// export default EmailTemplatesPage;
