import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { ChevronRight, ChevronLeft, Upload, X, Plus } from 'lucide-react';
import { useStudentAuth } from '../context/StudentAuthContext';
import externalStudentService from '../services/externalStudentService';
import StatusPage from './StatusPage';

interface FormData {
  studentName: string;
  email: string;
  phoneNumber: string;
  institute: string;
  course: string;
  year: string;
  batch: string;
  studentId: string;
  department: string;
  campaignName: string;
  campaignDescription: string;
  targetAmount: string;
  startDate: string;
  endDate: string;
  story: string;
  milestones: string[];
  documents: FileList;
}

export function StudentRegistration() {
  const navigate = useNavigate();
  const { login } = useStudentAuth();
  const [step, setStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
const [startYear, setStartYear] = useState('');
const [endYear, setEndYear] = useState('');

  const [hasActiveCampaign, setHasActiveCampaign] = useState<boolean | null>(null);

useEffect(() => {
  const fetchCampaignStatus = async () => {
    try {
      const res = await externalStudentService.getStudentCampaignStatus();
      setHasActiveCampaign(res.hasActiveCampaign);
    } catch (error) {
      console.error("Error fetching campaign status", error);
      // If API fails, redirect to student login instead of allowing registration
      toast.error('Unable to verify student status. Please sign in first.');
      navigate('/student-login');
    }
  };

  fetchCampaignStatus();
}, []);


  // Check if we have pre-filled data from Google login
  const [prefillData] = useState(() => {
    const storedData = localStorage.getItem('registration_prefill');
    if (storedData) {
      try {
        // Get the data but don't remove it yet (we'll remove it after successful registration)
        const data = JSON.parse(storedData);
        console.log('Pre-filled data from Google login:', data);
        return data;
      } catch (e) {
        console.error('Error parsing prefill data:', e);
      }
    }
    return null;
  });

  // Check if this is a Google sign-up
  const [isGoogleSignUp] = useState(() => {
    return !!localStorage.getItem('google_signup_email');
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors, isValid }
  } = useForm<FormData>({
    mode: 'onChange',
    defaultValues: {
      studentName: prefillData?.name || '',
      email: prefillData?.email || '',
      phoneNumber: '',
      institute: '',
      course: '',
      year: '',
      batch: '',
      studentId: '',
      department: '',
      campaignName: '',
      campaignDescription: '',
      targetAmount: '',
      startDate: '',
      endDate: '',
      story: '',
      milestones: [''],
      documents: undefined
    }
  });

  const watchMilestones = watch('milestones') || [''];
  // We're watching documents to trigger re-renders when they change
  watch('documents');

  const steps = [
    { number: 1, title: 'Personal Information' },
    { number: 2, title: 'Academic Details' },
    { number: 3, title: 'Campaign Information' }
  ];

  const handleAddMilestone = () => {
    if (watchMilestones.length < 5) {
      setValue('milestones', [...watchMilestones, '']);
    } else {
      toast.warning('Maximum 5 milestones allowed');
    }
  };

  const handleRemoveMilestone = (index: number) => {
    const newMilestones = watchMilestones.filter((_, i) => i !== index);
    setValue('milestones', newMilestones);
  };

  const handleMilestoneChange = (index: number, value: string) => {
    const newMilestones = [...watchMilestones];
    newMilestones[index] = value;
    setValue('milestones', newMilestones);
  };

  const handleNext = async () => {
    const isStepValid = await trigger();
    if (isStepValid) {
      setStep(step + 1);
      window.scrollTo(0, 0);
    }
  };

  const handlePrevious = () => {
    setStep(step - 1);
    window.scrollTo(0, 0);
  };

  const validateDates = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      return "Start date cannot be in the past";
    }
    if (end <= start) {
      return "End date must be after start date";
    }
    return true;
  };



    const onSubmit = async (data: any) => {
  try {
    setIsSubmitting(true);

    // ✅ Combine dropdown values into batch
    if (!startYear || !endYear) {
      toast.error('Please select both start and end years for batch');
      setIsSubmitting(false);
      return;
    }
    data.batch = `${startYear}-${endYear}`;

    console.log('Form Data:', data);


      // Check if this is a Google sign-up
      const googleEmail = localStorage.getItem('google_signup_email');

      // If this is a Google sign-up, use the Google email for registration
      const emailToRegister = googleEmail || data.email;

      // Hardcoded institution ID as specified
      // const institutionId = 'f4e2a72b-5b3c-42dc-bc3e-b9c7127abda7';

      // Create user and student in external API
      try {
        // Split the student name into first and last name
        const nameParts = data.studentName.split(' ');
        const firstName = nameParts[0];
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

        // Prepare user data
        const userData = {
          firstName: firstName,
          lastName: lastName,
          email: emailToRegister,
          password: 'Password123!' // Default password, should be changed by user later
        };

        // Prepare student data
        const studentData = {
          // institutionId: institutionId,
          studentRegId: data.studentId,
          course: data.course,
          department: data.department,
          year: data.year
        };

       console.log('User and student successfully created in external API');

      if (googleEmail) {
        try {
          console.log('Google sign-up detected. Registering via secure backend API...');
          await externalStudentService.registerStudent({
            studentRegId: data.studentId,
            course: data.course,
            department: data.department || 'General',
            year: data.year,
            // institutionId: institutionId,
            name: data.studentName,
            phoneNumber: data.phoneNumber,
            batch: data.batch
          });

          console.log('Creating campaign via secure backend API...');
          await externalStudentService.createStudentCampaign({
            title: data.campaignName,
            description: data.story,
            goalAmount: Number(data.targetAmount.replace(/,/g, '')),
            startDate: data.startDate,
            endDate: data.endDate,
            validDateRange: true
          });

          console.log('Secure backend registration and campaign creation successful.');
        } catch (apiError) {
          console.error('Error registering student or creating campaign:', apiError);
          const message = (apiError as any)?.response?.data?.message || 'Failed to register or create campaign. Please try again.';
          setErrorDetails({ message });
          setStatus('error');
          setIsSubmitting(false);
          return;
        }
      }

        console.log('User data:', userData);
        console.log('Student data:', studentData);

       

        console.log('User and student successfully created in external API');
      } catch (apiError) {
        console.error('Error creating user and student in external API:', apiError);
        // Continue with local registration even if API call fails
       
      }

      // Set flag in localStorage to indicate this student has registered
      const registrationKey = `student_registered_${emailToRegister}`;
      localStorage.setItem(registrationKey, 'true');
      console.log('Setting registration flag:', {
        email: emailToRegister,
        registrationKey,
        value: 'true',
        isGoogleSignUp: !!googleEmail
      });

      // Clean up localStorage
      localStorage.removeItem('registration_prefill');
      localStorage.removeItem('google_signup_email');

      // Log in the user using student auth context
      await login({ email: emailToRegister, password: 'dummy-password' });

      toast.success("Registration completed successfully!");

      // Short delay before navigation to show success message
      setTimeout(() => {
        navigate('/students', {
          state: {
            registrationSuccess: true,
            studentName: data.studentName
          }
        });
      }, 1500);
    } catch (error) {
      console.error('Registration error:', error);
      const message = (error as any)?.response?.data?.message || 'Registration failed. Please try again.';
      setErrorDetails({ message });
      setStatus('error');
      setIsSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="mb-8">
      <div className="flex justify-between items-center">
        {steps.map((s, idx) => (
          <div key={s.number} className="flex items-center">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full
              ${step >= s.number ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              {s.number}
            </div>
            <div className="ml-2 text-sm hidden md:block">{s.title}</div>
            {idx < steps.length - 1 && (
              <div className={`h-1 w-12 md:w-24 mx-2
                ${step > s.number ? 'bg-blue-600' : 'bg-gray-200'}`} />
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderPersonalInfo = () => {
    // Check if we have a Google email
    const googleEmail = localStorage.getItem('google_signup_email');

    return (
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Full Name</label>
          <input
            {...register('studentName', { required: 'Name is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter your full name"
          />
          {errors.studentName && (
            <p className="mt-1 text-sm text-red-600">{errors.studentName.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${googleEmail ? 'bg-gray-100' : ''}`}
              placeholder="<EMAIL>"
              readOnly={!!googleEmail}
            />
            {googleEmail && (
              <p className="mt-1 text-xs text-blue-600">This email is from your Google account</p>
            )}
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Phone Number</label>
            <input
              {...register('phoneNumber', {
                required: 'Phone number is required',
                pattern: {
                  value: /^\d{10}$/,
                  message: 'Please enter a valid 10-digit phone number'
                }
              })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Enter your phone number"
            />
            {errors.phoneNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderAcademicDetails = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Student ID</label>
        <input
          type="text"
          {...register('studentId', { required: 'Student ID is required' })}
          placeholder="Enter your student ID"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
        {errors.studentId && (
          <p className="mt-1 text-sm text-red-600">{errors.studentId.message}</p>
        )}
      </div>



     <div>
  <label className="block text-sm font-medium text-gray-700">Batch</label>
  <div className="flex gap-2">
    <select
      value={startYear}
      onChange={(e) => setStartYear(e.target.value)}
      className="mt-1 block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
    >
      <option value="">Start Year</option>
      {Array.from({ length: 10 }, (_, i) => {
        const year = new Date().getFullYear() - 5 + i;
        return (
          <option key={year} value={year}>
            {year}
          </option>
        );
      })}
    </select>
    <select
      value={endYear}
      onChange={(e) => setEndYear(e.target.value)}
      className="mt-1 block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
    >
      <option value="">End Year</option>
      {Array.from({ length: 10 }, (_, i) => {
        const year = new Date().getFullYear() - 5 + i;
        return (
          <option key={year} value={year}>
            {year}
          </option>
        );
      })}
    </select>
  </div>
  {(!startYear || !endYear) && (
    <p className="mt-1 text-sm text-red-600">Batch start and end year are required</p>
  )}
</div>


      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Course</label>
          <input
            {...register('course', { required: 'Course is required' })}
            type="text"
            list="course-options"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter course"
          />
          <datalist id="course-options">
            <option value="Computer Science" />
            <option value="Engineering" />
            <option value="Business" />
          </datalist>
          {errors.course && (
            <p className="mt-1 text-sm text-red-600">{errors.course.message}</p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Year</label>
          <select
            {...register('year', { required: 'Year is required' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Select year</option>
            <option value="1">First Year</option>
            <option value="2">Second Year</option>
            <option value="3">Third Year</option>
            <option value="4">Fourth Year</option>
          </select>
          {errors.year && (
            <p className="mt-1 text-sm text-red-600">{errors.year.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Department</label>
        <select
          {...register('department', { required: 'Department is required' })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">Select your department</option>
          <option value="Computer Science">Computer Science</option>
          <option value="Engineering">Engineering</option>
          <option value="Business">Business</option>
          <option value="Arts">Arts</option>
          <option value="Science">Science</option>
          <option value="General">General</option>
        </select>
        {errors.department && (
          <p className="mt-1 text-sm text-red-600">{errors.department.message}</p>
        )}
      </div>
    </div>
  );

  const formatWithCommas = (value: number | string) => {
    if (!value) return '';
    return Number(value).toLocaleString('en-IN');
  };

  const handleFormattedChange = (e: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const rawValue = e.target.value.replace(/,/g, '');
    const numericValue = Number(rawValue);

    if (isNaN(numericValue) || numericValue < 0) return;

    setValue(field as keyof FormData, numericValue.toString());
  };

 

  const renderCampaignInfo = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Campaign Name</label>
        <input
          {...register('campaignName', { required: 'Campaign name is required' })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          placeholder="Enter campaign name"
        />
        {errors.campaignName && (
          <p className="mt-1 text-sm text-red-600">{errors.campaignName.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Target Amount (₹)</label>
         <input
  type="text"
  inputMode="numeric"
  value={formatWithCommas(watch('targetAmount') || '')}
  onChange={(e) => handleFormattedChange(e, 'targetAmount')}
  className="w-full border rounded-lg px-3 py-2"
  required
/>
        {errors.targetAmount && (
          <p className="mt-1 text-sm text-red-600">{errors.targetAmount.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700">Start Date</label>
          <input
            type="date"
            {...register('startDate', {
              required: 'Start date is required',
              validate: (value) => {
                const endDate = watch('endDate');
                if (endDate) {
                  const result = validateDates(value, endDate);
                  return result === true ? true : result;
                }
                return true;
              }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            min={new Date().toISOString().split('T')[0]}
            max={watch('endDate') || undefined} // restrict start date not after end
          />
          {errors.startDate && (
            <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">End Date</label>
          <input
            type="date"
            {...register('endDate', {
              required: 'End date is required',
              validate: (value) => {
                const startDate = watch('startDate');
                if (startDate) {
                  const result = validateDates(startDate, value);
                  return result === true ? true : result;
                }
                return true;
              }
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            min={watch('startDate') || new Date().toISOString().split('T')[0]} // restrict end date not before start
          />
          {errors.endDate && (
            <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Description</label>
        <textarea
          {...register('story', {
            required: 'Description is required'
          })}
          rows={4}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          placeholder="Tell us about yourself and why you need support..."
        />
        {errors.story && (
          <p className="mt-1 text-sm text-red-600">{errors.story.message}</p>
        )}
      </div>

      {/* <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Campaign Milestones</label>
        <p className="text-sm text-gray-500">Add key milestones for your campaign</p>

        {watchMilestones.map((milestone, index) => (
          <div key={index} className="flex gap-2">
            <input
              value={milestone}
              onChange={(e) => handleMilestoneChange(index, e.target.value)}
              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder={`Milestone ${index + 1}`}
            />
            {index > 0 && (
              <button
                type="button"
                onClick={() => handleRemoveMilestone(index)}
                className="p-2 text-red-600 hover:text-red-800"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        ))}

        {watchMilestones.length < 5 && (
          <button
            type="button"
            onClick={handleAddMilestone}
            className="mt-2 inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Milestone
          </button>
        )}
      </div> */}

      <div>
        <label className="block text-sm font-medium text-gray-700">Supporting Documents</label>
        <p className="text-sm text-gray-500 mb-2">Upload relevant documents (admission letter, fee structure, etc.)</p>
        <input
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          {...register('documents', {
            required: 'At least one document is required'
          })}
          className="block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-md file:border-0
            file:text-sm file:font-medium
            file:bg-blue-50 file:text-blue-700
            hover:file:bg-blue-100"
        />
        {errors.documents && (
          <p className="mt-1 text-sm text-red-600">{errors.documents.message}</p>
        )}
      </div>
    </div>
  );

const renderStepContent = () => {
  switch (step) {
    case 1:
      return renderPersonalInfo();
    case 2:
      return renderAcademicDetails();
    case 3:
      if (hasActiveCampaign === false) {
        return renderCampaignInfo();
      } else if (hasActiveCampaign === true) {
        return (
          <p className="text-sm text-gray-700">
            You already have an active campaign. You can still register your details.
          </p>
        );
      } else {
        return <p>Checking campaign status...</p>;
      }
    default:
      return null;
  }
};

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Registration Failed"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }


  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white px-6 py-8 shadow-lg rounded-xl sm:px-10">
          <div className="mb-8 text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              Student Registration
            </h2>
            <p className="mt-2 text-gray-600">
              Complete your profile to start your fundraising journey
            </p>
          </div>

          {renderStepIndicator()}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              {renderStepContent()}
            </div>

            <div className="flex justify-between pt-5 border-t">
              {step > 1 && (
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  disabled={isSubmitting}
                >
                  <ChevronLeft className="h-5 w-5 mr-2" />
                  Previous
                </button>
              )}

              {step < steps.length ? (
                <button
                  type="button"
                  onClick={handleNext}
                  className="ml-auto inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  disabled={!isValid || isSubmitting}
                >
                  Next
                  <ChevronRight className="h-5 w-5 ml-2" />
                </button>
              ) : (
                <button
                  type="submit"
                  className="ml-auto inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  disabled={!isValid || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Submitting...
                    </>
                  ) : (
                    'Complete Registration'
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}






