import React, { useEffect, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import institutionService from '../services/institutionService';

export function InstitutionNavbar() {
  const auth = useAuth();
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogo = async () => {
      if (auth.isAuthenticated) {
        try {
          const logo = await institutionService.getInstitutionLogo(auth);
          if (logo) setLogoUrl(logo);
        } catch (error) {
          console.error('Failed to load institution logo:', error);
        }
      }
    };
    fetchLogo();
  }, [auth.isAuthenticated]);

  return (
    <nav className="bg-white shadow-sm mb-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* <span>Test navbar</span> */}
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center space-x-2">
            {logoUrl && (
              <img
                src={logoUrl}
                alt="Institution Logo"
                className="h-8 w-8 rounded-full object-cover"
              />
            )}
          </div>
          {auth.user && (
            <div className="text-right">
              <div className="font-medium text-gray-900">{auth.user.name}</div>
              <div className="text-sm text-gray-500">{auth.user.email}</div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

export default InstitutionNavbar;