import api from './api';

export interface DonationDetails {
  id: string;
  campaignId: string;
  userId: string;
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  transactionId: string;
  createdAt: string;
  updatedAt: string;
  studentName: string;
  institutionName: string;
  campaignTitle: string;
  progressPercentage: number;
}

const donationDetailsService = {
  /**
   * Get donation details by ID using the API endpoint from the curl example
   * @param id Donation ID
   * @returns Promise with donation details
   */
  getDonationDetails: async (id: string): Promise<DonationDetails> => {
    try {
      // Use the API endpoint from the curl example
      const response = await api.get(`/edu-fund-services/api/supporter/v1/donation/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching donation details:', error);
      throw error;
    }
  }
};

export default donationDetailsService;