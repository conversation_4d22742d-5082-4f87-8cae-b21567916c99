import api from './api';
import { Student } from './institutionService';

export interface StudentCreateRequest {
  firstName: string;
  lastName: string;
  email: string;
  institutionId: string;
  studentRegId: string;
  course: string;
  department: string;
  year: string;
  phone?: string;
  academicRecord?: string;
}

export interface StudentUpdateRequest {
  course?: string;
  department?: string;
  year?: string;
  phone?: string;
  academicRecord?: string;
}

/**
 * Service for managing students in the EduFund application
 */
const studentService = {
  /**
   * Get student by ID
   * @param id Student ID
   * @returns Promise with student details
   */
  getStudentById: async (id: string): Promise<Student> => {
    try {
      console.log(`Fetching student with ID: ${id}`);
      const response = await api.get(`/students/${id}`);
      console.log('Student response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching student:', error);
      throw error;
    }
  },

  /**
 * Check if the logged-in student exists and is active
 * @returns Promise with existence and status
 */
checkLoggedInStudent: async (): Promise<{ exists: boolean; status: string }> => {
  try {
    const response = await api.get('/api/student/v1/me/check');
    return response.data;
  } catch (error) {
    console.error('Error checking logged-in student:', error);
    throw error;
  }
},

  /**
   * Get all students
   * @returns Promise with array of students
   */
  getAllStudents: async (): Promise<Student[]> => {
    try {
      console.log('Fetching all students');
      const response = await api.get('/students');
      console.log('All students response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching all students:', error);
      return [];
    }
  },

  /**
   * Create a new student
   * @param studentData Student data
   * @returns Promise with created student
   */
  createStudent: async (studentData: StudentCreateRequest): Promise<Student> => {
    try {
      console.log('Creating new student with data:', studentData);
      const response = await api.post('/students', studentData);
      console.log('Create student response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating student:', error);
      throw error;
    }
  },

  /**
   * Update a student
   * @param id Student ID
   * @param studentData Updated student data
   * @returns Promise with updated student
   */
  updateStudent: async (id: string, studentData: StudentUpdateRequest): Promise<Student> => {
    try {
      console.log(`Updating student ${id} with data:`, studentData);
      const response = await api.put(`/students/${id}`, studentData);
      console.log('Update student response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating student:', error);
      throw error;
    }
  },

  /**
   * Delete a student
   * @param id Student ID
   * @returns Promise with deletion result
   */
  deleteStudent: async (id: string): Promise<any> => {
    try {
      console.log(`Deleting student with ID: ${id}`);
      const response = await api.delete(`/students/${id}`);
      console.log('Delete student response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error deleting student:', error);
      throw error;
    }
  },

  /**
   * Get students by institution ID with pagination
   * @param institutionId Institution ID
   * @param page Page number (0-based)
   * @param size Page size
   * @param sort Sort parameter (default: firstName,asc)
   * @returns Promise with array of students
   */
  getStudentsByInstitution: async (
    institutionId: string,
    page: number = 0,
    size: number = 20,
    sort: string = 'firstName,asc'
  ): Promise<Student[]> => {
    try {
      // Always use the hardcoded institution ID for testing
      const hardcodedId = 'ddb0d2a9-f150-4de6-8608-765b91b153e1';

      console.log(`Fetching students for institution ID: ${hardcodedId}, page: ${page}, size: ${size}, sort: ${sort}`);
      const response = await api.get(
        `/students/institution/${hardcodedId}?page=${page}&size=${size}&sort=${sort}`
      );

      console.log('Institution students response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching institution students:', error);
      return [];
    }
  },

  /**
   * Update student details
   * @param id Student ID
   * @param detailsData Updated student details
   * @returns Promise with updated student
   */
  updateStudentDetails: async (id: string, detailsData: {
    year?: string;
    phone?: string;
    department?: string;
    academicRecord?: string;
    course?: string;
    batch?: string;
  }): Promise<Student> => {
    try {
      console.log(`Updating details for student ${id} with data:`, detailsData);
      const response = await api.patch(`/students/${id}/details`, detailsData);
      console.log('Update student details response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating student details:', error);
      throw error;
    }
  }
};

export default studentService;
