import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserCircle2, Users, ChevronLeft } from 'lucide-react';

type UserType = 'student' | 'supporter';

export function AuthPage() {
  const navigate = useNavigate();
  const [hoveredButton, setHoveredButton] = useState<UserType | null>(null);

  // Check for direct logout flag and redirect if needed
  useEffect(() => {
    if (sessionStorage.getItem('direct_logout') === 'true') {
      console.log('AuthPage: Detected direct logout flag');

      // Check if there's a specific redirect path stored
      const redirectPath = sessionStorage.getItem('logout_redirect');

      // Clear the flags
      sessionStorage.removeItem('direct_logout');
      sessionStorage.removeItem('logout_redirect');

      // Redirect to the specified path or home page as fallback
      if (redirectPath) {
        console.log('Redirecting to:', redirectPath);
        navigate(redirectPath);
      } else {
        console.log('No redirect path specified, redirecting to home page');
        navigate('/');
      }
    }
  }, [navigate]);

  const handleBack = () => {
    navigate('/');
  };

  const handleUserTypeSelect = (type: UserType) => {
    switch (type) {
      case 'student':
        navigate('/student-login');
        break;
      case 'supporter':
        navigate('/supporter-login');
        break;
   
    }
  };

  const userTypes: { type: UserType; icon: React.ReactNode; label: string; color: string }[] = [
    {
      type: 'student',
      icon: <UserCircle2 className="h-6 w-6" />,
      label: 'Sign in as Student',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      type: 'supporter',
      icon: <Users className="h-6 w-6" />,
      label: 'Sign in as Supporter',
      color: 'bg-green-500 hover:bg-green-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <span className="ml-2 font-semibold text-xl">EDU-FUND</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-md mx-auto mt-16 px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Welcome to EDU-FUND</h1>
        <p className="text-gray-600 text-center mb-12">
          Choose your role to get started
        </p>

        <div className="space-y-4">
          {userTypes.map(({ type, icon, label, color }) => (
            <button
              key={type}
              onClick={() => handleUserTypeSelect(type)}
              onMouseEnter={() => setHoveredButton(type)}
              onMouseLeave={() => setHoveredButton(null)}
              className={`w-full flex items-center justify-between px-6 py-4 rounded-lg text-white ${color}
                transform transition-all duration-200 ${hoveredButton === type ? 'scale-105' : ''}`}
            >
              <div className="flex items-center">
                <span className="mr-3">{icon}</span>
                <span className="text-lg font-medium">{label}</span>
              </div>
              <svg
                className={`w-5 h-5 transform transition-transform duration-200 ${
                  hoveredButton === type ? 'translate-x-1' : ''
                }`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          ))}
        </div>

     
      </div>
    </div>
  );
}














