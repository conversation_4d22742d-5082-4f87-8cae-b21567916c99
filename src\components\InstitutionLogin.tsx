import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from 'react-oidc-context'; // using react-oidc-context
import { toast } from 'react-toastify';
import { ChevronLeft } from 'lucide-react';
import StatusPage from './StatusPage';
import { clearOidcState, isStateMismatchError, initiateFreshLogin } from '../utils/authUtils';


export function InstitutionLogin() {
  const navigate = useNavigate();
  const location = useLocation();
  const auth = useAuth();

  const [formData, setFormData] = useState({ email: '', password: '' });
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState({ email: '', password: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  // Check for password reset completion and other URL parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    
    if (params.get('password_reset_complete') === 'true') {
      console.log('Password reset completed, clearing state and showing login message');
      clearOidcState();
      toast.success('Password reset successful! Please sign in with your new password.');
      // Clean up URL
      navigate('/institution-login', { replace: true });
    }
    
    if (params.get('error') === 'session_expired') {
      setErrorDetails({ message: 'Your session has expired. Please sign in again.' });
      setStatus('error');
      navigate('/institution-login', { replace: true });
    }

    if (params.get('error') === 'state_mismatch') {
      setErrorDetails({ message: 'Authentication session expired. Please try signing in again.' });
      setStatus('error');
      navigate('/institution-login', { replace: true });
    }
  }, [location.search, navigate]);

  // Check if user is already authenticated
  useEffect(() => {
    console.log('InstitutionLogin - Auth state:', {
      isLoading: auth.isLoading,
      isAuthenticated: auth.isAuthenticated,
      hasUser: !!auth.user,
      error: auth.error
    });

    // Handle authentication errors
    if (auth.error) {
      console.error('OIDC authentication error:', auth.error);
      if (isStateMismatchError(auth.error)) {
        console.log('State mismatch detected, clearing state and showing retry option');
        clearOidcState();
        setErrorDetails({ message: 'Authentication session expired. Please try signing in again.' });
        setStatus('error');
      } else {
        setErrorDetails({ message: 'Authentication failed. Please try again.' });
        setStatus('error');
      }
    }

    if (!auth.isLoading && auth.isAuthenticated && auth.user) {
      console.log('User already authenticated, redirecting to dashboard...');
      navigate('/institution-dashboard');
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.user, auth.error, navigate]);

  const validateForm = () => {
    let valid = true;
    const newErrors = { email: '', password: '' };

    if (!formData.email) {
      newErrors.email = 'Email is required';
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
      valid = false;
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
      valid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);

  try {
    toast.success('Email-password login disabled. Use Cognito Sign-in.');
  } catch (error: any) {
      setErrorDetails({ message: 'Login failed' });
      setStatus('error');
  } finally {
    setIsLoading(false);
  }
  };
// const handleCognitoLogin = () => {
//   const COGNITO_DOMAIN = 'us-east-1xr0yffogk.auth.us-east-1.amazoncognito.com';
//   const CLIENT_ID = '42bvu1jm3cgnb91g659idno64v';
//   const REDIRECT_URI = encodeURIComponent('http://localhost:5173/auth/callback');

//    window.location.href = `${COGNITO_DOMAIN}/login?client_id=${CLIENT_ID}&response_type=code&scope=email+openid+profile&redirect_uri=${REDIRECT_URI}`;

// };



const handleCognitoLogin = async () => {
  console.log('Starting OIDC login...');
  console.log('Auth state before login:', {
    isLoading: auth.isLoading,
    isAuthenticated: auth.isAuthenticated,
    hasUser: !!auth.user
  });

  try {
    await initiateFreshLogin(auth);
  } catch (error) {
    console.error('OIDC login failed:', error);
    setErrorDetails({ message: 'Failed to start authentication. Please try again.' });
    setStatus('error');
  }
};



  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Login Failed"
        message={errorDetails?.message || ''}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex items-center justify-between p-4 bg-white shadow-sm">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/auth')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <span className="ml-2 font-semibold text-xl">EDU-FUND</span>
        </div>
      </div>

      <div className="max-w-md mx-auto mt-12 px-4">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-2">Welcome Back</h2>
            <p className="text-gray-600">Sign in to your institution account</p>
          </div>

          <form onSubmit={handleFormSubmit} className="space-y-6">
            <div className="space-y-4">
              <button
                type="button"
                onClick={handleCognitoLogin}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Sign in with AWS Cognito
              </button>
              <label className="flex items-center space-x-2 text-sm text-gray-600 mt-2">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <span>Remember me</span>
              </label>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
