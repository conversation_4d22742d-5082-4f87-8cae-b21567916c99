import React, { useEffect, useState } from 'react';
import { COGNITO_DOMAIN, COGNITO_CLIENT_ID, COGNITO_REDIRECT_URI, COGNITO_SIGNOUT_URI, COGNITO_RESPONSE_TYPE, COGNITO_SCOPE } from '../config/env';
import cognitoService from '../services/cognitoService';
import { useAuth } from '../context/AuthContext';

/**
 * CognitoTestPage is a diagnostic page to verify Cognito configuration
 * It displays the current Cognito configuration and provides buttons to test login/logout
 */
export function CognitoTestPage() {
  const { loginWithCognito } = useAuth();

  // Direct environment variables
  const [envVars, setEnvVars] = useState({
    VITE_COGNITO_DOMAIN: import.meta.env.VITE_COGNITO_DOMAIN || 'Not set',
    VITE_COGNITO_CLIENT_ID: import.meta.env.VITE_COGNITO_CLIENT_ID || 'Not set',
    VITE_COGNITO_REDIRECT_URI: import.meta.env.VITE_COGNITO_REDIRECT_URI || 'Not set',
    VITE_COGNITO_SIGNOUT_URI: import.meta.env.VITE_COGNITO_SIGNOUT_URI || 'Not set',
    VITE_COGNITO_RESPONSE_TYPE: import.meta.env.VITE_COGNITO_RESPONSE_TYPE || 'Not set',
    VITE_COGNITO_SCOPE: import.meta.env.VITE_COGNITO_SCOPE || 'Not set',
  });

  // Config variables from env.ts
  const [configVars, setConfigVars] = useState({
    COGNITO_DOMAIN,
    COGNITO_CLIENT_ID,
    COGNITO_REDIRECT_URI,
    COGNITO_SIGNOUT_URI,
    COGNITO_RESPONSE_TYPE,
    COGNITO_SCOPE
  });

  // Generated URLs
  const [authUrl, setAuthUrl] = useState('');
  const [logoutUrl, setLogoutUrl] = useState('');

  // Values used by cognitoService
  const [serviceVars, setServiceVars] = useState({
    domain: '',
    clientId: '',
    redirectUri: '',
    signoutUri: '',
    responseType: '',
    scope: ''
  });

  useEffect(() => {
    // Generate the authorization and logout URLs
    const generatedAuthUrl = cognitoService.buildAuthorizationUrl();
    const generatedLogoutUrl = cognitoService.buildLogoutUrl();

    setAuthUrl(generatedAuthUrl);
    setLogoutUrl(generatedLogoutUrl);

    // Extract the actual values used by the service
    try {
      // Parse domain from auth URL
      const authUrlObj = new URL(generatedAuthUrl);
      const domain = `${authUrlObj.protocol}//${authUrlObj.host}`;

      // Parse parameters from auth URL
      const authParams = new URLSearchParams(authUrlObj.search);
      const clientId = authParams.get('client_id') || '';
      const redirectUri = authParams.get('redirect_uri') || '';
      const responseType = authParams.get('response_type') || '';
      const scope = authParams.get('scope') || '';

      // Parse parameters from logout URL
      const logoutUrlObj = new URL(generatedLogoutUrl);
      const logoutParams = new URLSearchParams(logoutUrlObj.search);
      const signoutUri = logoutParams.get('logout_uri') || '';

      setServiceVars({
        domain,
        clientId,
        redirectUri,
        signoutUri,
        responseType,
        scope
      });
    } catch (error) {
      console.error('Error parsing URLs:', error);
    }
  }, []);

  return (
    <div className="min-h-screen p-8 bg-gray-50">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">Cognito Configuration Test</h1>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Environment Variables (from .env)</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(envVars, null, 2)}
          </pre>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Config Variables (from env.ts)</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(configVars, null, 2)}
          </pre>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Actual Values Used by cognitoService</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(serviceVars, null, 2)}
          </pre>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Generated URLs</h2>

          <div className="mb-4">
            <h3 className="font-medium mb-2">Authorization URL:</h3>
            <div className="bg-gray-100 p-4 rounded break-all">
              {authUrl}
            </div>
            <a
              href={authUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block mt-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
            >
              Open URL directly
            </a>
          </div>

          <div>
            <h3 className="font-medium mb-2">Logout URL:</h3>
            <div className="bg-gray-100 p-4 rounded break-all">
              {logoutUrl}
            </div>
            <a
              href={logoutUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block mt-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
            >
              Open URL directly
            </a>
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => cognitoService.redirectToLogin()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Login (cognitoService)
          </button>

          <button
            onClick={() => loginWithCognito(false)}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Test Login (AuthContext)
          </button>

          <button
            onClick={() => cognitoService.redirectToLogout()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Test Logout
          </button>
        </div>
      </div>
    </div>
  );
}

export default CognitoTestPage;
