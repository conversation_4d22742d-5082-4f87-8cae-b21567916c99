i have Institution Management
APIs for managing institutions

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/ddb0d2a9-f150-4de6-8608-765b91b153e1' \
  -H 'accept: */*'

curl -X 'PUT' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/398e8718-0251-46b2-be11-dec087748fd4' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "title": "string",
  "description": "string",
  "goalAmount": 0.01,
  "startDate": "2025-05-16",
  "endDate": "2025-05-16",
  "category": "string"
}'

 curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "string",
  "email": "string"
}'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/398e8718-0251-46b2-be11-dec087748fd4/bank-details' \
  -H 'accept: */*'

  curl -X 'PATCH' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/398e8718-0251-46b2-be11-dec087748fd9/bank-details' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "accountName": "string",
  "accountNumber": "string",
  "ifscCode": "string"
}'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/398e8718-0251-46b2-be11-dec087748fd4/stats' \
  -H 'accept: */*'

curl -X 'DELETE' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/398e8718-0251-46b2-be11-dec087748fd9?userId=398e8718-0251-46b2-be11-dec087748fd7' \
  -H 'accept: */*'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "title": "string",
  "description": "string",
  "goalAmount": 0.01,
  "studentId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "createdById": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "startDate": "2025-05-16",
  "endDate": "2025-05-16",
  "category": "string"
}'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/398e8718-0251-46b2-be11-dec087748fd9/reject?userId=398e8718-0251-46b2-be11-dec087748fd9' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "reason": "string"
}'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/398e8718-0251-46b2-be11-dec087748fd9/approve?userId=398e8718-0251-46b2-be11-dec087748fd9' \
  -H 'accept: */*' \
  -d ''

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/institution/398e8718-0251-46b2-be11-dec087748fd4?page=0&size=20&sort=createdAt%2Cdesc' \
  -H 'accept: */*'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/institution/398e8718-0251-46b2-be11-dec087748fd9' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "title": "string",
  "description": "string",
  "goalAmount": 0.01,
  "studentId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "createdById": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "startDate": "2025-05-16",
  "endDate": "2025-05-16",
  "category": "string"
}'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/student/398e8718-0251-46b2-be11-dec087748fd4?page=0&size=20&sort=createdAt%2Cdesc' \
  -H 'accept: */*'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/search?institutionId=398e8718-0251-46b2-be11-dec087748fd4&title=test&status=test&category=test&startDate=test&endDate=test&minAmount=293837&maxAmount=928339&page=35353&size=20&sort=createdAt%2Cdesc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/ddb0d2a9-f150-4de6-8608-765b91b15389' \
  -H 'accept: */*'

  curl -X 'PUT' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/ddb0d2a9-f150-4de6-8608-765b91b15389' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "name": "string",
  "address": "string",
  "email": "string",
  "phone": "string",
  "website": "string"
}'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/campaigns/3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*'



  NEW APIS



  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/activities/search?institutionId=3fa85f64-5717-4562-b3fc-2c963f66afa6&action=approve&userId=3fa85f64-5717-4562-b3fc-2c963f66afa6&startDate=2-2-25&endDate=2-2-25&page=0&size=20&sort=timestamp%2Cdesc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/activities/institution/3fa85f64-5717-4562-b3fc-2c963f66afa6?page=0&size=20&sort=timestamp%2Cdesc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/activities/institution/3fa85f64-5717-4562-b3fc-2c963f66afa6/recent?limit=10' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/students/3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*'

  curl -X 'PUT' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/students/3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "course": "string",
  "department": "string",
  "year": "string"
}'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/students' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "userId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "institutionId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "studentRegId": "string",
  "course": "string",
  "department": "string",
  "year": "string"
}'

curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/students/institution/3fa85f64-5717-4562-b3fc-2c963f66afa6?page=0&size=20&sort=firstName%2Casc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/search?institutionId=3fa85f64-5717-4562-b3fc-2c963f66afa6&requestType=TEST&status=TEST&startDate=2-2-25&endDate=2-2-25&studentName=test&campaignTitle=test&page=0&size=20&sort=requestedAt%2Cdesc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/institution/3fa85f64-5717-4562-b3fc-2c963f66afa6?page=0&size=20&sort=requestedAt%2Cdesc' \
  -H 'accept: */*'

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/institution/3fa85f64-5717-4562-b3fc-2c963f66afa6/pending?page=0&size=20&sort=requestedAt%2Cdesc' \
  -H 'accept: */*'


  curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/3fa85f64-5717-4562-b3fc-2c963f66afa6/reject-student?userId=3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "reason": "string"
}'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/3fa85f64-5717-4562-b3fc-2c963f66afa6/reject-campaign?userId=3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*' \
  -H 'Content-Type: application/json' \
  -d '{
  "reason": "string"
}'

curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/3fa85f64-5717-4562-b3fc-2c963f66afa6/approve-student?userId=3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*' \
  -d ''

  curl -X 'POST' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/approvals/3fa85f64-5717-4562-b3fc-2c963f66afa6/approve-campaign?userId=3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: */*' \
  -d ''

  curl -X 'GET' \
  'https://ashburn-rzs.icamxperts.com/edu-fund-services/institutions/email/naqhid%40icamxperts.com' \
  -H 'accept: */*'