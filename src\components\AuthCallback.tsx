import React, { useEffect, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import cognitoService from '../services/cognitoService';
import { toast } from 'react-toastify';
import StatusPage from './StatusPage';
import { COGNITO_DOMAIN } from '../config/env';

export function AuthCallback() {
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserFromCognito } = useAuth();
  const [loading, setLoading] = useState(true);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const hasHandledCallback = useRef(false); // ✅ This replaces hasRun

  useEffect(() => {
    if (hasHandledCallback.current) return;
    hasHandledCallback.current = true;

    const handleCallback = async () => {
      try {
        console.log('AuthCallback - Cognito Configuration:', {
          COGNITO_DOMAIN,
          currentUrl: window.location.href,
          search: location.search
        });

        const params = new URLSearchParams(location.search);
        const code = params.get('code');
        const error = params.get('error');
        const errorDescription = params.get('error_description');

        if (error) {
          console.error('Authentication error:', error, errorDescription);
          setErrorMessage(errorDescription || 'Authentication failed');
          setStatus('error');
          return;
        }

        if (!code) {
          console.error('No authorization code found in the URL');
          setErrorMessage('No authorization code found');
          setStatus('error');
          return;
        }

        console.log('Exchanging code for tokens...');
        const tokens = await cognitoService.exchangeCodeForTokens(code);
        cognitoService.saveTokens(tokens, true);

        console.log('Parsing user info from ID token...');
        const userInfo = cognitoService.parseJwt(tokens.id_token);
        setUserFromCognito(userInfo);

        // No longer calling welcome and roles APIs here
        // They will be called from the dashboard component

        toast.success('Successfully signed in!');
        navigate('/institution-dashboard');
      } catch (err) {
        console.error('Error processing authentication callback:', err);
        setErrorMessage('Failed to process authentication response');
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [location, navigate, setUserFromCognito]);

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Authentication Error"
        message={errorMessage || ''}
        actionText="Back to Login"
        backUrl="/institution-login"
        showHeader={false}
      />
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Signing you in...</h2>

          {loading && (
            <div className="flex justify-center my-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          )}

          <p className="text-gray-600">Please wait while we complete the authentication process.</p>
        </div>
      </div>
    </div>
  );
}

export default AuthCallback;

