// import { useState, useEffect } from 'react';
// import { useParams, useNavigate } from 'react-router-dom';
// import { ChevronLeft, FileText, X, Check, AlertTriangle, Calendar, Download, ExternalLink, GraduationCap } from 'lucide-react';
// import { toast } from 'react-toastify';
// import {
//   institutionService,
//   approvalService,
//   activityService,
//   ApprovalRequest,
//   Student,
//   Campaign
// } from '../services';
// import { useAuth } from 'react-oidc-context';
// import NotificationPreview from '../components/NotificationPreview';
// import Breadcrumb from '../components/Breadcrumb';
// import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
// import StatusPage from '../components/StatusPage';
// import InstitutionSidebar from '../components/InstitutionSidebar';


// // Extended type that combines data from multiple API endpoints
// type DetailedApproval = {
//   // Base approval request data
//   id: number;
//   studentId: number;
//   campaignId: number;
//   status: string;
//   amount: number;
//   submittedDate: string;
//   approvedBy?: string;
//   approvedDate?: string;
//   documents: string[];

//   // Derived fields for UI display
//   student: string;
//   campaign: string;

//   // Additional details fetched separately
//   studentDetails: {
//     email: string;
//     studentId: string;
//     course: string;
//     year: string;
//     phone: string;
//     batch?: string;
//   };
//   campaignDetails: {
//     description: string;
//     purpose: string;
//     duration: string;
//     milestones: string[];
//     expectedOutcome: string;
//     startDate: string;
//     endDate: string;
//   };
// };

// interface RolePermissions {
//   manageCampaigns: boolean;
//   manageApprovals: boolean;
//   manageInstitutionProfile: boolean;
//   manageBankDetails: boolean;
//   manageUsers: boolean;
//   downloadReports: boolean;
// }

// export function ApprovalRequestDetails() {
//   const { id } = useParams();
//    const auth = useAuth();
//   const navigate = useNavigate();
//   const { user } = useAuth();
//   const [approval, setApproval] = useState<DetailedApproval | null>(null);
//   const [showNotificationPreview, setShowNotificationPreview] = useState(false);
//   const [rejectionReason, setRejectionReason] = useState('');
//   const [loading, setLoading] = useState(true);
//   const [showConfirmDialog, setShowConfirmDialog] = useState(false);
//   const [actionType, setActionType] = useState<'approved' | 'rejected' | null>(null);
//   const [error, setError] = useState<string | null>(null);
//   const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
//   const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);

//   const [sidebarOpen, setSidebarOpen] = useState(true);

//   const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

//   const handleSignOut = () => {
//     localStorage.removeItem('token');
//     localStorage.removeItem('user');
//     localStorage.removeItem('id_token');
//     sessionStorage.removeItem('token');
//     sessionStorage.removeItem('user');
//     sessionStorage.removeItem('id_token');
//     auth.logout('/');
//     toast.success('Successfully signed out');
//     setTimeout(() => {
//       window.location.href = '${import.meta.env.VITE_BASE_PATH}/';
//     }, 100);
//   };
//   const [permissions, setPermissions] = useState<RolePermissions>({
//     manageCampaigns: false,
//     manageApprovals: false,
//     manageInstitutionProfile: false,
//     manageBankDetails: false,
//     manageUsers: false,
//     downloadReports: false
//   });

//   useEffect(() => {
//     // Fetch user roles and set permissions
//     // Try the simple string format first, then fall back to array format
//     const storedRole = localStorage.getItem('institution_role');
//     const storedRoles = localStorage.getItem('institution_roles');

//     let userRole = '';
//     if (storedRole) {
//       userRole = storedRole;
//     } else if (storedRoles) {
//       try {
//         const userRolesList = JSON.parse(storedRoles);
//         if (Array.isArray(userRolesList) && userRolesList.length > 0) {
//           userRole = userRolesList[0];
//         }
//       } catch (e) {
//         console.error('Error parsing stored roles:', e);
//       }
//     }

//     if (userRole) {
//       const newPermissions: RolePermissions = {
//         manageCampaigns: false,
//         manageApprovals: false,
//         manageInstitutionProfile: false,
//         manageBankDetails: false,
//         manageUsers: false,
//         downloadReports: false
//       };

//       if (userRole === 'INSTITUTION_ADMIN') {
//         // Institution Admin has all permissions
//         newPermissions.manageCampaigns = true;
//         newPermissions.manageApprovals = true;
//         newPermissions.manageInstitutionProfile = true;
//         newPermissions.manageBankDetails = true;
//         newPermissions.manageUsers = true;
//         newPermissions.downloadReports = true;
//       } else if (userRole === 'FINANCIAL_ADMIN') {
//         // Financial Admin has all except institution profile and user management
//         newPermissions.manageCampaigns = true;
//         newPermissions.manageApprovals = true;
//         newPermissions.manageBankDetails = true;
//         newPermissions.downloadReports = true;
//       }
//       // CLERK role gets no permissions (all remain false)

//       setPermissions(newPermissions);
//     }

//     if (id) {
//       fetchApprovalDetails(parseInt(id));
//     } else {
//       setError("No approval ID provided");
//       setLoading(false);
//     }
//   }, [id]);

//   const fetchApprovalDetails = async (approvalId: number) => {
//     setLoading(true);
//     setError(null);

//     try {
//       // Fetch the basic approval request data using the new approvalService
//       const approvalData = await approvalService.getApprovalById(approvalId.toString());

//       if (!approvalData || !approvalData.id) {
//         setError("Approval request not found");
//         setLoading(false);
//         return;
//       }

//       // Start building our detailed approval object
//       const detailedApproval: DetailedApproval = {
//         // Base approval data
//         id: approvalData.id,
//         studentId: approvalData.studentId,
//         campaignId: approvalData.campaignId,
//         status: approvalData.status,
//         amount: approvalData.amount,
//         submittedDate: approvalData.submittedDate,
//         approvedBy: approvalData.approvedBy,
//         approvedDate: approvalData.approvedDate,
//         documents: approvalData.documents || [],

//         // Default values for derived fields
//         student: approvalData.studentName,
//         campaign: approvalData.campaignTitle,

//         // Default values for additional details
//         studentDetails: {
//           email: "",
//           studentId: "",
//           course: "",
//           year: "",
//           phone: "",
//           batch: ""
//         },
//         campaignDetails: {
//           description: "",
//           purpose: "",
//           duration: "",
//           milestones: [],
//           expectedOutcome: "",
//           startDate: "",
//           endDate: ""
//         }
//       };

//       // Fetch student details
//       try {
//         if (user?.profileId) {
//           const students = await institutionService.getInstitutionStudents(user.profileId);
//           const studentData = students.find(s => s.id === approvalData.studentId);

//           if (studentData) {
//             detailedApproval.studentDetails = {
//               email: studentData.email || "",
//               studentId: studentData.studentId || "",
//               course: studentData.course || "",
//               year: studentData.year || "",
//               phone: studentData.phone || "",
//               batch: `${studentData.year} ${studentData.course}`
//             };
//           }
//         }
//       } catch (studentError) {
//         console.error("Error fetching student details:", studentError);
//         // Continue with partial data
//       }

//       // Fetch campaign details
//       try {
//         if (user?.profileId) {
//           const campaigns = await institutionService.getInstitutionCampaigns(user.profileId);
//           const campaignData = campaigns.find(c => c.id === approvalData.campaignId);

//           if (campaignData) {
//             // Calculate duration in months
//             const startDate = new Date(campaignData.startDate);
//             const endDate = new Date(campaignData.endDate);
//             const durationInMonths = Math.ceil(
//               (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30)
//             );

//             detailedApproval.campaignDetails = {
//               description: campaignData.description || "",
//               purpose: "To support educational needs", // Default purpose
//               duration: `${durationInMonths} months`,
//               milestones: [
//                 "Planning and preparation",
//                 "Implementation",
//                 "Evaluation and reporting"
//               ], // Default milestones
//               expectedOutcome: "Successful completion of educational project",
//               startDate: campaignData.startDate,
//               endDate: campaignData.endDate
//             };
//           }
//         }
//       } catch (campaignError) {
//         console.error("Error fetching campaign details:", campaignError);
//         // Continue with partial data
//       }

//       setApproval(detailedApproval);
//     } catch (error) {
//       console.error("Error fetching approval details:", error);
//       setError("Failed to fetch approval details. Please try again later.");
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleApproval = async (status: 'approved' | 'rejected') => {
//     if (!approval) return;

//     // For approved status, proceed directly with empty reason
//     if (status === 'approved') {
//       try {
//         setLoading(true);

//        await approvalService.updateApprovalRequest(
//   approval.id.toString(),
//   {
//     id: approval.id.toString(),
//     status: 'ACTIVE',
//     reason: '' // Empty reason for approved status
//   },
//   auth // ✅ required to generate the token
// );


//         setApproval({ ...approval, status: 'approved' });
//         setStatus('success');
//       } catch (error: any) {
//         console.error('Error approving campaign:', error);
//         const apiMessage = error.response?.data?.message || '';
//         const apiData = error.response?.data?.data || {};
//         let errorMessage = apiMessage;
//         if (Object.keys(apiData).length > 0) {
//           const validationErrors = Object.entries(apiData)
//             .map(([field, error]) => `• ${field}: ${error}`)
//             .join('\n');
//           errorMessage += '\n' + validationErrors;
//         }
//         setErrorDetails({ message: errorMessage });
//         setStatus('error');
//       } finally {
//         setLoading(false);
//       }
//       return;
//     }

//     // For rejected status, show dialog to collect reason
//     setActionType(status);
//     setShowConfirmDialog(true);
//   };

//   const confirmAction = async () => {
//     if (!approval || actionType !== 'rejected') return;

//     if (rejectionReason.trim() === '') {
//       toast.error("Please provide a reason for rejection");
//       return;
//     }

//     try {
//       setLoading(true);

//       // Call the API to update the approval status for rejection
//       await approvalService.updateApprovalRequest(approval.id.toString(), {
//         id: approval.id.toString(),
//         status: 'REJECTED',
//         reason: rejectionReason.trim(),
//       });

//       // Update the local state
//       setApproval({ ...approval, status: 'rejected' });
//       setStatus('success');
//       setShowConfirmDialog(false);
//       setRejectionReason('');
//     } catch (error: any) {
//       console.error('Error rejecting campaign:', error);
//       const apiMessage = error.response?.data?.message || '';
//       const apiData = error.response?.data?.data || {};
//       let errorMessage = apiMessage;
//       if (Object.keys(apiData).length > 0) {
//         const validationErrors = Object.entries(apiData)
//           .map(([field, error]) => `• ${field}: ${error}`)
//           .join('\n');
//         errorMessage += '\n' + validationErrors;
//       }
//       setErrorDetails({ message: errorMessage });
//       setStatus('error');
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Removed unused handleSignOut function

//   if (loading) {
//     return (
//       <div className="min-h-screen flex items-center justify-center bg-gray-50">
//         <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
//       </div>
//     );
//   }

//   if (error) {
//     return (
//       <div className="min-h-screen flex items-center justify-center bg-gray-50">
//         <div className="text-center">
//           <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
//           <h2 className="text-xl font-semibold text-gray-900">Error</h2>
//           <p className="mt-2 text-gray-600">{error}</p>
//           <button
//             onClick={() => navigate('/institution-dashboard')}
//             className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
//           >
//             Return to Dashboard
//           </button>
//         </div>
//       </div>
//     );
//   }

//   if (!approval && !loading) {
//     return (
//       <div className="min-h-screen flex items-center justify-center bg-gray-50">
//         <div className="text-center">
//           <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
//           <h2 className="text-xl font-semibold text-gray-900">Approval Request Not Found</h2>
//           <p className="mt-2 text-gray-600">The requested approval details could not be found.</p>
//           <button
//             onClick={() => navigate('/institution-dashboard')}
//             className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
//           >
//             Return to Dashboard
//           </button>
//         </div>
//       </div>
//     );
//   }

//   if (status === 'success') {
//     return (
//       <StatusPage
//         type="success"
//         title={`Campaign ${actionType === 'approved' ? 'Approved' : 'Rejected'} Successfully`}
//         message={`The campaign request has been ${actionType === 'approved' ? 'approved' : 'rejected'} and the student will be notified.`}
//         actionText="Back to Approvals"
//         backUrl="/institution-dashboard?tab=approvalRequests"
//       />
//     );
//   }

//   if (status === 'error') {
//     return (
//       <StatusPage
//         type="error"
//         title="Error"
//         message={errorDetails?.message || ""}
//         actionText="Try Again"
//         onAction={() => {
//           setStatus('idle');
//           setErrorDetails(null);
//         }}
//       />
//     );
//   }

//   // We've already checked for loading and error states, so approval should exist here
//   // But we'll add a type assertion to satisfy TypeScript
//   const approvalData = approval as DetailedApproval;

//   return (
//     <div className="flex">
//       <InstitutionSidebar
//         sidebarOpen={sidebarOpen}
//         toggleSidebar={toggleSidebar}
//         activeTab="approvalRequests"
//         onSignOut={handleSignOut}
//       />
//       <div className="flex-1 min-h-screen flex flex-col bg-gray-50">
//         {/* Main Content */}
//         <div className="flex-1 py-8">
//         <div className="max-w-4xl mx-auto px-4">
//           <div className="mb-6">
//             <button
//               onClick={() => navigate('/institution-dashboard?tab=approvalRequests')}
//               className="flex items-center text-gray-600 hover:text-gray-900"
//             >
//               <ChevronLeft className="h-5 w-5 mr-2" />
//               Back to Approval Requests
//             </button>
//           </div>

//           {/* Breadcrumb */}
//           <Breadcrumb
//             items={getBreadcrumbItems('approvalRequestDetails', {
//               approvalId: id,
//               approvalTitle: `Request #${id}`
//             })}
//           />

//           <div className="bg-white rounded-lg shadow-lg p-6">
//             <div className="border-b pb-4 mb-6">
//               <h1 className="text-2xl font-bold text-gray-900">Campaign Approval Request</h1>
//               <div className="mt-2 flex flex-wrap items-center">
//                 <span className={`px-3 py-1 rounded-full text-sm font-medium ${
//                   approvalData.status === 'approved' ? 'bg-green-100 text-green-800' :
//                   approvalData.status === 'rejected' ? 'bg-red-100 text-red-800' :
//                   'bg-yellow-100 text-yellow-800'
//                 }`}>
//                   {approvalData.status.charAt(0).toUpperCase() + approvalData.status.slice(1)}
//                 </span>
//                 <span className="ml-4 text-sm text-gray-500">
//                   Submitted on {new Date(approvalData.submittedDate).toLocaleDateString()}
//                 </span>

//                 {approvalData.status === 'approved' && approvalData.approvedBy && approvalData.approvedDate && (
//                   <div className="flex items-center ml-4 mt-2 sm:mt-0">
//                     <Check className="h-4 w-4 text-green-600 mr-1" />
//                     <span className="text-sm text-green-700">
//                       Approved by {approvalData.approvedBy} on {new Date(approvalData.approvedDate).toLocaleDateString()}
//                     </span>
//                   </div>
//                 )}
//               </div>
//             </div>

//             <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
//               {/* Student Information */}
//               <div className="bg-gray-50 p-4 rounded-lg">
//                 <h2 className="text-lg font-semibold mb-4 flex items-center">
//                   <GraduationCap className="h-5 w-5 mr-2 text-blue-600" />
//                   Student Information
//                 </h2>
//                 <div className="space-y-3">
//                   <InfoRow label="Name" value={approvalData.student} />
//                   <InfoRow label="Student ID" value={approvalData.studentDetails.studentId} />
//                   <InfoRow label="Email" value={approvalData.studentDetails.email} />
//                   <InfoRow label="Course" value={approvalData.studentDetails.course} />
//                   <InfoRow label="Year" value={approvalData.studentDetails.year} />
//                   <InfoRow label="Batch" value={approvalData.studentDetails.batch || 'Not specified'} />
//                   <InfoRow label="Phone" value={approvalData.studentDetails.phone} />
//                 </div>
//               </div>

//               {/* Campaign Details */}
//               <div className="bg-gray-50 p-4 rounded-lg">
//                 <h2 className="text-lg font-semibold mb-4 flex items-center">
//                   <Calendar className="h-5 w-5 mr-2 text-blue-600" />
//                   Campaign Details
//                 </h2>
//                 <div className="space-y-3">
//                   <InfoRow label="Amount" value={`₹${approvalData.amount.toLocaleString()}`} />
//                   <InfoRow label="Duration" value={approvalData.campaignDetails.duration} />
//                   <InfoRow label="Start Date" value={approvalData.campaignDetails.startDate} />
//                   <InfoRow label="End Date" value={approvalData.campaignDetails.endDate} />
//                 </div>
//               </div>
//             </div>

//             {/* Campaign Description */}
//             <div className="mb-6">
//               <h2 className="text-lg font-semibold mb-4">Campaign Description</h2>
//               <div className="bg-gray-50 p-4 rounded-lg">
//                 <p className="text-gray-700">{approvalData.campaignDetails.description}</p>
//               </div>
//             </div>

//             {/* Approval Information */}
//             {approvalData.status === 'approved' && approvalData.approvedBy && approvalData.approvedDate && (
//               <div className="mb-6">
//                 <h2 className="text-lg font-semibold mb-4">Approval Information</h2>
//                 <div className="bg-green-50 p-4 rounded-lg border border-green-200">
//                   <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-6">
//                     <div className="flex items-center">
//                       <Check className="h-5 w-5 text-green-600 mr-2" />
//                       <span className="font-medium text-gray-700">Approved by:</span>
//                       <span className="ml-2 text-gray-800">{approvalData.approvedBy}</span>
//                     </div>
//                     <div className="flex items-center">
//                       <Calendar className="h-5 w-5 text-green-600 mr-2" />
//                       <span className="font-medium text-gray-700">Approval Date:</span>
//                       <span className="ml-2 text-gray-800">{new Date(approvalData.approvedDate || '').toLocaleDateString()}</span>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             )}

//             {/* Milestones */}
//             {/* <div className="mb-6">
//               <h2 className="text-lg font-semibold mb-4">Milestones</h2>
//               <div className="bg-gray-50 p-4 rounded-lg">
//                 <ol className="list-decimal list-inside space-y-2">
//                   {approvalData.campaignDetails.milestones.map((milestone, index) => (
//                     <li key={index} className="text-gray-700">{milestone}</li>
//                   ))}
//                 </ol>
//               </div>
//             </div> */}

//             {/* Supporting Documents */}
//             <div className="mb-6">
//               <h2 className="text-lg font-semibold mb-4">Supporting Documents</h2>
//               <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//                 {approvalData.documents.map((doc, index) => (
//                   <div
//                     key={index}
//                     className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
//                   >
//                     <div className="flex items-center">
//                       <FileText className="h-5 w-5 text-blue-600 mr-3" />
//                       <span className="font-medium">{doc}</span>
//                     </div>
//                     <div className="flex space-x-2">
//                       <button
//                         className="p-2 text-gray-600 hover:text-blue-600"
//                         onClick={() => window.open('#', '_blank')}
//                         title="View"
//                       >
//                         <ExternalLink className="h-4 w-4" />
//                       </button>
//                       <button
//                         className="p-2 text-gray-600 hover:text-blue-600"
//                         onClick={() => {
//                           // Create a mock file for download
//                           const mockFileName = doc.replace(/\s+/g, '_').toLowerCase() + '.pdf';
//                           const blob = new Blob(['Mock document content for ' + doc], { type: 'application/pdf' });
//                           const url = URL.createObjectURL(blob);

//                           // Create a temporary link and trigger download
//                           const a = document.createElement('a');
//                           a.href = url;
//                           a.download = mockFileName;
//                           document.body.appendChild(a);
//                           a.click();

//                           // Clean up
//                           setTimeout(() => {
//                             document.body.removeChild(a);
//                             URL.revokeObjectURL(url);
//                           }, 100);

//                           toast.success(`Downloaded ${doc}`);
//                         }}
//                         title="Download"
//                       >
//                         <Download className="h-4 w-4" />
//                       </button>
//                     </div>
//                   </div>
//                 ))}
//               </div>
//             </div>

//             {/* Action Buttons */}
//             {approvalData.status === 'pending' && (
//               <div className="flex justify-end space-x-4 mt-6">
//                 {permissions.manageApprovals ? (
//                   <>
//                     <button
//                       onClick={() => handleApproval('rejected')}
//                       className="px-4 py-2 border border-red-600 text-red-600 rounded-lg hover:bg-red-50 flex items-center"
//                     >
//                       <X className="h-4 w-4 mr-2" />
//                       Reject Request
//                     </button>
//                     <button
//                       onClick={() => handleApproval('approved')}
//                       className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
//                     >
//                       <Check className="h-4 w-4 mr-2" />
//                       Approve Request
//                     </button>
//                   </>
//                 ) : (
//                   <>
//                     <button
//                       disabled
//                       className="px-4 py-2 border border-gray-400 text-gray-400 rounded-lg cursor-not-allowed flex items-center"
//                       title="You don't have permission to reject requests"
//                     >
//                       <X className="h-4 w-4 mr-2" />
//                       Reject Request
//                     </button>
//                     <button
//                       disabled
//                       className="px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed flex items-center"
//                       title="You don't have permission to approve requests"
//                     >
//                       <Check className="h-4 w-4 mr-2" />
//                       Approve Request
//                     </button>
//                   </>
//                 )}
//               </div>
//             )}
//           </div>
//         </div>
//       </div>

//       {/* Rejection Confirmation Dialog */}
//       {showConfirmDialog && actionType === 'rejected' && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
//           <div className="bg-white rounded-lg p-6 w-full max-w-md">
//             <h3 className="text-lg font-semibold mb-4">Confirm Rejection</h3>
//             <p className="text-gray-600 mb-4">
//               Are you sure you want to reject this campaign request?
//             </p>

//             <div className="mb-4">
//               <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-1">
//                 Reason for Rejection <span className="text-red-500">*</span>
//               </label>
//               <textarea
//                 id="rejectionReason"
//                 rows={3}
//                 className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
//                 placeholder="Enter reason for rejection..."
//                 value={rejectionReason}
//                 onChange={(e) => setRejectionReason(e.target.value)}
//                 required
//               />
//               {rejectionReason.trim() === '' && (
//                 <p className="text-sm text-red-500 mt-1">Please provide a reason for rejection</p>
//               )}
//             </div>

//             <div className="flex justify-end space-x-3">
//               <button
//                 className="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50"
//                 onClick={() => {
//                   setShowConfirmDialog(false);
//                   setRejectionReason('');
//                 }}
//               >
//                 Cancel
//               </button>
//               <button
//                 className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
//                 onClick={confirmAction}
//                 disabled={rejectionReason.trim() === ''}
//               >
//                 Reject
//               </button>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Notification Preview Modal */}
//       {showNotificationPreview && approval && (
//         <NotificationPreview
//           type={actionType === 'approved' ? 'approval' : 'rejection'}
//           data={{
//             recipientName: approvalData.student,
//             recipientEmail: approvalData.studentDetails.email || '<EMAIL>',
//             campaignTitle: approvalData.campaign,
//             amount: approvalData.amount,
//             reason: actionType === 'rejected' ? rejectionReason : undefined,
//             institutionName: 'Edu Fund Institution',
//             senderName: user?.name || 'Administrator',
//             date: new Date().toISOString(),
//           }}
//           onClose={() => setShowNotificationPreview(false)}
//           onSend={() => {
//             toast.success("Notification email sent successfully");
//             setShowNotificationPreview(false);
//             setRejectionReason('');
//             navigate('/institution-dashboard');
//           }}
//         />
//       )}
//     </div>
//   );
// }

// // Helper Components
// const InfoRow = ({ label, value }: { label: string; value: string }) => (
//   <div className="flex justify-between">
//     <span className="font-medium text-gray-600">{label}:</span>
//     <span className="text-gray-800">{value}</span>
//   </div>
// );

// // Removed NotificationPreviewModal as it's replaced by the NotificationPreview component

