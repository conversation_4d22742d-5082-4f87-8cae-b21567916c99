export const formatDateTime = (date: string | number | Date): string => {
  if (!date) return '';
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  };
  return new Date(date)
    .toLocaleString('en-GB', options)
    .replace(/\//g, '-')
    .replace(' am', ' AM')
    .replace(' pm', ' PM');
};