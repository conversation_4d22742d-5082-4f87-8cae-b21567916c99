import React from 'react';
import { X, Mail, Download, ExternalLink, AlertTriangle, Check } from 'lucide-react';

interface NotificationPreviewProps {
  type: 'approval' | 'rejection' | 'reminder' | 'notification';
  data: {
    recipientName: string;
    recipientEmail: string;
    campaignTitle?: string;
    amount?: number;
    reason?: string;
    institutionName: string;
    senderName: string;
    date: string;
    pendingCount?: number;
  };
  onClose: () => void;
  onSend: () => void;
}

export function NotificationPreview({ type, data, onClose, onSend }: NotificationPreviewProps) {
  // Get subject based on notification type
  const getSubject = () => {
    switch (type) {
      case 'approval':
        return 'Your Campaign Has Been Approved';
      case 'rejection':
        return 'Campaign Funding Request Rejected';
      case 'reminder':
        return 'Reminder: Pending Campaign Approval Requests';
      case 'notification':
        return 'Campaign Status Update';
      default:
        return 'Notification';
    }
  };
  
  // Get icon based on notification type
  const getIcon = () => {
    switch (type) {
      case 'approval':
        return <Check className="h-8 w-8 text-green-500" />;
      case 'rejection':
        return <AlertTriangle className="h-8 w-8 text-red-500" />;
      case 'reminder':
        return <Mail className="h-8 w-8 text-blue-500" />;
      case 'notification':
        return <Mail className="h-8 w-8 text-blue-500" />;
      default:
        return <Mail className="h-8 w-8 text-blue-500" />;
    }
  };
  
  // Get button color based on notification type
  const getButtonColor = () => {
    switch (type) {
      case 'approval':
        return 'bg-green-600 hover:bg-green-700';
      case 'rejection':
        return 'bg-red-600 hover:bg-red-700';
      case 'reminder':
        return 'bg-blue-600 hover:bg-blue-700';
      case 'notification':
        return 'bg-blue-600 hover:bg-blue-700';
      default:
        return 'bg-blue-600 hover:bg-blue-700';
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center">
            {getIcon()}
            <h2 className="text-xl font-semibold ml-2">
              {type === 'approval' ? 'Approval' : 
               type === 'rejection' ? 'Rejection' : 
               type === 'reminder' ? 'Reminder' : 'Notification'} Preview
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="bg-gray-50 p-4 rounded-md mb-4">
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              <strong>To:</strong> {data.recipientEmail}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Subject:</strong> {getSubject()}
            </p>
            <p className="text-sm text-gray-600">
              <strong>From:</strong> {data.institutionName} &lt;<EMAIL>&gt;
            </p>
          </div>
        </div>

        <div className="bg-white p-4 rounded border mb-4">
          {type === 'approval' && (
            <>
              <p>Dear {data.recipientName},</p>
              <p className="my-4">
                We are pleased to inform you that your campaign funding request for "{data.campaignTitle}" has been approved. 
                The approved amount is ₹{data.amount?.toLocaleString()}.
              </p>

              <div className="my-4">
                <p>Next steps:</p>
                <ul className="list-disc ml-5 mt-2">
                  <li>Review the attached approval document</li>
                  <li>Complete the fund utilization form</li>
                  <li>Submit required documentation within 7 days</li>
                </ul>
              </div>

              <p className="my-4">
                If you have any questions, please contact our support team.
              </p>

              <p>
                Best regards,<br />
                {data.senderName}<br />
                {data.institutionName}
              </p>

              <div className="mt-4 pt-4 border-t text-sm text-gray-500">
                <p>Attachments: approval_document.pdf</p>
              </div>
            </>
          )}

          {type === 'rejection' && (
            <>
              <p>Dear {data.recipientName},</p>
              <p className="my-4">
                We regret to inform you that your campaign funding request for "{data.campaignTitle}" has been rejected.
              </p>

              <div className="my-4 p-3 bg-gray-50 border-l-4 border-red-500">
                <p><strong>Reason for rejection:</strong> {data.reason || 'The campaign does not meet our current funding criteria.'}</p>
              </div>

              <p className="my-4">
                If you would like to discuss this decision or submit a revised campaign, please contact our support team.
              </p>

              <p>
                Best regards,<br />
                {data.senderName}<br />
                {data.institutionName}
              </p>
            </>
          )}

          {type === 'reminder' && (
            <>
              <p>Dear {data.senderName},</p>
              <p className="my-4">
                This is a reminder that you have {data.pendingCount} pending campaign approval requests that require your attention.
              </p>

              <div className="my-4 p-3 bg-yellow-50 border-l-4 border-yellow-500">
                <p>The oldest request has been waiting since {new Date(data.date).toLocaleDateString()}.</p>
              </div>

              <p className="my-4">
                Please review these requests at your earliest convenience by clicking the link below:
              </p>

              <p className="my-4">
                <a href="#" className="text-blue-600 hover:underline flex items-center">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Review Pending Approvals
                </a>
              </p>

              <p className="my-4">
                Thank you for your prompt attention to this matter.
              </p>

              <p>
                Best regards,<br />
                {data.institutionName} System
              </p>
            </>
          )}

          {type === 'notification' && (
            <>
              <p>Dear {data.recipientName},</p>
              <p className="my-4">
                This is to notify you that there has been an update to the campaign "{data.campaignTitle}".
              </p>

              <div className="my-4 p-3 bg-blue-50 border-l-4 border-blue-500">
                <p>The campaign status has been updated on {new Date(data.date).toLocaleDateString()}.</p>
              </div>

              <p className="my-4">
                Please log in to your account to view the details of this update.
              </p>

              <p className="my-4">
                <a href="#" className="text-blue-600 hover:underline flex items-center">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  View Campaign Details
                </a>
              </p>

              <p>
                Best regards,<br />
                {data.institutionName} Team
              </p>
            </>
          )}
        </div>

        <div className="flex justify-between items-center">
          <div>
            <button
              className="flex items-center px-3 py-1.5 text-gray-600 border rounded-md hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-1" />
              Download Preview
            </button>
          </div>
          
          <div className="flex space-x-3">
            <button
              className="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50"
              onClick={onClose}
            >
              Edit
            </button>
            <button
              className={`px-4 py-2 text-white rounded-md ${getButtonColor()}`}
              onClick={onSend}
            >
              Send Notification
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotificationPreview;
