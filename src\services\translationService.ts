import axios from 'axios';

// Supported languages
export type SupportedLanguage = 'en' | 'ta' | 'ur';

// Translation service interface
interface TranslationService {
  translateText(text: string, targetLanguage: SupportedLanguage): string;
  translateTextAsync(text: string, targetLanguage: SupportedLanguage): Promise<string>;
  translateCampaignDetails(campaignDetails: any, targetLanguage: SupportedLanguage): Promise<any>;
}

// Implementation using Google Translate API
class GoogleTranslationService implements TranslationService {
  private cache: Record<string, Record<SupportedLanguage, string>> = {};
  
  constructor() {
    // Initialize cache for better performance
    this.cache = {};
  }
  
  // Synchronous version for UI labels (uses cache only)
  translateText(text: string, targetLanguage: SupportedLanguage): string {
    if (!text || targetLanguage === 'en') {
      return text;
    }
    
    // Check cache first
    if (this.cache[text]?.[targetLanguage]) {
      return this.cache[text][targetLanguage];
    }
    
    // For synchronous calls without cache hit, return original with language marker
    return `[${targetLanguage}] ${text}`;
  }
  
  // Async version for API calls
  async translateTextAsync(text: string, targetLanguage: SupportedLanguage): Promise<string> {
    if (!text || targetLanguage === 'en') {
      return text;
    }
    
    // Check cache first
    if (this.cache[text]?.[targetLanguage]) {
      return this.cache[text][targetLanguage];
    }
    
    try {
      // Google Translate API endpoint
      const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=en&tl=${targetLanguage}&dt=t&q=${encodeURIComponent(text)}`;
      
      const response = await axios.get(url);
      
      // Parse Google Translate response
      let translatedText = '';
      if (response.data && Array.isArray(response.data[0])) {
        response.data[0].forEach((item: any) => {
          if (item[0]) translatedText += item[0];
        });
      }
      
      // Cache the result
      if (!this.cache[text]) {
        this.cache[text] = {} as Record<SupportedLanguage, string>;
      }
      this.cache[text][targetLanguage] = translatedText || text;
      
      return translatedText || text;
    } catch (error) {
      console.error('Google Translate error:', error);
      return text; // Return original text on error
    }
  }
  
  async translateCampaignDetails(campaignDetails: any, targetLanguage: SupportedLanguage): Promise<any> {
    if (!campaignDetails || targetLanguage === 'en') {
      return campaignDetails;
    }
    
    // Create a deep copy to avoid modifying the original object
    const translatedCampaign = JSON.parse(JSON.stringify(campaignDetails));
    
    // Translate text fields
    if (translatedCampaign.campaignTitle) {
      translatedCampaign.campaignTitle = await this.translateTextAsync(
        translatedCampaign.campaignTitle, 
        targetLanguage
      );
    }
    
    if (translatedCampaign.description) {
      translatedCampaign.description = await this.translateTextAsync(
        translatedCampaign.description, 
        targetLanguage
      );
    }
    
    // Translate student details if available
    if (translatedCampaign.studentDetails) {
      if (translatedCampaign.studentDetails.course) {
        translatedCampaign.studentDetails.course = await this.translateTextAsync(
          translatedCampaign.studentDetails.course,
          targetLanguage
        );
      }
      
      if (translatedCampaign.studentDetails.department) {
        translatedCampaign.studentDetails.department = await this.translateTextAsync(
          translatedCampaign.studentDetails.department,
          targetLanguage
        );
      }
    }
    
    // Translate status if available
    if (translatedCampaign.status) {
      translatedCampaign.status = await this.translateTextAsync(
        translatedCampaign.status,
        targetLanguage
      );
    }
    
    // Translate rejection reason if available
    if (translatedCampaign.rejectionReason) {
      translatedCampaign.rejectionReason = await this.translateTextAsync(
        translatedCampaign.rejectionReason,
        targetLanguage
      );
    }
    
    return translatedCampaign;
  }
  
  // Translate all UI labels
  async translateUILabels(targetLanguage: SupportedLanguage): Promise<Record<string, string>> {
    if (targetLanguage === 'en') {
      return this.getDefaultLabels();
    }
    
    const labels = this.getDefaultLabels();
    const translatedLabels: Record<string, string> = {};
    
    // Translate all labels in parallel for better performance
    const translations = await Promise.all(
      Object.entries(labels).map(async ([key, value]) => {
        const translated = await this.translateTextAsync(value, targetLanguage);
        return { key, translated };
      })
    );
    
    // Build the translated labels object
    translations.forEach(({ key, translated }) => {
      translatedLabels[key] = translated;
    });
    
    return translatedLabels;
  }
  
  // Translate support messages
  async translateSupportMessages(messages: any[], targetLanguage: SupportedLanguage): Promise<any[]> {
    if (targetLanguage === 'en') {
      return messages;
    }
    
    const translateMessage = async (msg: any): Promise<any> => {
      const translatedMessage = await this.translateTextAsync(msg.message, targetLanguage);
      const translatedReplies = msg.replies ? await Promise.all(
        msg.replies.map(async (reply: any) => ({
          ...reply,
          message: await this.translateTextAsync(reply.message, targetLanguage)
        }))
      ) : [];
      
      return {
        ...msg,
        message: translatedMessage,
        replies: translatedReplies
      };
    };
    
    return Promise.all(messages.map(translateMessage));
  }
  
  // Default English labels
  private getDefaultLabels(): Record<string, string> {
    return {
      'studentDetails': 'Student Details',
      'batch': 'Batch',
      'campaignDetails': 'Campaign Details',
      'campaignTitle': 'Campaign Title',
      'description': 'Description',
      'targetAmount': 'Target Amount',
      'raisedAmount': 'Raised Amount',
      'status': 'Status',
      'startDate': 'Start Date',
      'endDate': 'End Date',
      'autoCloseDate': 'Auto Close Date',
      'autoCompleteAmount': 'Auto Complete Amount',
      'campaignActive': 'Campaign Active',
      'campaignActiveDesc': 'This campaign is active and running as scheduled.',
      'automaticClosure': 'Automatic Closure Conditions',
      'endDateClosure': 'End Date: Campaign will automatically close on',
      'targetAmountClosure': 'Target Amount: Campaign will automatically complete when',
      'manualTermination': 'Manual Termination: Institution admin or student can terminate the campaign',
      'approvalInfo': 'Approval Information',
      'approvedBy': 'Approved By',
      'approvalDate': 'Approval Date',
      'timeline': 'Timeline',
      'createdAt': 'Created At',
      'lastUpdated': 'Last Updated',
      'wordsOfSupport': 'Words of Support',
      'supportPlaceholder': 'Share words of encouragement as an institution representative...',
      'share': 'Share',
      'noMessages': 'No messages yet. Share words of encouragement!',
      'views': 'views',
      'is': 'is',
      'raised': 'raised',
      'supportStudent': 'Support Student',
      'selectAmount': 'Select Amount',
      'customAmount': 'Or Enter Custom Amount',
      'proceedToPayment': 'Proceed to Payment',
      'backToDashboard': 'Back to Dashboard',
      'aboutCampaign': 'About This Campaign',
      'fundingGoal': 'Funding Goal',
      'raisedSoFar': 'Raised So Far',
      'progress': 'Progress',
      'supporters': 'Supporters'
    };
  }
}

// Create and export a singleton instance
const translationService = new GoogleTranslationService();
export default translationService;