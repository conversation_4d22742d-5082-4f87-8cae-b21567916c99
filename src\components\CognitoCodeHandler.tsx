import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import cognitoService from '../services/cognitoService';
import { toast } from 'react-toastify';
import StatusPage from './StatusPage';

/**
 * CognitoCodeHandler component
 * 
 * This component handles the Cognito authentication code parameter in the URL.
 * It should be used as a wrapper around protected routes that might receive the code parameter.
 */
export function CognitoCodeHandler({ children }: { children: React.ReactNode }) {
  const location = useLocation();
  const navigate = useNavigate();
  const { setUserFromCognito } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessed, setIsProcessed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  useEffect(() => {
    const processCodeIfPresent = async () => {
      try {
        // Parse the URL query parameters to check for code
        const params = new URLSearchParams(location.search);
        const code = params.get('code');

        // If we have a code parameter and haven't processed it yet
        if (code && !isProcessing && !isProcessed) {
          setIsProcessing(true);
          console.log('CognitoCodeHandler: Found authorization code, exchanging for tokens...');

          try {
            // Exchange the code for tokens
            const tokens = await cognitoService.exchangeCodeForTokens(code);

            // Save tokens to localStorage for persistence
            cognitoService.saveTokens(tokens, true);

            // Parse the ID token to get user information
            const idToken = tokens.id_token;
            const userInfo = cognitoService.parseJwt(idToken);

            // Set the user in the auth context
            setUserFromCognito(userInfo);

            // Show success message
            toast.success('Successfully signed in!');

            // Remove the code from the URL by navigating to the same page without query params
            navigate(location.pathname, { replace: true });
            
            setIsProcessed(true);
            setIsLoading(false);
          } catch (error) {
            console.error('CognitoCodeHandler: Error processing authentication code:', error);
            setErrorDetails({ message: 'Authentication failed. Please try again.' });
            setStatus('error');
            setIsLoading(false);
          } finally {
            setIsProcessing(false);
          }
        } else {
          // No code to process or already processed
          setIsLoading(false);
        }
      } catch (error) {
        console.error('CognitoCodeHandler: Error checking for code parameter:', error);
        setIsLoading(false);
      }
    };

    processCodeIfPresent();
  }, [location, navigate, setUserFromCognito, isProcessing, isProcessed]);

  // Show loading state while processing the code
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <p className="ml-3 text-blue-600">Processing authentication...</p>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Authentication Error"
        message={errorDetails?.message || ''}
        actionText="Back to Login"
        backUrl="/institution-login"
        showHeader={false}
      />
    );
  }

  // Render children once processing is complete
  return <>{children}</>;
}

export default CognitoCodeHandler;
