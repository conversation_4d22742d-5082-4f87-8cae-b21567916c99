import { useState, useEffect } from 'react';
import { getGoogleToken, hasGoogleToken, clearGoogleToken } from '../services/googleApi';

interface GoogleAuthState {
  isAuthenticated: boolean;
  token: string | null;
  isLoading: boolean;
}

/**
 * Secure Google authentication hook
 * Provides access to Google authentication state without exposing tokens in localStorage
 */
export const useGoogleAuth = () => {
  const [authState, setAuthState] = useState<GoogleAuthState>({
    isAuthenticated: false,
    token: null,
    isLoading: true
  });

  // Check authentication status
  useEffect(() => {
    const checkAuthStatus = () => {
      const token = getGoogleToken();
      const isAuthenticated = hasGoogleToken();
      
      setAuthState({
        isAuthenticated,
        token,
        isLoading: false
      });
    };

    checkAuthStatus();
    
    // Set up periodic check for token validity
    const interval = setInterval(checkAuthStatus, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Function to manually refresh auth state
  const refreshAuthState = () => {
    const token = getGoogleToken();
    const isAuthenticated = hasGoogleToken();
    
    setAuthState({
      isAuthenticated,
      token,
      isLoading: false
    });
  };

  // Function to clear authentication
  const clearAuth = () => {
    clearGoogleToken();
    setAuthState({
      isAuthenticated: false,
      token: null,
      isLoading: false
    });
  };

  return {
    ...authState,
    refreshAuthState,
    clearAuth
  };
};
