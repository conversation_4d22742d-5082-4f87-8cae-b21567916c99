import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import {
  Search,
  GraduationCap,
  X,
  Share2,
  QrCode,
  FileText,
  CheckCircle,
  XCircle,
  Printer,
  Calendar,
  ChevronLeft,
  ArrowDown,
  ArrowUp,
  School,
  Book,
  Users,
  SearchX,
  TrendingUp,
  Award,
  Clock,
  Filter,
  ChevronUp,
  ChevronDown
} from 'lucide-react';
import { toast } from 'react-toastify';
import { Tab, TabList, TabPanel, Tabs } from "react-tabs";
import "react-tabs/style/react-tabs.css";
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import supporterService from '../services/supporterService';
import paymentService from '../services/paymentService';
import Pagination from '../components/Pagination';
import StatusPage from '../components/StatusPage';
import SupporterHeader from '../components/SupporterHeader';


declare global {
  interface Window {
    Razorpay: any;
  }
}

// Add new interface for campaign details
interface CampaignDetails {
  id: string | number | undefined;
  name: string;
  story: string;
  targetAmount: number;
  raisedAmount: number;
  documents: {
    name: string;
    file: string;
  }[];
  supporters: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'upcoming';
  student: {
    name: string;
    course: string;
    year: string;
    institution: string;
    photo?: string;
  };
  updates: {
    date: string;
    title: string;
    content: string;
  }[];
}


export function SupporterPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, isAuthenticated } = useSupporterAuth();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [donationSortField, setDonationSortField] = useState<string>('donatedAt');
  const [donationSortDirection, setDonationSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedStudent, setSelectedStudent] = useState<(typeof campaigns)[0] | null>(null);
  const [donationAmount, setDonationAmount] = useState('');
  const [activeTab, setActiveTab] = useState<'browse' | 'donations'>('browse');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCampaignDetails, setShowCampaignDetails] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'success' | 'failed' | null>(null);
  const [isCampaignView, setIsCampaignView] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [donations, setDonations] = useState<any[]>([]);
  const [statementStartDate, setStatementStartDate] = useState<string>('');
  const [statementEndDate, setStatementEndDate] = useState<string>('');
  // Donation table sorting
  const [donationPage, setDonationPage] = useState(0);
  const [donationPageSize, setDonationPageSize] = useState(10);
  const [totalDonationPages, setTotalDonationPages] = useState(0);
  const [totalDonationElements, setTotalDonationElements] = useState(0);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);
  const [operationType, setOperationType] = useState<'payment' | 'download'>('payment');

  
useEffect(() => {
  const fetchCampaigns = async () => {
    try {
      const data = await supporterService.getSupporterCampaigns(
        searchQuery || 'a',
        0,
        12,
        sortBy
      );
      // Map API response to the expected structure
      const mappedCampaigns = data.content.map(campaign => ({
        id: campaign.id,
         title: campaign.title,
        name: campaign.studentName,
        institution: campaign.institution,
        course: campaign.course,
        batch: campaign.batch,
        targetAmount: campaign.goalAmount,
        raisedAmount: (campaign.progressPercentage / 100) * campaign.goalAmount,
        campaign: {
          name: campaign.title,
          story: campaign.title, // No story in API, using title as placeholder
          supporters: campaign.supportersCount || 0,
          progress: campaign.progressPercentage || 0
        },
        studentId: campaign.id
      }));
      setCampaigns(mappedCampaigns);
    } catch (error: any) {
      const message = error?.response?.data?.message || 'Failed to fetch campaigns';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  fetchCampaigns();
}, [searchQuery, sortBy]);

 const fetchDonations = async () => {
  try {
    const response = await supporterService.getMyDonations(
      donationPage,
      donationPageSize,
      `${donationSortField},${donationSortDirection}`
    );

    const responseData = response.data || {};
    
    setDonations(Array.isArray(responseData.content) ? responseData.content : []);
    setTotalDonationPages(responseData.totalPages || 0);
    setTotalDonationElements(responseData.totalElements || 0);
  } catch (error: any) {
    const message = error?.response?.data?.message || 'Failed to fetch donations';
    setErrorDetails({ message });
    setStatus('error');
    setDonations([]);
    setTotalDonationPages(0);
    setTotalDonationElements(0);
  }
};


  useEffect(() => {
    if (activeTab !== 'donations') return;
    fetchDonations();
  }, [activeTab, donationPage, donationPageSize, donationSortField, donationSortDirection]);


  // Parse URL query parameters to set the active tab
  const getInitialTab = (): 'browse' | 'donations' => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');

    if (tabParam === 'donations') {
      return 'donations';
    }
    return "browse";
  };

  // Set the active tab based on URL parameters
  useEffect(() => {
    const initialTab = getInitialTab();
    if (initialTab === 'donations' && !isAuthenticated) {
      // Store the current URL to redirect back after login
      sessionStorage.setItem('login_redirect', location.pathname + location.search);
      toast.info('Please sign in to view your donations');
      navigate('/supporter-login');
    } else {
      setActiveTab(initialTab);
    }
  }, [location.search, isAuthenticated, navigate]);



  const loadRazorpay = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const handlePayment = (student: any) => {
    setShowCampaignDetails(false); // Close the details modal first
    setSelectedStudent(student);
    setShowPaymentModal(true);
  };

  const handlePaymentComplete = (success: boolean) => {
    setShowPaymentModal(false);
    setPaymentStatus(success ? 'success' : 'failed');
    setShowReceiptModal(true);
  };

  const initializeRazorpayPayment = async () => {
    try {
      const res = await loadRazorpay();

      if (!res) {
        toast.error('Razorpay SDK failed to load');
        return;
      }
      if (!selectedStudent) {
        toast.error('No campaign selected');
        return;
      }

      // Remove commas, convert to number and ensure it's valid
      const cleanAmount = donationAmount.replace(/,/g, '');
      const amountValue = Number(cleanAmount);
      if (isNaN(amountValue) || amountValue <= 0) {
        toast.error('Please enter a valid donation amount');
        return;
      }

      const order = await paymentService.createOrder(String(selectedStudent.id), amountValue);

      const options = {
        key: paymentService.getRazorpayKey(),
        amount: order.amount, // Amount is already in paise from the backend
        currency: "INR",
        name: "EDU-FUND",
        description: `Supporting ${selectedStudent?.name}'s education`,
        image: "https://img.icons8.com/?size=100&id=51z5mmzrVhk8&format=png&color=000000",
        order_id: order.orderId,
        handler: async function(response: any) {
          try {
            // Try to verify the payment, but continue even if it fails
            try {
              await paymentService.verifyPayment(response);
            } catch (verifyErr) {
              console.warn('Payment verification via webhook failed, but continuing payment flow:', verifyErr);
              // We'll continue with the payment flow even if the webhook fails
            }
            
            // Consider the payment successful based on Razorpay's response
            if (response.razorpay_payment_id) {
              setOperationType('payment');
              setStatus('success');
              handlePaymentComplete(true);
            } else {
              throw new Error('Invalid payment response');
            }
          } catch (err) {
            console.error('Payment processing failed', err);
            setOperationType('payment');
            setStatus('error');
            handlePaymentComplete(false);
          }
        },
        prefill: {
          name: "Supporter Name",
          email: "<EMAIL>",
          contact: "9999999999"
        },
        notes: {
          studentId: selectedStudent?.id ?? '',
          studentName: selectedStudent?.name ?? '',
          institution: selectedStudent?.institution ?? ''
        },
        theme: {
          color: "#2563EB"
        },
        modal: {
          ondismiss: function() {
            setShowPaymentModal(false);
            handlePaymentComplete(false);
          }
        }
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.open();
    } catch (error) {
      console.error('Payment initialization error:', error);
      const message = (error as any)?.response?.data?.message || 'Failed to initialize payment';
      setErrorDetails({ message });
      setOperationType('payment');
      setStatus('error');
      toast.error(message);
    }
  };

  const handleProceedToPayment = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to donate');
      navigate('/supporter-login');
      return;
    }

    // Remove commas for numeric comparison
    const cleanAmount = donationAmount.replace(/,/g, '');
    
    if (!cleanAmount || Number(cleanAmount) <= 0) {
      toast.error('Please enter a valid donation amount');
      return;
    }

    if (Number(cleanAmount) > 500000) {
      toast.error('Maximum donation amount is ₹5,00,000');
      return;
    }

    initializeRazorpayPayment();
  };

  const predefinedAmounts = [1000, 2000, 5000, 10000];

  const renderPaymentModal = () => (
    showPaymentModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]"> {/* Increased z-index */}
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Support {selectedStudent?.name}</h3>
            <button onClick={() => setShowPaymentModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Amount (₹)
              </label>
              <div className="grid grid-cols-2 gap-2">
                {predefinedAmounts.map((amount) => (
                  <button
                    key={amount}
                    onClick={() => setDonationAmount(amount.toString())}
                    className={`p-2 rounded border ${
                      donationAmount === amount.toString()
                        ? 'bg-blue-600 text-white'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    ₹{amount}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Or Enter Custom Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2">₹</span>
                <input
                  type="text"
                  value={donationAmount}
                  onChange={(e) => {
                    // Remove commas and non-numeric characters, prevent negative values
                    const rawValue = e.target.value.replace(/[^0-9]/g, '');
                    
                    // Don't update if empty
                    if (!rawValue) {
                      setDonationAmount('');
                      return;
                    }
                    
                    // Convert to number and format with commas
                    const numValue = parseInt(rawValue, 10);
                    setDonationAmount(numValue.toLocaleString('en-IN'));
                  }}
                  className="pl-8 p-2 border rounded w-full"
                  placeholder="Enter amount"
                  inputMode="numeric"
                />
              </div>
            </div>

            <button
              onClick={handleProceedToPayment}
              className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
            >
              Proceed to Payment
            </button>
          </div>
        </div>
      </div>
    )
  );

  const renderHeroSection = () => (
    <div className="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 px-4 mb-8">
      <div className=" mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          Champion Future Scholars
        </h1>
        <p className="text-xl md:text-2xl mb-8 opacity-90">
          Make education accessible for all
        </p>
      </div>
    </div>
  );

  const renderSearchAndFilters = () => (
    <div className="mb-8 space-y-6">
      <div className="relative  mx-auto">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        <input
          type="text"
          placeholder="Search students by name, institution, course or student ID..."
          className="w-full pl-12 pr-4 py-4 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg shadow-sm"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4">
        {/* Three tabs "All studs, urgent need, near goal" removed */}

        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-500" />
          <select
            className="border rounded-lg px-4 py-2 bg-white"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="mostRecent">Most Recent</option>
            <option value="mostUrgent">Most Urgent</option>
            <option value="mostSupported">Most Supported</option>
            <option value="mostAmount">Highest Amount</option>
          </select>
        </div>
      </div>
    </div>
  );

 const renderStudentCard = (student: typeof campaigns[0]) => {
  const fundedPercentage = Math.round((student.raisedAmount / student.targetAmount) * 100);

  return (
    <div
      key={student.id}
      className="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-transform duration-300 cursor-pointer"
      onClick={() => handleStudentClick(student)}
    >
    <p className="text-lg font-bold text-gray-800 mb-2">{student.title}</p>

      <p className="text-lg font-bold text-gray-600">{student.name}</p>

      <div className="mt-3 space-y-1 text-sm text-gray-700">
        <p className="flex items-center gap-2">
          <School className="w-4 h-4 text-blue-600" />
          {student.institution}
        </p>
        <p className="flex items-center gap-2">
          <Book className="w-4 h-4 text-blue-600" />
          {student.course}
        </p>
        <p className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-blue-600" />
          Batch: {student.batch}
        </p>
      </div>

      <div className="grid grid-cols-3 gap-3 mt-6 text-center">
        <div className="bg-blue-50 py-3 rounded-lg">
          <p className="text-blue-600 font-semibold text-lg">{student.campaign.supporters}</p>
          <p className="text-sm text-gray-600">Supporters</p>
        </div>
        <div className="bg-green-50 py-3 rounded-lg">
          <p className="text-green-600 font-semibold text-lg">{fundedPercentage}%</p>
          <p className="text-sm text-gray-600">Funded</p>
        </div>
        <div className="bg-yellow-50 py-3 rounded-lg">
          <p className="text-yellow-700 font-semibold text-lg">
            ₹{student.targetAmount.toLocaleString("en-IN")}
          </p>
          <p className="text-sm text-gray-600">Target</p>
        </div>
      </div>

      <button
        className="mt-6 w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition flex justify-center items-center gap-2"
        onClick={(e) => {
          e.stopPropagation();
          if (!isAuthenticated) {
            // Store the support URL to redirect back after login
            sessionStorage.setItem('login_redirect', `/support/${student.id}`);
            navigate('/supporter-login');
          } else {
            navigate(`/support/${student.id}`);
          }
        }}
      >
        {isAuthenticated ? 'Support Now' : 'Login to Support'} <TrendingUp className="w-4 h-4" />
      </button>
    </div>
  );
};

  const renderBrowseStudents = () => {
    const filteredStudents = campaigns.filter(student =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.institution.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.course.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (student.studentId ?? '').toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
      <div className="space-y-8">
        {renderSearchAndFilters()}

        {filteredStudents.length === 0 ? (
          <div className="text-center py-12">
            <SearchX className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900">No results found</h3>
            <p className="text-gray-600 mt-2">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredStudents.map(renderStudentCard)}
          </div>
        )}
      </div>
    );
  };

  const renderCampaignStats = () => (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
      <div className="bg-blue-50 p-4 rounded-lg text-center">
        <h4 className="text-3xl font-bold text-blue-600">{campaigns.length}</h4>
        <p className="text-sm text-gray-600">Active Campaigns</p>
      </div>
      <div className="bg-green-50 p-4 rounded-lg text-center">
        <h4 className="text-3xl font-bold text-green-600">₹{campaigns.reduce((acc, student) => acc + student.raisedAmount, 0).toLocaleString()}</h4>
        <p className="text-sm text-gray-600">Total Raised</p>
      </div>
      <div className="bg-purple-50 p-4 rounded-lg text-center">
        <h4 className="text-3xl font-bold text-purple-600">{campaigns.reduce((acc, student) => acc + student.campaign.supporters, 0)}</h4>
        <p className="text-sm text-gray-600">Total Supporters</p>
      </div>
      <div className="bg-orange-50 p-4 rounded-lg text-center">
        <h4 className="text-3xl font-bold text-orange-600">92%</h4>
        <p className="text-sm text-gray-600">Success Rate</p>
      </div>
    </div>
  );

  const handleStudentClick = (student: typeof campaigns[0]) => {
    navigate(`/campaign/${student.id}`);
  };

  const renderCampaignView = () => {
    if (!selectedStudent || !isCampaignView) return null;

    return (
      <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
        {/* Navigation Header */}
        <div className="sticky top-0 bg-white border-b z-10">
          <div className=" mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4 flex items-center justify-between">
              <button
                onClick={() => setIsCampaignView(false)}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <ChevronLeft className="h-5 w-5 mr-2" />
                Back to Browse
              </button>
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowShareModal(true)}
                  className="flex items-center px-4 py-2 rounded-md border hover:bg-gray-50"
                >
                  <Share2 className="h-5 w-5 mr-2" />
                  Share
                </button>
                <button
                  onClick={() => {
                    if (!isAuthenticated) {
                      // Store the support URL to redirect back after login
                      sessionStorage.setItem('login_redirect', `/support/${selectedStudent.id}`);
                      navigate('/supporter-login');
                    } else {
                      navigate(`/support/${selectedStudent.id}`);
                    }
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {isAuthenticated ? 'Support Now' : 'Login to Support'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className=" mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Hero Section */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h1 className="text-3xl font-bold mb-4">{selectedStudent.campaign.name}</h1>
                <div>
                  <p className="font-medium text-gray-900">{selectedStudent.name}</p>
                  <p className="text-gray-600">{selectedStudent.institution}</p>
                  <p className="text-blue-600">{selectedStudent.course}</p>
                  {selectedStudent.batch && (
                    <p className="text-gray-600 flex items-center gap-2 mt-1">
                      <Users className="h-4 w-4" />
                      Batch: {selectedStudent.batch}
                    </p>
                  )}
                </div>
              </div>

              {/* Description Section */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Campaign Description</h2>
                <p className="text-gray-600 whitespace-pre-wrap">
                  {selectedStudent.campaign?.story}
                </p>
              </div>

              {/* Documents Section */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold mb-4">Supporting Documents</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {selectedStudent.campaign?.documents.map((doc, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-gray-500 mr-3" />
                        <span className="font-medium">{doc.name}</span>
                      </div>
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        onClick={() => window.open(doc.file, '_blank')}
                      >
                        View
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Campaign Updates section removed */}
            </div>

            {/* Right Column - Campaign Stats */}
            <div className="lg:col-span-1">
              <div className="sticky top-24">
                <div className="bg-white rounded-xl p-6 shadow-sm space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-600">Raised</span>
                      <span className="font-semibold">₹{selectedStudent.raisedAmount.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${(selectedStudent.raisedAmount / selectedStudent.targetAmount) * 100}%` }}
                      />
                    </div>
                    <div className="flex justify-between mt-2">
                      <span className="text-sm text-gray-500">Goal: ₹{selectedStudent.targetAmount.toLocaleString()}</span>
                      <span className="text-sm text-gray-500">
                        {Math.round((selectedStudent.raisedAmount / selectedStudent.targetAmount) * 100)}% raised
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {selectedStudent.campaign?.supporters || 0}
                      </p>
                      <p className="text-sm text-gray-600">Supporters</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg text-center">
                      <p className="text-2xl font-bold text-blue-600">
                        {selectedStudent.campaign ?
                          Math.max(0, Math.ceil((new Date(selectedStudent.campaign.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
                          : 0}
                      </p>
                      <p className="text-sm text-gray-600">Days Left</p>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      if (!isAuthenticated) {
                        // Store the support URL to redirect back after login
                        sessionStorage.setItem('login_redirect', `/support/${selectedStudent.id}`);
                        navigate('/supporter-login');
                      } else {
                        navigate(`/support/${selectedStudent.id}`);
                      }
                    }}
                    className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {isAuthenticated ? 'Support Campaign' : 'Login to Support'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderShareModal = () => (
    showShareModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Share Campaign</h3>
            <button onClick={() => setShowShareModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <input
                type="text"
                readOnly
                value={`https:/${import.meta.env.VITE_BASE_PATH}.example.com/campaign/${selectedStudent?.id}`}
                className="flex-1 bg-transparent outline-none"
              />
              <button
                onClick={() => {
                  navigator.clipboard.writeText(`https:/${import.meta.env.VITE_BASE_PATH}.example.com/campaign/${selectedStudent?.id}`);
                  toast.success('Link copied to clipboard!');
                }}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                Copy
              </button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <button className="flex items-center justify-center p-3 bg-[#1DA1F2] text-white rounded-lg hover:bg-[#1a8cd8]">
                Share on Twitter
              </button>
              <button className="flex items-center justify-center p-3 bg-[#4267B2] text-white rounded-lg hover:bg-[#365899]">
                Share on Facebook
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  );

  const renderQRModal = () => (
    showQRModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Campaign QR Code</h3>
            <button onClick={() => setShowQRModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="flex flex-col items-center space-y-4">
            <div className="bg-gray-100 p-4 rounded-lg">
              {/* Replace with actual QR code implementation */}
              <div className="w-48 h-48 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
                <QrCode className="w-32 h-32 text-gray-400" />
              </div>
            </div>
            <button
              onClick={() => {
                // Create a mock QR code image for download
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');

                if (ctx) {
                  // Draw a simple mock QR code
                  ctx.fillStyle = 'white';
                  ctx.fillRect(0, 0, 200, 200);
                  ctx.fillStyle = 'black';

                  // Draw a border
                  ctx.fillRect(0, 0, 200, 10);
                  ctx.fillRect(0, 0, 10, 200);
                  ctx.fillRect(190, 0, 10, 200);
                  ctx.fillRect(0, 190, 200, 10);

                  // Draw some random squares to simulate QR code
                  for (let i = 0; i < 10; i++) {
                    const x = 20 + Math.floor(Math.random() * 160);
                    const y = 20 + Math.floor(Math.random() * 160);
                    ctx.fillRect(x, y, 20, 20);
                  }

                  // Add campaign ID text
                  ctx.font = '12px Arial';
                  ctx.fillText(`Campaign ID: ${selectedStudent?.id || 'Unknown'}`, 50, 100);
                }

                // Convert canvas to blob and download
                canvas.toBlob((blob) => {
                  if (blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `campaign_qr_${selectedStudent?.id || 'unknown'}.png`;
                    document.body.appendChild(a);
                    a.click();

                    // Clean up
                    setTimeout(() => {
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    }, 100);
                  }
                });

                toast.success('QR Code downloaded!');
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Download QR Code
            </button>
          </div>
        </div>
      </div>
    )
  );

  // Add redirection effect after successful payment
  useEffect(() => {
    let redirectTimer: NodeJS.Timeout;
    
    if (paymentStatus === 'success' && showReceiptModal) {
      redirectTimer = setTimeout(() => {
        setShowReceiptModal(false);
        navigate('/supporters?tab=donations');
      }, 5000);
    }
    
    return () => {
      if (redirectTimer) clearTimeout(redirectTimer);
    };
  }, [paymentStatus, showReceiptModal, navigate]);

  const renderReceiptModal = () => (
    showReceiptModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[70]">
        <div className="bg-white rounded-lg p-6  w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Payment Receipt</h3>
            <button onClick={() => setShowReceiptModal(false)} className="text-gray-500">
              <X className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-6">
            <div className={`p-4 rounded-lg ${paymentStatus === 'success' ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className="flex items-center">
                {paymentStatus === 'success' ? (
                  <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
                ) : (
                  <XCircle className="h-8 w-8 text-red-500 mr-3" />
                )}
                <div>
                  <h4 className="text-lg font-medium">
                    {paymentStatus === 'success' ? 'Payment Successful!' : 'Payment Failed'}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {paymentStatus === 'success'
                      ? 'Your donation has been processed successfully.'
                      : 'There was an issue processing your payment.'}
                  </p>
                  {paymentStatus === 'success' && (
                    <>
                      <p className="text-sm text-gray-600 mt-1">
                        Transaction ID: pay_Qibx9qP4giUxV3
                      </p>
                      <p className="text-sm text-gray-600 mt-2 italic">
                        You will be redirected in 5 seconds.
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>

            {paymentStatus === 'success' && (
              <>
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transaction ID</span>
                    <span className="font-medium">TXN123456789</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount</span>
                    <span className="font-medium">₹{donationAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date & Time</span>
                    <span className="font-medium">{new Date().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Campaign</span>
                    <span className="font-medium">{selectedStudent?.name}'s Education</span>
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    onClick={() => {
                      // Create a mock receipt PDF for download
                      const receiptContent = `
EDUFUND DONATION RECEIPT
------------------------
Transaction ID: TXN123456789
Date: ${new Date().toLocaleString()}
Amount: ₹${donationAmount}
Donor: Supporter Name
Campaign: ${selectedStudent?.name}'s Education
Status: Successful

Thank you for your generous contribution!
This receipt can be used for tax deduction purposes.
                      `;

                      const blob = new Blob([receiptContent], { type: 'application/pdf' });
                      const url = URL.createObjectURL(blob);

                      // Create a temporary link and trigger download
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `donation_receipt_${new Date().getTime()}.pdf`;
                      document.body.appendChild(a);
                      a.click();

                      // Clean up
                      setTimeout(() => {
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      }, 100);

                      toast.success('Receipt downloaded!');
                    }}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <FileText className="h-5 w-5 mr-2" />
                    Download PDF
                  </button>
                  <button
                    onClick={() => {
                      // Add print logic
                      window.print();
                    }}
                    className="flex items-center text-gray-600 hover:text-gray-800"
                  >
                    <Printer className="h-5 w-5 mr-2" />
                    Print
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    )
  );

  // Donations are sorted by the server
  const getSortedDonations = () => {
    return Array.isArray(donations) ? donations : [];
  };

  const getPaginatedSortedDonations = () => {
    const sorted = getSortedDonations();
    const start = donationPage * donationPageSize;
    return sorted.slice(start, start + donationPageSize);
  };

  // Function to toggle sort direction or change sort field
 const handleDonationSort = (field: string) => {
  if (donationSortField === field) {
    setDonationSortDirection(prev => prev === 'desc' ? 'asc' : 'desc');
  } else {
    setDonationSortField(field);
    setDonationSortDirection('desc');
  }
  setDonationPage(0);
};


  const renderMyDonations = () => (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">My Donations</h2>
        <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
          <div className="flex flex-col">
            <label htmlFor="startDate" className="text-sm font-medium text-gray-700 mb-1">
              From Date
            </label>
            <input
              id="startDate"
              type="date"
              value={statementStartDate}
              onChange={(e) => setStatementStartDate(e.target.value)}
              className="border rounded-lg px-3 py-2"
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="endDate" className="text-sm font-medium text-gray-700 mb-1">
              To Date
            </label>
            <input
              id="endDate"
              type="date"
              value={statementEndDate}
              onChange={(e) => setStatementEndDate(e.target.value)}
              className="border rounded-lg px-3 py-2"
            />
          </div>
          <div className="flex items-end">
            <button
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 whitespace-nowrap"
              onClick={async () => {
                try {
                  const blob = await supporterService.downloadDonationStatement(
                    statementStartDate,
                    statementEndDate
                  );
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `donation_statement_${new Date().toISOString().split('T')[0]}.pdf`;
                  document.body.appendChild(a);
                  a.click();
                  setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }, 100);
                  setOperationType('download');
                  setStatus('success');
                } catch (error) {
                  setOperationType('download');
                  setStatus('error');
                }
              }}
            >
              Download Statement
            </button>
          </div>
        </div>
      </div>

      {/* Donation sorting headers */}
     <div className="bg-white rounded-lg shadow-md p-4 mb-4">
  <div className="flex justify-between items-center">
    {/* Student Column */}
    <div
      className="flex items-center cursor-pointer w-1/3 group hover:text-blue-600"
      onClick={() => handleDonationSort('campaign.student.user.firstName')}
    >
      <span className="font-medium mr-1">Student</span>
      <div className="flex flex-col ml-1">
        <ChevronUp
          className={`h-4 w-4 ${
            donationSortField === 'campaign.student.user.firstName' && donationSortDirection === 'asc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
        <ChevronDown
          className={`h-4 w-4 -mt-1 ${
            donationSortField === 'campaign.student.user.firstName' && donationSortDirection === 'desc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
      </div>
    </div>

    {/* Donation Date Column */}
    <div
      className="flex items-center cursor-pointer w-1/3 justify-center group hover:text-blue-600"
      onClick={() => handleDonationSort('donatedAt')}
    >
      <span className="font-medium mr-1">Donation Date</span>
      <div className="flex flex-col ml-1">
        <ChevronUp
          className={`h-4 w-4 ${
            donationSortField === 'donatedAt' && donationSortDirection === 'asc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
        <ChevronDown
          className={`h-4 w-4 -mt-1 ${
            donationSortField === 'donatedAt' && donationSortDirection === 'desc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
      </div>
    </div>

    {/* Amount Column */}
    <div
      className="flex items-center cursor-pointer w-1/3 justify-end group hover:text-blue-600"
      onClick={() => handleDonationSort('amount')}
    >
      <span className="font-medium mr-1">Amount</span>
      <div className="flex flex-col ml-1">
        <ChevronUp
          className={`h-4 w-4 ${
            donationSortField === 'amount' && donationSortDirection === 'asc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
        <ChevronDown
          className={`h-4 w-4 -mt-1 ${
            donationSortField === 'amount' && donationSortDirection === 'desc'
              ? 'text-blue-600'
              : 'text-gray-400'
          }`}
        />
      </div>
    </div>
  </div>
</div>


      <div className="grid grid-cols-1 gap-4">
        {getPaginatedSortedDonations().map((donation) => (
          <div
            key={donation.id}
            className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => {
              // Navigate to donation details page instead of showing modal
              navigate(`/donation-details/${donation.id}`);
            }}
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">{donation.studentFullName || donation.studentName}</h3>
                <p className="text-gray-600">{donation.campaignTitle}</p>
                <div className="flex items-center mt-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  {new Date(donation.donationDate || donation.date).toLocaleDateString()}
                </div>
              </div>
              <div className="text-right">
                <p className="text-xl font-bold">₹{donation.amount.toLocaleString()}</p>
                <p className="text-sm text-gray-500">Transaction ID: {donation.transactionId}</p>
                {donation.donationStatus && (
                  <span
                    className={`inline-flex mt-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      donation.donationStatus === 'SUCCESS'
                        ? 'bg-green-100 text-green-800'
                        : donation.donationStatus === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {donation.donationStatus.charAt(0) + donation.donationStatus.slice(1).toLowerCase()}
                  </span>
                )}
              </div>
            </div>

            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Campaign Progress</span>
                <span>{donation.progressPercentage || (donation.campaign && donation.campaign.progress) || 0}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${donation.progressPercentage || (donation.campaign && donation.campaign.progress) || 0}%` }}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center gap-4 px-4 py-2 bg-gray-50 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-700 font-medium">Rows per page:</span>
          <select
            value={donationPageSize}
            onChange={e => { setDonationPageSize(parseInt(e.target.value,10)); setDonationPage(0); }}
            className="px-2 py-1 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            {[5,10,20,50].map(size => <option key={size} value={size}>{size}</option>)}
          </select>
        </div>
        <Pagination
          currentPage={donationPage}
          totalPages={totalDonationPages}
          onPageChange={setDonationPage}
          totalItems={totalDonationElements}
          itemsPerPage={donationPageSize}
        />
      </div>
    </div>
  );

  // Donation details are now shown on a separate page

  if (status === 'success') {
    return (
      <StatusPage
        type="success"
        title={operationType === 'payment' ? "Payment Successful!" : "Download Complete!"}
        message={operationType === 'payment' 
          ? `Thank you for your generous donation of ₹${donationAmount}. Your support will make a real difference in ${selectedStudent?.name}'s education.`
          : "Your donation statement has been downloaded successfully."
        }
        actionText={operationType === 'payment' ? "View My Donations" : "Continue"}
        backUrl={operationType === 'payment' ? "/supporters?tab=donations" : undefined}
        onAction={operationType === 'payment' ? undefined : () => setStatus('idle')}
      />
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title={errorDetails?.message ? 'Error' : operationType === 'payment' ? 'Payment Failed' : 'Download Failed'}
        message={errorDetails?.message || (operationType === 'payment'
          ? "We couldn't process your payment. Please try again or contact support if the problem persists."
          : "We couldn't download your statement. Please try again.")}
        actionText="Try Again"
        onAction={() => { setStatus('idle'); setErrorDetails(null); }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <SupporterHeader />
      {renderHeroSection()}
      <div className="container mx-auto px-4 py-8 flex-grow">
        <Tabs
          selectedIndex={activeTab === 'browse' ? 0 : 1}
          onSelect={(index: number) => {
            const newTab = index === 0 ? 'browse' : 'donations';
            if (newTab === 'donations' && !isAuthenticated) {
              // Store the current URL with the desired tab to redirect back after login
              sessionStorage.setItem('login_redirect', `/supporters?tab=${newTab}`);
              toast.info('Please sign in to view your donations');
              navigate('/supporter-login');
              return false;
            }
            setActiveTab(newTab);
            navigate(`/supporters?tab=${newTab}`);
            return true;
          }}
          className="focus:outline-none"
        >
          <TabList className="flex space-x-2 mx-auto mb-8 border-b">
            <Tab
              className="flex-1 py-3 px-6 text-center cursor-pointer transition-colors focus:outline-none"
              selectedClassName="border-b-2 border-blue-600 text-blue-600"
            >
              Browse Students
            </Tab>
            <Tab
              className="flex-1 py-3 px-6 text-center cursor-pointer transition-colors focus:outline-none"
              selectedClassName="border-b-2 border-blue-600 text-blue-600"
            >
              {isAuthenticated ? 'My Donations' : 'Sign in to View Your Donations'}
            </Tab>
          </TabList>

          <TabPanel>
            {/* Breadcrumb for Browse Students tab */}
            <Breadcrumb
              items={getBreadcrumbItems('supporter-browse')}
              className="mb-6"
            />
            
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    <span className="font-medium">Welcome to EduFund!</span> You can browse all student campaigns freely. 
                    {!isAuthenticated ? (
                      <span> Sign in only when you're ready to support a student. Click on any campaign to view details.</span>
                    ) : (
                      <span> Click on any campaign to view details or use the "Support Now" button to make a donation.</span>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {renderSearchAndFilters()}
            {campaigns.filter(student =>
              student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              student.institution.toLowerCase().includes(searchQuery.toLowerCase()) ||
              student.course.toLowerCase().includes(searchQuery.toLowerCase()) ||
              (student.studentId ?? '').toLowerCase().includes(searchQuery.toLowerCase())
            ).length === 0 ? (
              <div className="text-center py-16">
                <SearchX className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900">No results found</h3>
                <p className="text-gray-600 mt-2">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {campaigns.filter(student =>
                  student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  student.institution.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  student.course.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  (student.studentId ?? '').toLowerCase().includes(searchQuery.toLowerCase())
                ).map(renderStudentCard)}
              </div>
            )}
          </TabPanel>
          <TabPanel>
            {/* Breadcrumb for My Donations tab */}
            <Breadcrumb
              items={getBreadcrumbItems('supporter-donations')}
              className="mb-6"
            />
            
            {!isAuthenticated && (
              <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      Please sign in to view your donation history and download statements.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {renderMyDonations()}
          </TabPanel>
        </Tabs>

        {/* Existing modals */}
        {renderPaymentModal()}
        {renderShareModal()}
        {renderQRModal()}
        {renderReceiptModal()}
      </div>
    
    </div>
  );
}

const CampaignView = ({ campaign }: { campaign: CampaignDetails }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm border-b">
        <div className=" mx-auto px-4 py-8 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <Breadcrumb
            items={getBreadcrumbItems('supporter-campaign-details', {
              campaignTitle: campaign.name,
              campaignId: campaign.id
            })}
            className="mb-6"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                  campaign.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>
                <span className="text-gray-500">
                  {new Date(campaign.startDate).toLocaleDateString()} - {new Date(campaign.endDate).toLocaleDateString()}
                </span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{campaign.name}</h1>
              <div className="flex items-center space-x-4 mb-6">
                <img
                  src={campaign.student.photo}
                  alt={campaign.student.name}
                  className="h-12 w-12 rounded-full"
                />
                <div>
                  <p className="font-medium text-gray-900">{campaign.student.name}</p>
                  <p className="text-sm text-gray-500">{campaign.student.course} • {campaign.student.year}</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Raised: ₹{campaign.raisedAmount.toLocaleString()}</span>
                  <span>Goal: ₹{campaign.targetAmount.toLocaleString()}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${(campaign.raisedAmount / campaign.targetAmount) * 100}%` }}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{campaign.supporters}</p>
                  <p className="text-sm text-gray-500">Supporters</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">
                    {Math.ceil((new Date(campaign.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}
                  </p>
                  <p className="text-sm text-gray-500">Days Left</p>
                </div>
              </div>
              <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Support Campaign
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Campaign Content */}
      <div className=" mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Description Section */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Campaign Description</h2>
              <p className="text-gray-600 whitespace-pre-wrap">{campaign.story}</p>
            </div>

            {/* Campaign Updates section removed */}
          </div>

          <div className="lg:col-span-1">
            {/* Documents Section */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Supporting Documents</h2>
              <div className="space-y-3">
                {campaign.documents.map((doc, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium">{doc.name}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Share Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">Share Campaign</h2>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-center space-x-2 p-3 bg-[#1DA1F2] text-white rounded-lg hover:bg-[#1a8cd8]">
                  <Share2 className="h-5 w-5" />
                  <span>Share on Twitter</span>
                </button>
                <button className="w-full flex items-center justify-center space-x-2 p-3 bg-[#4267B2] text-white rounded-lg hover:bg-[#365899]">
                  <Share2 className="h-5 w-5" />
                  <span>Share on Facebook</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};































