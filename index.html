<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="https://img.icons8.com/?size=100&id=51z5mmzrVhk8&format=png&color=000000" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <title>Edu Fund</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
 
<!-- Google Translate Container -->
<!-- <div class="fixed top-2 right-2 z-50 w-full max-w-xs sm:max-w-sm">
  <div class="bg-white border border-gray-200 rounded-lg shadow-md px-3 py-2 flex flex-col sm:flex-row items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-2">
    <div id="google_translate_element" class="text-sm w-full"></div>
  </div>
</div> -->


<script type="text/javascript">
  function googleTranslateElementInit() {
    new google.translate.TranslateElement(
      { pageLanguage: 'en' },
      'google_translate_element'
    );
  }
</script>
<script src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

  </body>
</html>

