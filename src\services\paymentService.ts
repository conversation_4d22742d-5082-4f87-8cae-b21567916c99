import googleApi from './googleApi';
import { toast } from 'react-toastify';

export interface CreateOrderResponse {
  orderId: string;
  amount: number;
  key: string;
}

// Get Razorpay key from environment variables
const getRazorpayKey = (): string => {
  return import.meta.env.VITE_RAZORPAY_KEY_ID || '';
};

// Payment result dialog component
const showPaymentResultDialog = (success: boolean, paymentId: string, donationId: string): Promise<void> => {
  return new Promise((resolve) => {
    const modalContainer = document.createElement('div');
    modalContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white rounded-lg p-6 max-w-md w-full mx-4 text-center';

    const baseUrl = window.location.pathname.includes('${import.meta.env.VITE_BASE_PATH}') ? '${import.meta.env.VITE_BASE_PATH}' : '';

    if (success) {
      let countdown = 5;

      modalContent.innerHTML = `
        <div class="mb-4">
          <svg class="mx-auto h-16 w-16 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h3 class="text-lg font-bold text-gray-900 mb-2">Payment Successful!</h3>
        <p class="text-gray-600 mb-4">Your donation has been processed successfully.</p>
        <p class="text-sm text-gray-500 mb-2">Transaction ID: ${paymentId}</p>
        <p class="text-sm text-blue-500 font-medium mb-6 text-center" id="redirect-msg">
          You will be redirected in ${countdown} seconds...
        </p>
        <button id="view-details-btn" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
          View My Donations
        </button>
      `;

      const interval = setInterval(() => {
        countdown--;
        const msg = document.getElementById("redirect-msg");
        if (msg) msg.textContent = `You will be redirected in ${countdown} seconds...`;
        if (countdown === 0) {
          clearInterval(interval);
          if (document.body.contains(modalContainer)) {
            document.body.removeChild(modalContainer);
          }
          window.location.href = `${baseUrl}/supporters?tab=donations`;
          resolve();
        }
      }, 1000);
    } else {
      modalContent.innerHTML = `
        <div class="mb-4">
          <svg class="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
        <h3 class="text-lg font-bold text-gray-900 mb-2">Payment Failed</h3>
        <p class="text-gray-600 mb-4">We couldn't process your payment.</p>
        <p class="text-sm text-gray-500 mb-6">Please try again later.</p>
        <button id="view-details-btn" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
          Close
        </button>
      `;
    }

    modalContainer.appendChild(modalContent);
    document.body.appendChild(modalContainer);

    const viewDetailsBtn = document.getElementById('view-details-btn');
    if (viewDetailsBtn) {
      viewDetailsBtn.addEventListener('click', () => {
        if (document.body.contains(modalContainer)) {
          document.body.removeChild(modalContainer);
        }
        if (success) {
          window.location.href = `${baseUrl}/supporters?tab=donations`;
        }
        resolve();
      });
    }
  });
};


const paymentService = {
  getRazorpayKey,
  
  createOrder: async (campaignId: string, amount: number): Promise<CreateOrderResponse> => {
    try {
      // Ensure amount is in paise 
      const amountInPaise = amount * 1;
      
      const response = await googleApi.post('/api/supporter/v1/payments/create-order', {
        campaignId,
        amount: amountInPaise,
      });
      
      return {
        ...response.data,
        amount: amountInPaise, // Ensure amount is in paise for Razorpay
      };
    } catch (error) {
      console.error('Error creating Razorpay order:', error);
      throw error;
    }
  },

  verifyPayment: async (paymentData?: any): Promise<any> => {
    try {
      // If no payment data is provided or missing required fields, show failure dialog
      if (!paymentData || !paymentData.razorpay_payment_id || !paymentData.razorpay_order_id || !paymentData.razorpay_signature) {
        console.warn('Incomplete payment data provided for verification');
        
        // Show failure toast and dialog
        toast.error('Payment verification failed');
        await showPaymentResultDialog(false, '', '');
        
        return { 
          success: false, 
          message: 'Payment verification failed: Incomplete data'
        };
      }
      
      // In Razorpay, when all three parameters are present (payment_id, order_id, and signature),
      // it indicates the payment was successful from the client side
      
      // Make an API call to get the donation ID from the backend
      let donationId;
      try {
        // Call the API to verify payment and get the donation ID
        const verifyResponse = await googleApi.post('/api/supporter/v1/payments/verify', {
          razorpay_payment_id: paymentData.razorpay_payment_id,
          razorpay_order_id: paymentData.razorpay_order_id,
          razorpay_signature: paymentData.razorpay_signature
        });
        
        // Extract the donation ID from the response
        if (verifyResponse.data && verifyResponse.data.success && verifyResponse.data.data) {
          donationId = verifyResponse.data.data.id;
        } else {
          // Fallback if API doesn't return a donation ID
          donationId = paymentData.razorpay_payment_id;
        }
      } catch (error) {
        console.error('Error verifying payment with backend:', error);
        // Fallback to using payment ID if API call fails
        donationId = paymentData.razorpay_payment_id;
      }
      
      console.log('Payment verification: Using donation ID:', donationId);
      
      // Show success dialog and redirect to donation details page
      await showPaymentResultDialog(
        true,
        paymentData.razorpay_payment_id,
        donationId
      );
      
      // Return a success response
      return { 
        success: true, 
        message: 'Payment successful',
        paymentId: paymentData.razorpay_payment_id,
        donationId: donationId
      };
    } catch (error) {
      console.error('Error in payment verification process:', error);
      
      // Show failure toast and dialog
      toast.error('Payment process encountered an error');
      await showPaymentResultDialog(false, '', '');
      
      throw error;
    }
  },
};

export default paymentService;