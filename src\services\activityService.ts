import api from './api';
import { Activity, getIdToken } from './institutionService';
import { PaginatedResponse } from '../types';

/**
 * Service for managing activities in the EduFund application
 */
const activityService = {
  /**
   * Get all activities
   * @returns Promise with array of activities
   */
  getAllActivities: async (): Promise<Activity[]> => {
    const response = await api.get('/activities');
    return response.data;
  },

  /**
   * Get activity by ID
   * @param id Activity ID
   * @returns Promise with activity details
   */
  getActivityById: async (id: string): Promise<Activity> => {
    const response = await api.get(`/activities/${id}`);
    return response.data;
  },

  /**
   * Search activities with various filters
   * @param filters Search filters
   * @param page Page number (0-based)
   * @param size Page size
   * @returns Promise with array of activities
   */
  searchActivities: async (
    filters: {
      institutionId?: string;
      action?: string;
      userId?: string;
      startDate?: string;
      endDate?: string;
    },
    page: number = 0,
    size: number = 20,
    sort: string = 'timestamp,desc'
  ): Promise<Activity[]> => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      
      // Add filters to query parameters
      if (filters.institutionId) queryParams.append('institutionId', filters.institutionId);
      if (filters.action) queryParams.append('action', filters.action);
      if (filters.userId) queryParams.append('userId', filters.userId);
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);
      
      // Add pagination parameters
      queryParams.append('page', page.toString());
      queryParams.append('size', size.toString());
      queryParams.append('sort', sort);
      
      console.log(`Searching activities with filters: ${queryParams.toString()}`);
      const response = await api.get(`/activities/search?${queryParams.toString()}`);
      console.log('Activity search response:', response.data);
      
      return response.data;
    } catch (error) {
      console.error('Error searching activities:', error);
      return [];
    }
  },

  /**
   * Get activities for an institution with pagination
   * @param institutionId Institution ID
   * @param page Page number (0-based)
   * @param size Page size
   * @param sort Sort parameter (default: timestamp,desc)
   * @returns Promise with array of activities
   */
  getInstitutionActivities: async (
    institutionId: string,
    page: number = 0,
    size: number = 20,
    sort: string = 'timestamp,desc'
  ): Promise<Activity[]> => {
    try {
      // Always use the hardcoded institution ID for testing
      const hardcodedId = 'ddb0d2a9-f150-4de6-8608-765b91b153e1';
      
      console.log(`Fetching activities for institution ID: ${hardcodedId}, page: ${page}, size: ${size}, sort: ${sort}`);
      const response = await api.get(
        `/activities/institution/${hardcodedId}?page=${page}&size=${size}&sort=${sort}`
      );
      
      console.log('Institution activities response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching institution activities:', error);
      return [];
    }
  },

  /**
   * Get recent activities for an institution
   * @param institutionId Institution ID
   * @param limit Maximum number of activities to return
   * @returns Promise with array of recent activities
   */
  getRecentInstitutionActivities: async (
    institutionId: string,
    limit: number = 10
  ): Promise<Activity[]> => {
    try {
      // Always use the hardcoded institution ID for testing
      const hardcodedId = 'ddb0d2a9-f150-4de6-8608-765b91b153e1';
      
      console.log(`Fetching recent activities for institution ID: ${hardcodedId}, limit: ${limit}`);
      const response = await api.get(`/activities/institution/${hardcodedId}/recent?limit=${limit}`);
      
      console.log('Recent institution activities response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching recent institution activities:', error);
      return [];
    }
  },

  /**
   * Search activities using the admin API
   */
  adminSearchActivities: async (
    params: {
      entityId: string;
      entityType: string;
      action?: string;
      userId: string;
      page?: number;
      size?: number;
      sortBy?: string;
      sortDir?: string;
    }
  ): Promise<PaginatedResponse<Activity>> => {
    try {
      const response = await api.get('/api/admin/v1/activities/search', {
        params,
      });
      return response.data;
    } catch (error) {
      console.error('Error searching activities:', error);
      throw error;
    }
  },

  /**
   * Get activity details by ID using the admin API
   */
  getActivityDetail: async (id: string, auth?: any): Promise<{
    title: string;
    performedBy: string;
    actionType: string;
    timestamp: string;
    detailFields: Array<{ key: string; value: string }>;
    linkToEntity?: string;
    sensitive: boolean;
  }> => {
    try {
      const token = await getIdToken(auth);
      const response = await api.get(`/api/admin/v1/activities/${id}/structured-detail`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data?.data || response.data;
    } catch (error) {
      console.error('Error fetching activity detail:', error);
      throw error;
    }
  }
};

export default activityService;
