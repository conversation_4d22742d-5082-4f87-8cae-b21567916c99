
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ChevronLeft,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  X,
  Download,
  Edit2,
  Trash2,
  XCircle
} from 'lucide-react';
import { toast } from 'react-toastify';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import externalStudentService from '../services/externalStudentService';
import feedbackService from '../services/feedbackService';
import { getGoogleToken, getCurrentUserEmail, getCurrentUserName, isCurrentUserAuthor } from '../services/googleApi';
import StatusPage from '../components/StatusPage';
import { formatDateTime } from '../utils/dateUtils';
import StudentSidebar from '../components/StudentSidebar';
import StudentHeader from '../components/StudentHeader';
import { StudentFooter } from '../components/StudentFooter';
import { useStudentAuth } from '../context/StudentAuthContext';
import LanguageDropdown from '../components/LanguageDropdown';
import translationService, { SupportedLanguage } from '../services/translationService';

interface CampaignDetails {
  id: any;
  title: string;
  description: string;
  targetAmount: number;
  raisedAmount: number;
  status: 'active' | 'pending' | 'rejected' | 'terminated';
  startDate: string;
  endDate: string;
  rejectionReason?: string;
  approvedBy?: string;
  approvedOn?: string;
  studentBatch?: string;
}

// Mock campaign data - this would come from your API in a real app
const mockCampaigns = [
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    title: '2025-4-22-saqib-BCA-2nd Year-4th Semester',
    targetAmount: 30000,
    raisedAmount: 20000,
    startDate: '2024-03-01',
    endDate: '2024-06-30',
    status: 'active',
    description: 'Seeking support for completing my final year tuition fees.',
    rejectionReason: null,
    approvedBy: 'Dr. Zainab Khan',
    approvedOn: '2024-03-05',
    studentBatch: '2022-2025 BCA'
  },
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    title: '2024-6-21-saqib-BCA-2nd Year-3th Semester',
    targetAmount: 20000,
    raisedAmount: 15000,
    startDate: '2024-04-01',
    endDate: '2024-07-31',
    status: 'active',
    description: 'Funding required for AI research project materials.',
    rejectionReason: null,
    approvedBy: 'Prof. Ahmed Siddiqui',
    approvedOn: '2024-04-10',
    studentBatch: '2022-2025 BCA'
  },
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    title: '2025-4-21-saqib-BCA-1nd Year-2th Semester',
    targetAmount: 45000,
    raisedAmount: 0,
    startDate: '2024-05-01',
    endDate: '2024-08-31',
    status: 'rejected',
    description: 'Need a laptop for advanced programming courses.',
    rejectionReason: 'Insufficient documentation provided.',
    studentBatch: '2022-2025 BCA'
  },
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    title: 'Conference Attendance Fund',
    targetAmount: 25000,
    raisedAmount: 0,
    startDate: '2024-06-01',
    endDate: '2024-09-30',
    status: 'pending',
    description: 'Support needed to attend International Tech Conference.',
    rejectionReason: null,
    studentBatch: '2022-2025 BCA'
  }
];


// Mock donation data
const mockDonations = [
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    campaignId: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    supporter: 'Omar Farooq',
    amount: 5000,
    date: '2024-03-15',
    message: 'Keep up the great work! Your project looks promising.',
    isAnonymous: false
  },
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    campaignId: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    supporter: 'Anonymous',
    amount: 3000,
    date: '2024-03-14',
    message: 'All the best for your future!',
    isAnonymous: true
  },
  {
    id: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    campaignId: '0d1a135c-d6c2-4bfc-b95e-2826906e73b5',
    supporter: 'Aisha Rahman',
    amount: 10000,
    date: '2024-03-12',
    message: 'Your dedication is inspiring. Wishing you success!',
    isAnonymous: false
  }
];


// Mock milestones commented out
/*
const mockMilestones = [
  {
    campaignId: 1,
    title: 'First Semester Fees',
    amount: 15000,
    status: 'completed',
    dueDate: '2024-04-30'
  },
  {
    campaignId: 1,
    title: 'Laptop Purchase',
    amount: 20000,
    status: 'in-progress',
    dueDate: '2024-05-30'
  },
  {
    campaignId: 1,
    title: 'Programming Courses',
    amount: 15000,
    status: 'pending',
    dueDate: '2024-06-30'
  },
  {
    campaignId: 2,
    title: 'Research Materials',
    amount: 10000,
    status: 'completed',
    dueDate: '2024-05-15'
  },
  {
    campaignId: 2,
    title: 'Lab Equipment',
    amount: 10000,
    status: 'pending',
    dueDate: '2024-06-15'
  }
];
*/

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
};

export function StudentCampaignDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [donations, setDonations] = useState<any[]>([]);
  // Milestones state commented out
  // const [milestones, setMilestones] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{message?: string} | null>(null);
  const [wordsOfSupport, setWordsOfSupport] = useState('');
  const [replyText, setReplyText] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [supportMessages, setSupportMessages] = useState<Array<{id: string, name: string, email: string, message: string, date: string, replies?: Array<{id: string, name: string, email: string, message: string, date: string}>}>>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<SupportedLanguage>('en');
  const [translatedCampaign, setTranslatedCampaign] = useState<CampaignDetails | null>(null);
  const [uiLabels, setUiLabels] = useState<Record<string, string>>({});
  const [translatedMessages, setTranslatedMessages] = useState<any[]>([]);

  const { logout, isAuthenticated } = useStudentAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [profile, setProfile] = useState<{ name?: string; studentRegId?: string }>({});
  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);
  const handleSignOut = () => {
    logout('${import.meta.env.VITE_BASE_PATH}/student-login');
    toast.success('Successfully signed out');
  };

  useEffect(() => {
    const loadProfile = async () => {
      try {
        const data = await externalStudentService.getStudentProfile();
        setProfile({ name: data.name, studentRegId: data.studentRegId });
      } catch (err) {
        console.error('Error loading profile:', err);
      }
    };
    loadProfile();
  }, []);

  useEffect(() => {
    const fetchCampaign = async () => {
      if (!id) return;
      setIsLoading(true);
      try {
        const data = await externalStudentService.getStudentCampaignDetails(id);
        const mapped: CampaignDetails = {
          id: data.id,
          title: data.title,
          description: data.description,
          targetAmount: data.goalAmount ?? data.targetAmount,
          raisedAmount: data.raisedAmount ?? 0,
          status: (data.status || 'active').toLowerCase() as CampaignDetails['status'],
          startDate: data.startDate,
          endDate: data.endDate,
          rejectionReason: data.rejectionReason,
          approvedBy: data.approvedBy,
          approvedOn: data.approvedOn,
          studentBatch: data.studentBatch
        };

        setCampaign(mapped);

        // Placeholder: donations are not fetched from API yet
        const campaignDonations = mockDonations.filter(d => d.campaignId === id);
        setDonations(campaignDonations);
      } catch (error) {
        console.error('Error fetching campaign:', error);
        const apiMessage = (error as any)?.response?.data?.message;
        const message = apiMessage || (error as Error).message;
        setErrorDetails({ message });
        setStatus('error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCampaign();
  }, [id, navigate]);

  // Translation effect
  useEffect(() => {
    const translateContent = async () => {
      if (!campaign) return;
      
      try {
        const [translatedCampaignData, labels, translatedMsgs] = await Promise.all([
          translationService.translateCampaignDetails({
            campaignTitle: campaign.title,
            description: campaign.description,
            status: campaign.status,
            rejectionReason: campaign.rejectionReason
          }, selectedLanguage),
          translationService.translateUILabels(selectedLanguage),
          translationService.translateSupportMessages(supportMessages, selectedLanguage)
        ]);
        
        setTranslatedCampaign({
          ...campaign,
          title: translatedCampaignData.campaignTitle || campaign.title,
          description: translatedCampaignData.description || campaign.description,
          status: translatedCampaignData.status || campaign.status,
          rejectionReason: translatedCampaignData.rejectionReason || campaign.rejectionReason
        });
        setUiLabels(labels);
        setTranslatedMessages(translatedMsgs);
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedCampaign(campaign);
        setUiLabels({});
        setTranslatedMessages(supportMessages);
      }
    };

    translateContent();
  }, [campaign, selectedLanguage, supportMessages]);

  // Function to fetch feedback using the public API
  const fetchPublicFeedback = async (campaignId: string) => {
    try {
      // Always use public API to fetch feedback without authentication
      const feedbackList = await feedbackService.getPublicFeedback(campaignId);

      const byId: Record<string, any> = {};
      const topLevel: any[] = [];

      feedbackList.forEach((fb: any) => {
        byId[fb.id] = {
          id: fb.id,
          name: fb.authorName ?? fb.name ?? 'Supporter',
          email: fb.authorEmail ?? fb.email ?? '',
          message: fb.content ?? fb.message ?? '',
          date: formatDateTime(fb.createdAt),
          replies: [] as any[],
        };
      });

      feedbackList.forEach((fb: any) => {
        if (fb.parentId && byId[fb.parentId]) {
          byId[fb.parentId].replies.push(byId[fb.id]);
        } else if (!fb.parentId) {
          topLevel.push(byId[fb.id]);
        }
      });

      setSupportMessages(topLevel);
    } catch (err) {
      console.error('Error fetching feedback:', err);
    }
  };

  // Fetch feedback when component mounts or campaign ID changes
  useEffect(() => {
    if (!id) return;
    fetchPublicFeedback(id);
  }, [id]);

  const handleGoBack = () => {
    navigate('/students?tab=dashboard');
  };

  const handlePostReply = async (messageId: string) => {
    if (!id || !replyText.trim()) return;
    const token = getGoogleToken();
    if (!token) {
      toast.error('You must be logged in with Google to post feedback.');
      return;
    }
    try {
      await feedbackService.postFeedback(
        id,
        {
          content: replyText.trim(),
          parentId: messageId,
          authorEmail: getCurrentUserEmail() || '',
          authorName: getCurrentUserName() || ''
        },
        token
      );
      
      // Refresh feedback from public API after posting
      await fetchPublicFeedback(id);
      setReplyText('');
      setReplyingTo(null);
      toast.success('Reply posted!');
    } catch (err) {
      console.error('Error posting reply:', err);
      const message = (err as any)?.response?.data?.message || 'Failed to post reply';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!id) return;
    const token = getGoogleToken();
    if (!token) return;
    try {
      // Find the message to verify ownership before deletion
      const message = supportMessages.find(msg => msg.id === messageId);
      if (!message) {
        toast.error('Message not found');
        return;
      }
      
      // Verify the current user is the author
      const isAuthor = isCurrentUserAuthor(message.authorName || message.name) || 
                      (getCurrentUserEmail() && message.email && 
                       getCurrentUserEmail()?.toLowerCase() === message.email.toLowerCase());
      
      console.log('Delete authorization check:', {
        messageId: message.id,
        name: message.name || message.authorName,
        isAuthor
      });
      
      if (!isAuthor) {
        toast.error('You can only delete your own messages');
        return;
      }
      
      await feedbackService.deleteFeedback(messageId, token);
      
      // Refresh feedback from public API after deleting
      await fetchPublicFeedback(id);
      toast.success('Message deleted');
    } catch (err) {
      console.error('Error deleting message:', err);
      const message = (err as any)?.response?.data?.message || 'Failed to delete message';
      setErrorDetails({ message });
      setStatus('error');
    }
  };

  const handleDeleteReply = async (parentId: string, replyId: string) => {
    if (!id) return;
    const token = getGoogleToken();
    if (!token) return;
    try {
      // Find the parent message and reply to verify ownership before deletion
      const parentMessage = supportMessages.find(msg => msg.id === parentId);
      if (!parentMessage || !parentMessage.replies) {
        toast.error('Parent message not found');
        return;
      }
      
      const reply = parentMessage.replies.find(r => r.id === replyId);
      if (!reply) {
        toast.error('Reply not found');
        return;
      }
      
      // Verify the current user is the author of the reply
      const isAuthor = isCurrentUserAuthor(reply.authorName || reply.name) || 
                      (getCurrentUserEmail() && reply.email && 
                       getCurrentUserEmail()?.toLowerCase() === reply.email.toLowerCase());
      
      console.log('Delete reply authorization check:', {
        replyId: reply.id,
        name: reply.name || reply.authorName,
        isAuthor
      });
      
      if (!isAuthor) {
        toast.error('You can only delete your own replies');
        return;
      }
      
      await feedbackService.deleteFeedback(replyId, token);
      
      // Refresh feedback from public API after deleting
      await fetchPublicFeedback(id);
      toast.success('Reply deleted');
    } catch (err) {
      console.error('Error deleting reply:', err);
      const message = (err as any)?.response?.data?.message || 'Failed to delete reply';
      setErrorDetails({ message });
      setStatus('error');
    }
  };


  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ""}
        actionText="Try Again"
        onAction={() => {
          setStatus('idle');
          setErrorDetails(null);
        }}
      />
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16 pb-16">
        <div className="max-w-4xl mx-auto p-4">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!campaign) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16 pb-16">
        <div className="max-w-4xl mx-auto p-4">
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Campaign Not Found</h2>
            <p className="text-gray-600 mb-6">The campaign you're looking for doesn't exist or has been removed.</p>
            <button
              onClick={handleGoBack}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Go Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StudentHeader />
      <div className="flex pt-16 pb-16">
        <StudentSidebar
          sidebarOpen={sidebarOpen}
          toggleSidebar={toggleSidebar}
          activeTab="dashboard"
          onSignOut={handleSignOut}
          name={profile.name}
          studentRegId={profile.studentRegId}
        />
        <div className="flex-1 md:ml-64 px-4 md:px-8">

        <div className="max-w-4xl mx-auto p-4">
          <div className="flex justify-between items-center mb-4">
            <button
              onClick={handleGoBack}
              className="flex items-center text-blue-600 hover:text-blue-800"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              {uiLabels.backToDashboard || 'Back to Dashboard'}
            </button>
            <LanguageDropdown
              selectedLanguage={selectedLanguage}
              onLanguageChange={setSelectedLanguage}
            />
          </div>

        {/* Breadcrumb */}
        <Breadcrumb
          items={getBreadcrumbItems('student-campaign-details', {
            campaignTitle: translatedCampaign?.title || campaign.title,
            campaignId: id
          })}
          className="mb-4"
        />

        <div className="bg-white rounded-lg shadow overflow-hidden">
          {/* Campaign Header */}
          <div className="p-6 border-b">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center mb-1">
                  <h1 className="text-2xl font-bold text-gray-900 mr-3">{translatedCampaign?.title || campaign.title}</h1>
                  {campaign.studentBatch && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                      Batch: {campaign.studentBatch}
                    </span>
                  )}
                </div>
                <div className="flex flex-wrap items-center text-sm text-gray-600">
                  <div className="flex items-center mr-3 mb-1">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{uiLabels.createdAt || 'Created'}: {formatDate(campaign.startDate)}</span>
                  </div>
                  <span className="mx-2 hidden sm:inline">•</span>
                  <div className="flex items-center mr-3 mb-1">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>{uiLabels.endDate || 'Ends'}: {formatDate(campaign.endDate)}</span>
                  </div>
                  {campaign.status === 'active' && campaign.approvedBy && campaign.approvedOn && (
                    <>
                      <span className="mx-2 hidden sm:inline">•</span>
                      <div className="flex items-center mb-1">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        <span>Approved by institution on {formatDate(campaign.approvedOn)}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <span className={`px-3 py-1 rounded-full text-sm ${
                  campaign.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : campaign.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800'
                    : campaign.status === 'terminated'
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>

                {campaign.status === 'active' && (
                  <button
                    onClick={() => navigate(`/student/campaign/${id}/terminate`)}
                    className="flex items-center bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Terminate Campaign
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Campaign Progress */}
          {campaign.status === 'active' && (
            <div className="p-6 bg-blue-50">
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                <div className="mb-4 md:mb-0">
                  <p className="text-sm text-gray-600 mb-1">{uiLabels.fundingGoal || 'Funding Goal'}</p>
                  <p className="text-2xl font-bold">₹{campaign.targetAmount.toLocaleString()}</p>
                </div>
                <div className="mb-4 md:mb-0">
                  <p className="text-sm text-gray-600 mb-1">{uiLabels.raisedSoFar || 'Raised So Far'}</p>
                  <p className="text-2xl font-bold text-green-600">₹{campaign.raisedAmount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-1">{uiLabels.progress || 'Progress'}</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {((campaign.raisedAmount / campaign.targetAmount) * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full"
                  style={{ width: `${(campaign.raisedAmount / campaign.targetAmount) * 100}%` }}
                />
              </div>
            </div>
          )}

          {/* Rejection/Termination Reason */}
          {(campaign.status === 'rejected' || campaign.status === 'terminated') && campaign.rejectionReason && (
            <div className={`p-6 ${campaign.status === 'terminated' ? 'bg-purple-50' : 'bg-red-50'} border-b`}>
              <div className="flex items-start">
                <AlertTriangle className={`h-5 w-5 ${campaign.status === 'terminated' ? 'text-purple-600' : 'text-red-600'} mr-2 mt-0.5`} />
                <div>
                  <h3 className={`font-medium ${campaign.status === 'terminated' ? 'text-purple-800' : 'text-red-800'}`}>
                    {campaign.status === 'terminated' ? 'Termination Reason' : 'Rejection Reason'}
                  </h3>
                  <p className={`${campaign.status === 'terminated' ? 'text-purple-700' : 'text-red-700'} mt-1`}>
                    {campaign.rejectionReason}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Campaign Description */}
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold mb-4">{uiLabels.aboutCampaign || 'About This Campaign'}</h2>
            <p className="text-gray-700 whitespace-pre-line">{translatedCampaign?.description || campaign.description}</p>
          </div>

          {/* Words of Support Section */}
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold mb-4">{uiLabels.wordsOfSupport || 'Words of Support'}</h2>
            
            <div className="mb-6">
              <textarea
                value={wordsOfSupport}
                onChange={(e) => setWordsOfSupport(e.target.value)}
                placeholder={uiLabels.supportPlaceholder || 'Share updates or respond to supporters...'}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-sm text-gray-500">{wordsOfSupport.length}/500</span>
                <button
                  onClick={async () => {
                    if (!id || !wordsOfSupport.trim()) return;
                    const token = getGoogleToken();
                    if (!token) {
                      toast.error('You must be logged in with Google to post feedback.');
                      return;
                    }
                    try {
                      const token = getGoogleToken();
                      await feedbackService.postFeedback(
                        id,
                        {
                          content: wordsOfSupport.trim(),
                          parentId: null,
                          authorEmail: getCurrentUserEmail() || '',
                          authorName: getCurrentUserName() || ''
                        },
                        token
                      );
                      
                      // Refresh feedback from public API after posting
                      await fetchPublicFeedback(id);
                      setWordsOfSupport('');
                      toast.success('Message shared!');
                    } catch (err) {
                      console.error('Error posting feedback:', err);
                      const message = (err as any)?.response?.data?.message || 'Failed to share message';
                      setErrorDetails({ message });
                      setStatus('error');
                    }
                  }}
                  disabled={!wordsOfSupport.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                >
                  {uiLabels.share || 'Share Update'}
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {translatedMessages.map((message) => {
                // Debug the message data
                console.log('Message data:', {
                  id: message.id,
                  name: message.name,
                  authorName: message.authorName,
                  email: message.email,
                  authorEmail: message.authorEmail,
                  content: message.message
                });
                
                // Enhanced authorization logic using ID token
                const canDeleteBasedOnIdToken = (): boolean => {
                  const currentUserEmail = getCurrentUserEmail();
                  const currentUserName = getCurrentUserName();
                  if (!currentUserEmail) return false;

                  // 1. Direct email match (for comments that have stored email)
                  if (message.email && currentUserEmail.toLowerCase() === message.email.toLowerCase()) {
                    return true;
                  }

                  // 2. Name match using ID token name (for comments that match current user's name)
                  if (currentUserName && message.name &&
                      currentUserName.toLowerCase() === message.name.toLowerCase()) {
                    return true;
                  }

                  // 3. Check authorName field as well
                  if (currentUserName && message.authorName &&
                      currentUserName.toLowerCase() === message.authorName.toLowerCase()) {
                    return true;
                  }

                  return false;
                };

                // Check if the current user is the author of the comment
                const canDeleteMessage = isAuthenticated && (
                  isCurrentUserAuthor(message.authorName || message.name) ||
                  canDeleteBasedOnIdToken()
                );
                
                // Debug the authorization result
                console.log('Delete authorization for message:', {
                  messageId: message.id,
                  name: message.name || message.authorName,
                  canDelete: canDeleteMessage,
                  currentUserEmail: getCurrentUserEmail(),
                  currentUserName: getCurrentUserName()
                });
                
                return (
                <div key={message.id} className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <p className="font-medium text-gray-900">{message.name}</p>
                      {/* Don't display email for privacy reasons */}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">{message.date}</span>
                      <button
                        onClick={() => handleDeleteMessage(message.id)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete message"
                        style={{ display: canDeleteMessage ? 'block' : 'none' }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-700 mb-3">{message.message}</p>
                  
                  <button
                    onClick={() => setReplyingTo(replyingTo === message.id ? null : message.id)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    {replyingTo === message.id ? 'Cancel' : 'Reply'}
                  </button>
                  
                  {replyingTo === message.id && (
                    <div className="mt-3 pl-4 border-l-2 border-blue-200">
                      <textarea
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                        placeholder="Write your reply..."
                        className="w-full p-2 border rounded text-sm"
                        rows={2}
                        maxLength={300}
                      />
                      <div className="flex justify-between mt-2">
                        <span className="text-xs text-gray-500">{replyText.length}/300</span>
                        <button
                          onClick={() => handlePostReply(message.id)}
                          disabled={!replyText.trim()}
                          className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:bg-gray-400"
                        >
                          Reply
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {message.replies && message.replies.length > 0 && (
                    <div className="mt-4 pl-4 border-l-2 border-gray-200 space-y-3">
                      {message.replies.map((reply) => {
                        // Enhanced authorization logic for replies using ID token
                        const canDeleteReplyBasedOnIdToken = (): boolean => {
                          const currentUserEmail = getCurrentUserEmail();
                          if (!currentUserEmail) return false;

                          // 1. Direct email match
                          if (reply.email && currentUserEmail.toLowerCase() === reply.email.toLowerCase()) {
                            return true;
                          }

                          // 2. Check if the reply was posted by the same user using ID token
                          const token = getGoogleToken();
                          if (token) {
                            try {
                              const payload = JSON.parse(atob(token.split('.')[1]));
                              const tokenEmail = payload.email;
                              const tokenName = payload.name;

                              // If current user's token email matches known associations
                              if (tokenEmail === '<EMAIL>') {
                                // Allow deletion of replies from ICamXperts Org (your organization)
                                if (reply.name === 'ICamXperts Org' || reply.name === 'icamxperts org') {
                                  return true;
                                }
                                // Allow deletion if the name matches (case insensitive)
                                if (tokenName && reply.name &&
                                    tokenName.toLowerCase() === reply.name.toLowerCase()) {
                                  return true;
                                }
                              }
                            } catch (e) {
                              console.error('Error parsing ID token for reply authorization:', e);
                            }
                          }

                          return false;
                        };

                        // Check if the current user is the author of the reply
                        const canDeleteReply = isAuthenticated && (
                          isCurrentUserAuthor(reply.authorName || reply.name) ||
                          canDeleteReplyBasedOnIdToken()
                        );
                        return (
                        <div key={reply.id} className="bg-white p-3 rounded">
                          <div className="flex justify-between items-start mb-1">
                            <div>
                              <p className="font-medium text-sm text-blue-600">{reply.name}</p>
                              {/* Don't display email for privacy reasons */}
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-500">{reply.date}</span>
                              <button
                                onClick={() => handleDeleteReply(message.id, reply.id)}
                                className="text-red-600 hover:text-red-800"
                                title="Delete reply"
                                style={{ display: canDeleteReply ? 'block' : 'none' }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <p className="text-gray-700 text-sm">{reply.message}</p>
                        </div>
                        );
                      })}
                    </div>
                  )}
                </div>
                );
              })}
              {translatedMessages.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>{uiLabels.noMessages || 'No messages yet. Share updates with your supporters!'}</p>
                </div>
              )}
            </div>
          </div>

          {/* Approval Information */}
          {campaign.status === 'active' && campaign.approvedBy && campaign.approvedOn && (
            <div className="p-6 border-b bg-green-50">
              <h2 className="text-lg font-semibold mb-4">{uiLabels.approvalInfo || 'Approval Information'}</h2>
              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-6">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <span className="font-medium">{uiLabels.approvedBy || 'Approved by'}:</span>
                  <span className="ml-2">Institution</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-green-600 mr-2" />
                  <span className="font-medium">{uiLabels.approvalDate || 'Approval Date'}:</span>
                  <span className="ml-2">{formatDate(campaign.approvedOn || '')}</span>
                </div>
              </div>
            </div>
          )}

          {/* Milestones section commented out
          {milestones.length > 0 && (
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold mb-4">Milestones</h2>
              <div className="space-y-4">
                {milestones.map((milestone, idx) => (
                  <div key={idx} className="flex items-start">
                    <div className={`p-2 rounded-full mr-4 ${
                      milestone.status === 'completed'
                        ? 'bg-green-100'
                        : milestone.status === 'in-progress'
                        ? 'bg-blue-100'
                        : 'bg-gray-100'
                    }`}>
                      {milestone.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : milestone.status === 'in-progress' ? (
                        <Clock className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Clock className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{milestone.title}</h3>
                          <p className="text-sm text-gray-600">Due: {formatDate(milestone.dueDate)}</p>
                        </div>
                        <span className="text-sm font-medium">₹{milestone.amount.toLocaleString()}</span>
                      </div>
                      <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full ${
                            milestone.status === 'completed'
                              ? 'bg-green-500'
                              : milestone.status === 'in-progress'
                              ? 'bg-blue-500 w-1/2'
                              : 'bg-gray-300 w-0'
                          }`}
                          style={{ width: milestone.status === 'completed' ? '100%' : milestone.status === 'in-progress' ? '50%' : '0%' }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          */}



        </div>
        </div>
      </div>
      <StudentFooter />
    </div>
    </div>
  );
}
