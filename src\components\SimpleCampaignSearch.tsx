import React, { useState, useEffect, useRef } from 'react';
import { Search, Calendar, User, X, RefreshCw } from 'lucide-react';
import { toast } from 'react-toastify';

export interface SimpleSearchFilters {
  searchTerm: string;
  status: string[];
  startDate: string;
  endDate: string;
  studentName: string;
}

interface SimpleCampaignSearchProps {
  onSearch: (filters: SimpleSearchFilters) => void;
  onReset: () => void;
  loading?: boolean;
}

export function SimpleCampaignSearch({
  onSearch,
  onReset,
  loading = false
}: SimpleCampaignSearchProps) {
  // Default filters
  const defaultFilters: SimpleSearchFilters = {
    searchTerm: '',
    status: [],
    startDate: '',
    endDate: '',
    studentName: ''
  };

  // State for filters
  const [filters, setFilters] = useState<SimpleSearchFilters>(defaultFilters);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchDebounceRef = useRef<NodeJS.Timeout | null>(null);

  // Status options
  const statusOptions = [
    { value: 'ACTIVE', label: 'Active' },
    { value: 'PENDING', label: 'Pending Approval' },
    { value: 'COMPLETED', label: 'Ended' },
    { value: 'REJECTED', label: 'Rejected' }
  ];

  // Handle search term change with debounce
  const handleSearchTermChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Update local state immediately for UI feedback
    setFilters(prev => ({ ...prev, searchTerm: value }));
    
    // Clear any existing timeout
    if (searchDebounceRef.current) {
      clearTimeout(searchDebounceRef.current);
    }
    
    // Set a new timeout to debounce the search
    searchDebounceRef.current = setTimeout(() => {
      // Call the search function with updated filters
      onSearch({ ...filters, searchTerm: value });
    }, 500); // 500ms debounce
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    setFilters(prev => {
      const newStatus = prev.status.includes(status)
        ? prev.status.filter(s => s !== status)
        : [...prev.status, status];
      
      return { ...prev, status: newStatus };
    });
  };

  // Handle student name change
  const handleStudentNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFilters(prev => ({ ...prev, studentName: value }));
  };

  // Handle reset filters
  const handleResetFilters = () => {
    setFilters(defaultFilters);
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    onReset();
  };

  // Handle apply filters
  const handleApplyFilters = () => {
    onSearch(filters);
    toast.success('Filters applied successfully');
  };

  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      {/* Main search bar */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        <input
          ref={searchInputRef}
          type="text"
          placeholder="Search campaigns by title, description..."
          className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={filters.searchTerm}
          onChange={handleSearchTermChange}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              onSearch(filters);
            }
          }}
        />
      </div>

      {/* Quick filters */}
      <div className="flex flex-wrap items-center gap-2 mb-4">
        <div className="text-sm text-gray-600 mr-2">Status:</div>
        {statusOptions.map(option => (
          <button
            key={option.value}
            onClick={() => handleStatusChange(option.value)}
            className={`px-3 py-1 text-sm rounded-full ${
              filters.status.includes(option.value)
                ? 'bg-blue-100 text-blue-800 border border-blue-300'
                : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>

      {/* Additional filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {/* Student name filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <User className="h-4 w-4 inline mr-1" />
            Student Name
          </label>
          <input
            type="text"
            value={filters.studentName}
            onChange={handleStudentNameChange}
            className="w-full px-3 py-2 border rounded-md text-sm"
            placeholder="Enter student name"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                onSearch(filters);
              }
            }}
          />
        </div>

        {/* Start date filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <Calendar className="h-4 w-4 inline mr-1" />
            Start Date
          </label>
          <input
            type="date"
            value={filters.startDate}
            onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
            className="w-full px-3 py-2 border rounded-md text-sm"
          />
        </div>

        {/* End date filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <Calendar className="h-4 w-4 inline mr-1" />
            End Date
          </label>
          <input
            type="date"
            value={filters.endDate}
            onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
            className="w-full px-3 py-2 border rounded-md text-sm"
          />
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex justify-end space-x-3">
        <button
          onClick={handleResetFilters}
          className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm"
          disabled={loading}
        >
          Reset
        </button>
        <button
          onClick={handleApplyFilters}
          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
          disabled={loading}
        >
          {loading ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Searching...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Search
            </>
          )}
        </button>
      </div>
    </div>
  );
}

export default SimpleCampaignSearch;
