import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ChevronLeft, Download, Share2, GraduationCap, LogOut, Loader } from 'lucide-react';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import Breadcrumb from '../components/Breadcrumb';
import { getBreadcrumbItems } from '../utils/breadcrumbUtils';
import { toast } from 'react-toastify';
import { donationService, supporterService } from '../services';
import StatusPage from '../components/StatusPage';
import SupporterHeader from '../components/SupporterHeader';


interface DonationDetails {
  id: string;
  campaignTitle: string;
  studentFullName: string;
  donationDate: string;
  transactionId: string;
  amount: number;
  progressPercentage: number;
  donationStatus: string;
}

export function DonationDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, logout } = useSupporterAuth();
  const [donation, setDonation] = useState<DonationDetails | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [status, setStatus] = useState<'idle' | 'error'>('idle');
  const [errorDetails, setErrorDetails] = useState<{ message?: string } | null>(null);

  useEffect(() => {
    const fetchDonationDetails = async () => {
      if (!id) {
        setErrorDetails({ message: 'No donation ID provided' });
        setStatus('error');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // Fetch donation details from API
        const response = await donationService.getDonationById(id);
        
        if (!response.success || !response.data) {
          const apiMessage = response.message || 'Donation not found';
          setErrorDetails({ message: apiMessage });
          setStatus('error');
          setLoading(false);
          return;
        }

        // Set the donation data directly from the API response
        setDonation(response.data);

        setLoading(false);
      } catch (error: any) {
        console.error('Error fetching donation details:', error);
        const apiMessage = error.response?.data?.message || 'Failed to load donation details. Please try again later.';
        setErrorDetails({ message: apiMessage });
        setStatus('error');
        setLoading(false);
      }
    };

    fetchDonationDetails();
  }, [id]);

  const handleSignOut = () => {
    // First clear all authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');

    // Then use the logout function to update the context state
    logout('/supporter-login');

    // Show a success message
    toast.success('Successfully signed out');

    // Force a page reload to clear any remaining state
    setTimeout(() => {
      window.location.href = '${import.meta.env.VITE_BASE_PATH}/supporter-login';
    }, 100);
  };

  const handleDownloadReceipt = async () => {
    if (!donation) return;

    try {
      const blob = await supporterService.downloadReceipt(donation.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `donation_receipt_${donation.transactionId}.pdf`;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);
      toast.success('Receipt downloaded successfully');
    } catch (error: any) {
      console.error('Failed to download receipt:', error);
      const apiMessage = error.response?.data?.message || 'Failed to download receipt';
      setErrorDetails({ message: apiMessage });
      setStatus('error');
    }
  };

  const handleShare = () => {
    navigator.clipboard.writeText(`${window.location.origin}/donation-details/${id}`);
    toast.success('Link copied to clipboard!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading donation details...</p>
        </div>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <StatusPage
        type="error"
        title="Error"
        message={errorDetails?.message || ''}
        actionText="Back to My Donations"
        backUrl="/supporters?tab=donations"
      />
    );
  }

  const progress = donation.progressPercentage || 0;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <SupporterHeader />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 flex-grow">
        {/* Breadcrumb */}
        <Breadcrumb
          items={getBreadcrumbItems('donation-details', {
            donationId: donation.id,
            studentFullName: donation.studentFullName
          })}
          className="mb-6"
        />

        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h1 className="text-2xl font-bold mb-6">Donation Details</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Donation Information */}
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-semibold mb-4">Donation Information</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Student Name</p>
                    <p className="font-medium">{donation.studentFullName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Campaign</p>
                    <p className="font-medium">{donation.campaignTitle}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Amount</p>
                    <p className="font-medium text-green-600">₹{donation.amount.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Date</p>
                    <p className="font-medium">{donation.donationDate}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Transaction ID</p>
                    <p className="font-medium">{donation.transactionId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Progress</p>
                    <p className="font-medium">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {donation.progressPercentage}%
                      </span>
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <p className="font-medium">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          donation.donationStatus === 'SUCCESS'
                            ? 'bg-green-100 text-green-800'
                            : donation.donationStatus === 'PENDING'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {donation.donationStatus.charAt(0) + donation.donationStatus.slice(1).toLowerCase()}
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h2 className="text-lg font-semibold mb-4">Campaign Progress</h2>
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span>Campaign Completion: {donation.progressPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${donation.progressPercentage}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Actions */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">Actions</h2>
              <div className="space-y-4">
                <button
                  onClick={handleDownloadReceipt}
                  className="w-full flex items-center justify-center bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700"
                >
                  <Download className="h-5 w-5 mr-2" />
                  Download Receipt
                </button>

               

                <button
                  onClick={() => {
                    navigate('/supporters?tab=donations');
                  }}
                  className="w-full flex items-center justify-center border border-gray-300 bg-white text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50"
                >
                  Back to My Donations
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
