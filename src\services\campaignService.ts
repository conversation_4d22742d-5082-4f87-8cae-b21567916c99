import api from './api';
import { Campaign, CampaignDetails } from './institutionService';

export interface CampaignCreateRequest {
  title: string;
  description: string;
  goalAmount: number;
  studentId: number | string;
  startDate?: string;
  endDate?: string;
  category?: string;
  status?: string;
}

const campaignService = {
  /**
   * Get all campaigns
   * @returns Promise with array of campaigns
   */
  getAllCampaigns: async (): Promise<Campaign[]> => {
    const response = await api.get('/campaigns');
    return response.data;
  },

  /**
   * Get campaign by ID
   * @param id Campaign ID
   * @returns Promise with campaign details
   */
  getCampaignById: async (id: number | string): Promise<Campaign> => {
    const response = await api.get(`/campaigns/${id}`);
    return response.data;
  },

  /**
   * Create a new campaign
   * @param campaignData Campaign data
   * @returns Promise with created campaign
   */
  createCampaign: async (campaignData: CampaignCreateRequest): Promise<Campaign> => {
    const response = await api.post('/campaigns', campaignData);
    return response.data;
  },

  /**
   * Create a campaign for a specific institution
   * @param institutionId Institution ID
   * @param campaignData Campaign data
   * @returns Promise with created campaign
   */
  createCampaignForInstitution: async (institutionId: number | string, campaignData: CampaignCreateRequest): Promise<Campaign> => {
    const response = await api.post(`/campaigns/institution/${institutionId}`, campaignData);
    return response.data;
  },

  /**
   * Update a campaign
   * @param id Campaign ID
   * @param campaignData Updated campaign data
   * @returns Promise with updated campaign
   */
  updateCampaign: async (id: number | string, campaignData: Partial<Campaign>): Promise<Campaign> => {
    const response = await api.put(`/campaigns/${id}`, campaignData);
    return response.data;
  },

  /**
   * Delete a campaign
   * @param id Campaign ID
   * @returns Promise with deletion result
   */
  deleteCampaign: async (id: number | string): Promise<any> => {
    const response = await api.delete(`/campaigns/${id}`);
    return response.data;
  },

  /**
   * Approve a campaign
   * @param id Campaign ID
   * @returns Promise with approval result
   */
  approveCampaign: async (id: number | string): Promise<any> => {
    const response = await api.put(`/campaigns/${id}/approve`);
    return response.data;
  },

  /**
   * Reject a campaign
   * @param id Campaign ID
   * @param reason Rejection reason
   * @returns Promise with rejection result
   */
  rejectCampaign: async (id: number | string, reason: string): Promise<any> => {
    const response = await api.put(`/campaigns/${id}/reject`, { reason });
    return response.data;
  },

  /**
   * Get campaigns by institution ID
   * @param institutionId Institution ID
   * @returns Promise with array of campaigns
   */
  getCampaignsByInstitution: async (institutionId: number | string): Promise<Campaign[]> => {
    const response = await api.get(`/campaigns/institution/${institutionId}`);
    return response.data;
  },

  /**
   * Get campaigns by student ID
   * @param studentId Student ID
   * @returns Promise with array of campaigns
   */
  getCampaignsByStudent: async (studentId: number | string): Promise<Campaign[]> => {
    const response = await api.get(`/campaigns/student/${studentId}`);
    return response.data;
  },

  /**
   * Get campaign details with student information
   * @param campaignId Campaign ID
   * @param forceRefresh Whether to force a refresh of the data
   * @returns Promise with detailed campaign information
   */
  getCampaignDetails: async (campaignId: number | string, forceRefresh: boolean = false): Promise<CampaignDetails> => {
    // Add cache-busting parameter if forceRefresh is true
    const cacheBuster = forceRefresh ? `?_t=${new Date().getTime()}` : '';
    
    // Get campaign data
    const response = await api.get(`/campaigns/${campaignId}${cacheBuster}`);
    const campaignData = response.data;
    
    // If there's a student ID, get student details
    if (campaignData.studentId) {
      const studentResponse = await api.get(`/students/${campaignData.studentId}${cacheBuster}`);
      const studentData = studentResponse.data;
      
      // Combine campaign and student data
      return {
        ...campaignData,
        studentDetails: {
          email: studentData.email || '',
          course: studentData.course || '',
          year: studentData.year || studentData.academicYear || '',
          phone: studentData.phone || '',
          academicRecord: studentData.academicRecord || studentData.story || '',
          department: studentData.department || '',
          batch: studentData.batch || ''
        }
      };
    }
    
    return campaignData;
  }
};

export default campaignService;
