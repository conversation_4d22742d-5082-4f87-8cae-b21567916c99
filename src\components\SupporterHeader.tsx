import React, { useState, useEffect } from 'react';
import { useSupporterAuth } from '../context/SupporterAuthContext';
import { LogOut, LogIn } from 'lucide-react';
import { toast } from 'react-toastify';
import getInitials from '../utils/getInitials';
import { getBasePathWithSlash } from '../utils/basePath';
import logoService from '../services/logoService';
import { Link, useLocation, useNavigate } from 'react-router-dom';

export function SupporterHeader() {
  const { user, logout, isAuthenticated } = useSupporterAuth();
  const [logoUrl, setLogoUrl] = useState<string>('');
  const initials = user?.name ? getInitials(user.name) : '';
  const navigate = useNavigate();
  const location = useLocation();
  
  // useEffect(() => {
  //   const loadLogo = async () => {
  //     try {
  //       const logo = await logoService.getLogo();
  //       setLogoUrl(logo);
  //     } catch (err) {
  //       console.error('Error loading logo:', err);
  //     }
  //   };
  //   loadLogo();
  // }, []);

  const handleSignOut = () => {
    // Clear all authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('id_token');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('id_token');

    // Sign out from Google
    // if (window.google && window.google.accounts) {
    //   window.google.accounts.id.disableAutoSelect();
    // }

    // Use the logout function to update the context state
     logout(`${getBasePathWithSlash()}supporter-login`);

    // Show success message
    toast.success('Successfully signed out');

    // Force page reload to clear any remaining state
    setTimeout(() => {
      window.location.href = `${getBasePathWithSlash()}supporter-login`;
    }, 100);
  };

  return (
    <nav className="bg-white shadow-sm mb-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16 items-center">
          <div className="flex items-center space-x-3">
            {logoUrl ? (
              <img src={logoUrl} alt="Institution Logo" className="h-10 w-auto" />
            ) : (
              <h1 className="text-xl font-bold text-blue-600">EduFund</h1>
            )}
          </div>
          <div className="flex items-center space-x-3" />
           
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-3">
              <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-semibold">
                {initials}
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">{user.name}</div>
                <div className="text-xs text-gray-500">{user.email}</div>
              </div>
              <button
                onClick={handleSignOut}
                className="flex items-center space-x-1 text-gray-600 hover:text-red-600 transition-colors"
                title="Sign Out"
              >
                <LogOut className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <button
              onClick={() => {
                // Store the current URL to redirect back after login
                sessionStorage.setItem('login_redirect', location.pathname + location.search);
                navigate('/supporter-login');
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <LogIn className="h-4 w-4" />
              <span>{location.pathname.includes('/support/') ? 'Login to Support' : 'Sign In'}</span>
            </button>
          )}
        </div>
      </div>
    </nav>
  );
}

export default SupporterHeader;