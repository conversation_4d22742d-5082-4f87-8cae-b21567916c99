import React from 'react';
import { 
  Calendar, 
  Target, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Info, 
  XCircle,
  HelpCircle
} from 'lucide-react';

interface CampaignClosureInfoProps {
  campaignStatus: string;
  startDate: string;
  endDate: string;
  targetAmount: number;
  raisedAmount: number;
  rejectionReason?: string;
}

export function CampaignClosureInfo({
  campaignStatus,
  startDate,
  endDate,
  targetAmount,
  raisedAmount,
  rejectionReason
}: CampaignClosureInfoProps) {
  // Calculate days remaining
  const today = new Date();
  const end = new Date(endDate);
  const daysRemaining = Math.ceil((end.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  // Calculate funding progress percentage
  const progressPercentage = Math.min(Math.round((raisedAmount / targetAmount) * 100), 100);
  
  // Check if campaign is close to end date
  const isCloseToEndDate = daysRemaining <= 7 && daysRemaining > 0;
  
  // Check if campaign is close to target
  const isCloseToTarget = progressPercentage >= 90 && progressPercentage < 100;
  
  // Check if campaign has reached target
  const hasReachedTarget = raisedAmount >= targetAmount;
  
  // Check if campaign has ended
  const hasEnded = daysRemaining <= 0;
  
  // Determine closure status
  const getClosureStatus = () => {
    if (campaignStatus.toUpperCase() === 'COMPLETED') {
      return {
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        title: 'Campaign Completed',
        description: 'This campaign has been successfully completed.',
        color: 'bg-green-50 border-green-200'
      };
    } else if (campaignStatus.toUpperCase() === 'REJECTED') {
      return {
        icon: <XCircle className="h-5 w-5 text-red-500" />,
        title: 'Campaign Rejected',
        description: rejectionReason || 'This campaign has been rejected.',
        color: 'bg-red-50 border-red-200'
      };
    } else if (campaignStatus.toUpperCase() === 'PAUSED') {
      return {
        icon: <Clock className="h-5 w-5 text-amber-500" />,
        title: 'Campaign Paused',
        description: 'This campaign is currently paused.',
        color: 'bg-amber-50 border-amber-200'
      };
    } else if (hasEnded) {
      return {
        icon: <Calendar className="h-5 w-5 text-red-500" />,
        title: 'End Date Reached',
        description: 'This campaign has reached its end date and will be automatically closed.',
        color: 'bg-red-50 border-red-200'
      };
    } else if (hasReachedTarget) {
      return {
        icon: <Target className="h-5 w-5 text-green-500" />,
        title: 'Target Amount Reached',
        description: 'This campaign has reached its target amount and will be automatically completed.',
        color: 'bg-green-50 border-green-200'
      };
    } else if (isCloseToEndDate) {
      return {
        icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
        title: 'Approaching End Date',
        description: `This campaign will end in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}.`,
        color: 'bg-amber-50 border-amber-200'
      };
    } else if (isCloseToTarget) {
      return {
        icon: <Info className="h-5 w-5 text-blue-500" />,
        title: 'Approaching Target',
        description: `This campaign is at ${progressPercentage}% of its target amount.`,
        color: 'bg-blue-50 border-blue-200'
      };
    } else if (campaignStatus.toUpperCase() === 'PENDING') {
      return {
        icon: <Clock className="h-5 w-5 text-orange-500" />,
        title: 'Campaign Pending',
        description: 'This campaign is pending approval from the institution.',
        color: 'bg-orange-50 border-orange-200'
      };
    } else if (campaignStatus.toUpperCase() === 'ACTIVE') {
      return {
        icon: <Info className="h-5 w-5 text-blue-500" />,
        title: 'Campaign Active',
        description: 'This campaign is active and running as scheduled.',
        color: 'bg-blue-50 border-blue-200'
      };
    } else {
      return {
        icon: <HelpCircle className="h-5 w-5 text-gray-500" />,
        title: `Campaign ${campaignStatus}`,
        description: `This campaign status is ${campaignStatus.toLowerCase()}.`,
        color: 'bg-gray-50 border-gray-200'
      };
    }
  };
  
  const closureStatus = getClosureStatus();
  
  return (
    <div className={`rounded-lg border p-4 ${closureStatus.color}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          {closureStatus.icon}
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium">{closureStatus.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{closureStatus.description}</p>
        </div>
      </div>
      
      {campaignStatus.toUpperCase() === 'ACTIVE' && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-medium flex items-center">
            <HelpCircle className="h-4 w-4 mr-1 text-gray-500" />
            Automatic Closure Conditions
          </h4>
          <ul className="mt-2 space-y-2 text-sm text-gray-600">
            <li className="flex items-start">
              <Calendar className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
              <span>
                <strong>End Date:</strong> Campaign will automatically close on{' '}
                {new Date(endDate).toLocaleDateString()}
                {isCloseToEndDate && (
                  <span className="text-amber-600 ml-1">
                    ({daysRemaining} day{daysRemaining !== 1 ? 's' : ''} remaining)
                  </span>
                )}
              </span>
            </li>
            <li className="flex items-start">
              <Target className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
              <span>
                <strong>Target Amount:</strong> Campaign will automatically complete when{' '}
                {new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(targetAmount)}{' '}
                is raised
                {isCloseToTarget && (
                  <span className="text-blue-600 ml-1">
                    (Currently at {progressPercentage}%)
                  </span>
                )}
              </span>
            </li>
            <li className="flex items-start">
              <AlertTriangle className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
              <span>
                <strong>Manual Termination:</strong> Institution admin or student can terminate the campaign
              </span>
            </li>
          </ul>
        </div>
      )}
    </div>
  );
}

export default CampaignClosureInfo;
