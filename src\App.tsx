import React, { useEffect, lazy, Suspense } from "react";
import { Routes, Route, useLocation, useNavigate } from "react-router-dom";
import { StudentAuthProvider } from "./context/StudentAuthContext";
import { SupporterAuthProvider } from "./context/SupporterAuthContext";
import { Navbar } from "./components/Navbar";
import { Footer } from "./components/Footer";
import { StudentFooter } from "./components/StudentFooter";
import { SupporterFooter } from "./components/SupporterFooter";
import { HomePage } from "./pages/HomePage";

import { SupporterProtectedRoute } from "./components/SupporterProtectedRoute";
import { StudentProtectedRoute } from "./components/StudentProtectedRoute";
import { lazyLoad } from "./utils/lazyLoad";
import { GoogleOAuthProvider } from "@react-oauth/google";
import "react-toastify/dist/ReactToastify.css";

import { SupporterRegistration } from "./components/SupporterRegistration";
import { SupporterRegistrationForm } from "./components/SupporterRegistrationForm";
import { StudentRegistration } from "./components/StudentRegistration";
import { AuthPage } from "./pages/AuthPage";
import { StudentLogin } from "./components/StudentLogin";
import { SupporterLogin } from "./components/SupporterLogin";
import { ToastContainer } from "react-toastify";



// Lazy load student-related components
const StudentPage = lazyLoad(
  () => import("./pages/StudentPage"),
  "Loading student dashboard..."
);
const StudentCampaignDetailsPage = lazyLoad(
  () =>
    import("./pages/StudentCampaignDetailsPage").then((module) => ({
      default: module.StudentCampaignDetailsPage,
    })),
  "Loading campaign details..."
);
const AddUpdatePage = lazyLoad(
  () => import("./pages/AddUpdatePage"),
  "Loading update form..."
);
const RequestCampaignPage = lazyLoad(
  () => import("./pages/RequestCampaignPage"),
  "Loading campaign request form..."
);
const StudentCampaignTerminationPage = lazyLoad(
  () => import("./pages/StudentCampaignTerminationPage"),
  "Loading termination page..."
);

// Lazy load supporter-related components
const SupporterPage = lazyLoad(
  () =>
    import("./pages/SupporterPage").then((module) => ({
      default: module.SupporterPage,
    })),
  "Loading supporter page..."
);
const CampaignDetailsPage = lazyLoad(
  () =>
    import("./pages/CampaignDetailsPage").then((module) => ({
      default: module.CampaignDetailsPage,
    })),
  "Loading campaign details..."
);
const DonationDetailsPage = lazyLoad(
  () =>
    import("./pages/DonationDetailsPage").then((module) => ({
      default: module.DonationDetailsPage,
    })),
  "Loading donation details..."
);
const SupportStudentPage = lazyLoad(
  () => import("./pages/SupportStudentPage"),
  "Loading support form..."
);



function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const isAuthPage =
    location.pathname.includes("/auth") || location.pathname.includes("login");
  const isCampaignPage = location.pathname.includes("/campaign/");
  const isDonationDetailsPage =
    location.pathname.includes("/donation-details/");
  const isStudentRoute =
    location.pathname.startsWith("/student") ||
    location.pathname.startsWith("/students");
  const isSupporterRoute =
    location.pathname.startsWith("/supporter") ||
    location.pathname.startsWith("/supporters") ||
    location.pathname.startsWith("/support/");
  const hideNavbar =
    isAuthPage ||
    isCampaignPage ||
    isDonationDetailsPage ||
    isSupporterRoute;
  
  // Check if current page is the homepage
  const isHomePage = location.pathname === "/" || location.pathname === "${import.meta.env.VITE_BASE_PATH}/" || location.pathname === "${import.meta.env.VITE_BASE_PATH}";

  // Check for direct logout flag and redirect if needed
  useEffect(() => {
    // If we're on the /auth page and have the direct_logout flag
    if (
      location.pathname === "/auth" &&
      sessionStorage.getItem("direct_logout") === "true"
    ) {
      console.log("Detected direct logout flag, redirecting to home page");
      // Clear the flag
      sessionStorage.removeItem("direct_logout");
      // Redirect to home page
      navigate("/");
    }
  }, [location.pathname, navigate]);

  // Get Google client ID from environment variables
  const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
  console.log("Using Google Client ID:", googleClientId);

  let FooterComponent: React.ComponentType<{ className?: string }> = Footer;
  if (isStudentRoute) {
    FooterComponent = StudentFooter;
  } else if (isSupporterRoute) {
    FooterComponent = SupporterFooter;
  }

  return (
    <GoogleOAuthProvider clientId={googleClientId}>
      <StudentAuthProvider>
        <SupporterAuthProvider>
          <div className="min-h-screen flex flex-col bg-gray-50">
            {!hideNavbar && (
              <Navbar 
                showSignInButton={isHomePage} // Only show Sign In on homepage
                displaySignOutButton={false} // Don't show Sign Out by default
              />
            )}
            <main className={`flex-grow ${!hideNavbar ? "" : "flex flex-col"}`}>
                  <Routes>
                    {/* Common routes that don't need authentication */}
                    <Route path="/" element={<HomePage />} />
                    <Route path="/auth" element={<AuthPage />} />
                 

                    <Route
                      path="/supporter-registration"
                      element={<SupporterRegistration />}
                    />
                    <Route
                      path="/supporter-registration-form"
                      element={<SupporterRegistrationForm />}
                    />
                    <Route
                      path="/student-registration"
                      element={<StudentRegistration />}
                    />


                    {/* Student routes with their own auth provider */}
                    <Route
                      path="/students"
                      element={
                        <StudentProtectedRoute>
                          <StudentPage />
                        </StudentProtectedRoute>
                      }
                    />
                    <Route
                      path="/student-login"
                      element={<StudentLogin />}
                    />
                    <Route
                      path="/student/campaign/:id"
                      element={
                        <StudentProtectedRoute>
                          <StudentCampaignDetailsPage />
                        </StudentProtectedRoute>
                      }
                    />
                    <Route
                      path="/student/campaign/:id/terminate"
                      element={
                        <StudentProtectedRoute>
                          <StudentCampaignTerminationPage />
                        </StudentProtectedRoute>
                      }
                    />
                    <Route
                      path="/student/add-update"
                      element={
                        <StudentProtectedRoute>
                          <AddUpdatePage />
                        </StudentProtectedRoute>
                      }
                    />
                    <Route
                      path="/student/request-campaign"
                      element={
                        <StudentProtectedRoute>
                          <RequestCampaignPage />
                        </StudentProtectedRoute>
                      }
                    />

                    {/* Supporter routes with their own auth provider */}
                    <Route path="/supporters" element={<SupporterPage />} />
                    <Route
                      path="/supporter-login"
                      element={<SupporterLogin />}
                    />
                    <Route
                      path="/campaign/:id"
                      element={<CampaignDetailsPage />}
                    />
                    <Route
                      path="/support/:studentId"
                      element={<SupportStudentPage />}
                    />
                    <Route
                      path="/donation-details/:id"
                      element={
                        <SupporterProtectedRoute>
                          <DonationDetailsPage />
                        </SupporterProtectedRoute>
                      }
                    />


                  </Routes>
                </main>
                {(!hideNavbar || isSupporterRoute || isCampaignPage || isDonationDetailsPage) && <FooterComponent />}
                 <ToastContainer position="top-right" autoClose={3000} />
              </div>
          </SupporterAuthProvider>
        </StudentAuthProvider>
      </GoogleOAuthProvider>
  );
}

// Export the App component directly
export function AppWrapper() {
  return <App />;
}

export default AppWrapper;




