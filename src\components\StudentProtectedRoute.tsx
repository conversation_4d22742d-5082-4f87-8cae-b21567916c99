import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useStudentAuth } from '../context/StudentAuthContext';
import { hasGoogleToken } from '../services/googleApi';
import { toast } from 'react-toastify';
import LoadingSpinner from './LoadingSpinner';

interface StudentProtectedRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
}

/**
 * A route component that protects routes requiring student authentication
 * Redirects to the student login page if the user is not authenticated
 */
export function StudentProtectedRoute({
  children,
  redirectPath = '/student-login'
}: StudentProtectedRouteProps) {
  const location = useLocation();
  const { isAuthenticated, user, loading } = useStudentAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        // Wait for the auth context to finish loading
        if (!loading) {
          // Check for Google auth or other specific student registrations
          const email = localStorage.getItem('student_email') || sessionStorage.getItem('student_email');
          const hasGoogleAuth = Object.keys(localStorage).some(key =>
            key.startsWith('student_registered_') ||
            key.includes('google') ||
            key.includes('oauth')
          );

          console.log('StudentProtectedRoute - Authentication check:', {
            isAuthenticated,
            hasUser: !!user,
            pathname: location.pathname,
            hasGoogleAuth,
            email,
            localStorage: {
              token: localStorage.getItem('token') ? 'exists' : 'none',
              user: localStorage.getItem('user') ? 'exists' : 'none',
              studentKeys: Object.keys(localStorage).filter(key => key.includes('student'))
            },
            sessionStorage: {
              token: sessionStorage.getItem('token') ? 'exists' : 'none',
              user: sessionStorage.getItem('user') ? 'exists' : 'none'
            }
          });

          setIsChecking(false);
        }
      } catch (error) {
        console.error('Error in student authentication check:', error);
        setIsChecking(false);
      }
    };

    checkAuthentication();
  }, [loading, isAuthenticated, user, location]);

  // Show loading state
  if (loading || isChecking) {
    return <LoadingSpinner message="Checking authentication..." />;
  }

  // Check for secure Google authentication and localStorage fallback
  const email = localStorage.getItem('student_email');
  const hasSecureGoogleAuth = hasGoogleToken(); // Secure in-memory token check
  const hasLocalStorageAuth = Object.keys(localStorage).some(key =>
    key.startsWith('student_registered_') ||
    key.includes('google_auth')
  );

  // Check if we have a valid session (either secure Google auth or localStorage fallback)
  const checkAuthSession = () => {
    // Priority 1: Secure Google token in memory
    if (hasSecureGoogleAuth) {
      console.log('Valid secure Google authentication found');
      return true;
    }

    // Priority 2: Regular auth context
    if (isAuthenticated) {
      console.log('Valid auth context authentication found');
      return true;
    }

    // Priority 3: localStorage fallback (for existing sessions)
    if (email && hasLocalStorageAuth) {
      console.log('Valid localStorage authentication found');
      return true;
    }

    // If we have email but no valid session, clear stale data
    if (email && !hasLocalStorageAuth && !hasSecureGoogleAuth) {
      console.log('Found student email but no valid session, clearing stale data');
      localStorage.removeItem('student_email');
      localStorage.removeItem('google_auth_picture');
      localStorage.removeItem('google_auth_name');

      // Clear any other Google auth-related items
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('student_registered_') ||
            key.includes('google') ||
            key.includes('oauth') ||
            key.includes('student')) {
          localStorage.removeItem(key);
        }
      });
    }

    return false;
  };

  const isValidSession = checkAuthSession();

  // If not authenticated, redirect to login
  if (!isValidSession) {
    console.log('Not authenticated as student, redirecting to', redirectPath, {
      isAuthenticated,
      hasSecureGoogleAuth,
      hasLocalStorageAuth,
      email,
      studentKeys: Object.keys(localStorage).filter(key => key.includes('student'))
    });

    // Show a toast message
    toast.info('Please sign in to access this page');

    // Redirect to login page with the current location as the "from" state
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If authenticated, render the protected content
  console.log('User is authenticated as student, rendering protected content');
  return <>{children}</>;
}

export default StudentProtectedRoute;
