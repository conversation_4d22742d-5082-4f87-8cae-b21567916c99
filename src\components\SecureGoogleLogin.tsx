import React, { useState } from 'react';
import { GoogleLogin as ReactGoogleLogin } from '@react-oauth/google';
import { FcGoogle } from 'react-icons/fc';

interface SecureGoogleLoginProps {
  buttonText?: string;
  onSuccess: (response: any) => void;
  onError: () => void;
  customClass?: string;
  iconClass?: string;
  textClass?: string;
}

export function SecureGoogleLogin({
  buttonText = "Continue with Google",
  onSuccess,
  onError,
  customClass,
  iconClass,
  textClass
}: SecureGoogleLoginProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showFallback, setShowFallback] = useState(false);

  // Enhanced error handler with fallback
  const handleError = (error: any) => {
    console.error('Google login error details:', error);
    setIsLoading(false);
    
    // Check if it's a FedCM or origin error
    if (error?.message?.includes('FedCM') || 
        error?.message?.includes('origin') ||
        error?.message?.includes('NetworkError')) {
      console.log('Detected FedCM/Origin issue, showing fallback');
      setShowFallback(true);
    } else {
      onError();
    }
  };

  // Enhanced success handler
  const handleSuccess = (response: any) => {
    setIsLoading(false);
    onSuccess(response);
  };

  // Fallback manual login
  const handleFallbackLogin = () => {
    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
    const redirectUri = encodeURIComponent(window.location.origin + '/auth/google/callback');
    const scope = encodeURIComponent('openid email profile');
    
    const googleAuthUrl = `https://accounts.google.com/oauth/authorize?` +
      `client_id=${clientId}&` +
      `redirect_uri=${redirectUri}&` +
      `scope=${scope}&` +
      `response_type=code&` +
      `access_type=offline&` +
      `prompt=consent`;
    
    window.location.href = googleAuthUrl;
  };

  // Use custom classes if provided, otherwise use default classes
  const buttonClassName = customClass || "w-full flex items-center justify-center gap-2 bg-white text-gray-700 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors";
  const googleIconClassName = iconClass || "w-5 h-5";
  const textClassName = textClass || "";

  // Show fallback button if FedCM/Origin issues detected
  if (showFallback) {
    return (
      <div className="space-y-3">
        <button
          onClick={handleFallbackLogin}
          disabled={isLoading}
          className={buttonClassName}
        >
          <FcGoogle className={googleIconClassName} />
          <span className={textClassName}>
            {isLoading ? 'Signing in...' : buttonText}
          </span>
        </button>
        <p className="text-xs text-gray-500 text-center">
          Using secure Google authentication
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <ReactGoogleLogin
        onSuccess={handleSuccess}
        onError={handleError}
        useOneTap={false} // Disable OneTap to avoid FedCM issues
        theme="filled_blue"
        text="continue_with"
        shape="rectangular"
        width="100%"
        type="standard"
        context="signin"
        auto_select={false}
        cancel_on_tap_outside={false}
        itp_support={true} // Enable Intelligent Tracking Prevention support
      />
      
      {/* Fallback option */}
      <button
        onClick={() => setShowFallback(true)}
        className="w-full text-xs text-blue-600 hover:text-blue-800 underline"
      >
        Having trouble? Try alternative sign-in
      </button>
    </div>
  );
}
