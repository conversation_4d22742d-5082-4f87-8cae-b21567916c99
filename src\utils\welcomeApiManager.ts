/**
 * Utility to manage welcome API calls and prevent repetitive last_login logs
 */

interface WelcomeApiState {
  lastCallTime: number;
  userId: string;
  sessionCalled: boolean;
}

const WELCOME_API_COOLDOWN = 5 * 60 * 1000; // 5 minutes in milliseconds
const STORAGE_KEY = 'welcome_api_state';
const SESSION_KEY = 'welcome_session_called';

export class WelcomeApiManager {
  private static getStoredState(): WelcomeApiState | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  private static setStoredState(state: WelcomeApiState): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to store welcome API state:', error);
    }
  }

  private static getSessionState(): boolean {
    return sessionStorage.getItem(SESSION_KEY) === 'true';
  }

  private static setSessionState(called: boolean): void {
    sessionStorage.setItem(SESSION_KEY, called.toString());
  }

  /**
   * Check if welcome API should be called
   * @param userId Current user ID
   * @returns true if API should be called, false otherwise
   */
  static shouldCallWelcomeApi(userId: string): boolean {
    const currentTime = Date.now();
    const storedState = this.getStoredState();
    const sessionCalled = this.getSessionState();

    // If already called in this session for the same user, skip
    if (sessionCalled && storedState?.userId === userId) {
      console.log('Welcome API already called in this session for current user');
      return false;
    }

    // If called recently for the same user, skip
    if (storedState && 
        storedState.userId === userId && 
        (currentTime - storedState.lastCallTime) < WELCOME_API_COOLDOWN) {
      console.log('Welcome API called recently for current user, skipping to prevent duplicate logs');
      return false;
    }

    return true;
  }

  /**
   * Mark that welcome API has been called successfully
   * @param userId Current user ID
   */
  static markWelcomeApiCalled(userId: string): void {
    const currentTime = Date.now();
    
    // Update persistent storage
    this.setStoredState({
      lastCallTime: currentTime,
      userId,
      sessionCalled: true
    });

    // Update session storage
    this.setSessionState(true);
    
    console.log('Welcome API call recorded for user:', userId);
  }

  /**
   * Clear all welcome API tracking (call on logout)
   */
  static clearWelcomeApiState(): void {
    localStorage.removeItem(STORAGE_KEY);
    sessionStorage.removeItem(SESSION_KEY);
    console.log('Welcome API state cleared');
  }

  /**
   * Get debug information about current state
   */
  static getDebugInfo(): any {
    const storedState = this.getStoredState();
    const sessionCalled = this.getSessionState();
    const currentTime = Date.now();
    
    return {
      storedState,
      sessionCalled,
      currentTime,
      timeSinceLastCall: storedState ? currentTime - storedState.lastCallTime : null,
      cooldownRemaining: storedState ? Math.max(0, WELCOME_API_COOLDOWN - (currentTime - storedState.lastCallTime)) : 0
    };
  }
}

export default WelcomeApiManager;